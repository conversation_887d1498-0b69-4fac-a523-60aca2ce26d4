<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/db.php';

use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Client;

$conf = new RdKafka\Conf();

// Set a rebalance callback to log partition assignments (optional)
$conf->setRebalanceCb(function (RdKafka\KafkaConsumer $kafka, $err, array $partitions = null) {
    switch ($err) {
        case RD_KAFKA_RESP_ERR__ASSIGN_PARTITIONS:
            echo "Assign: ";
            var_dump($partitions);
            $kafka->assign($partitions);
            break;

         case RD_KAFKA_RESP_ERR__REVOKE_PARTITIONS:
             echo "Revoke: ";
             var_dump($partitions);
             $kafka->assign(NULL);
             break;

         default:
            throw new \Exception($err);
    }
});

// Configure the group.id. All consumer with the same group.id will consume
// different partitions.
$conf->set('group.id', 'myConsumerGroup');

// Initial list of Kafka brokers
$conf->set('metadata.broker.list', '127.0.0.1');

// Set where to start consuming messages when there is no initial offset in
// offset store or the desired offset is out of range.
// 'earliest': start from the beginning
$conf->set('auto.offset.reset', 'earliest');

$consumer = new RdKafka\KafkaConsumer($conf);

// Subscribe to topic 'test'
$consumer->subscribe(['test']);

echo "Waiting for partition assignment... (make take some time when\n";
echo "quickly re-joining the group after leaving it.)\n";

while (true) {
    $message = $consumer->consume(1200*1000);
    switch ($message->err) {
        case RD_KAFKA_RESP_ERR_NO_ERROR:
            echo date('H:i:s')." ".($message->payload) ."\n";
            echo ' [x] Received ', "\n";
            $tokenfile = fopen("/home/<USER>/code/talkzuri/token.txt", "r");
            $token = fgets($tokenfile);
            echo 'token: ' . $token;
            fclose($tokenfile);
            $msg_array = json_decode($message->payload, true);
            $sdp_smses = $msg_array[0];
            $sms = $msg_array[1];
            $time_start = microtime(true);
            $db_instance = DatabaseConnection::getInstance();
            /*
$sql = array(); 
foreach( $data as $row ) {
   // $sql[] = '("'.mysql_real_escape_string($row['messagePart']).'", '.$row['category_id'].')';
    $sql[] = '(" . $sms["messagePart"] . "', '" . $sms["credits"] . "', '" . $sms['messageId'] . "', '" . $sms['messagePart'] . "', '" . $sms['to'] . "', '" . $sms['status'] . "', '" . $sms['status'] . "', 
                '" . $sms['statusCode'] . "', '" . $sms['status'] . "', 'sms','" . $sms['packageId'] . "', '" . $sms['project_id'] . "', '" . $sms['message'] . "', '" . $sms['from'] . "','outbound', '" . $sms['created_at'] . "',  
                '" . $sms['created_at'] . "','2','" . $sms['messageId'] . "', '" . $sms['bulkId'] . "', '" . $sms['telco'] . "',  
                '" . $sms['type'] . "','" . $sms['link_id'] . ")';

                
}
mysql_query('INSERT INTO table (cost,price,message_id,message_part,`to`,detail,status,
                status_code,description,channel,package_id,project_id,`message`,`from`,direction,created_at,
                sent_at,campaign_id,tz_id,bulk_id,telco,type,uri)  VALUES '.implode(',', $sql));

            */
            $conn = $db_instance->getConnection();
            //foreach ($raw_sms as $sms) {
                $sql = "INSERT INTO sms (cost,price,message_id,message_part,`to`,detail,status,
                status_code,description,channel,package_id,project_id,`message`,`from`,direction,created_at,
                sent_at,campaign_id,tz_id,bulk_id,telco,type,uri)     
                VALUES ( '" . $sms["messagePart"] . "', '" . $sms["credits"] . "', '" . $sms['messageId'] . "', '" . $sms['messagePart'] . "', '" . $sms['to'] . "', '" . $sms['status'] . "', '" . $sms['status'] . "', 
                '" . $sms['statusCode'] . "', '" . $sms['status'] . "', 'sms','" . $sms['packageId'] . "', '" . $sms['project_id'] . "', '" . $sms['message'] . "', '" . $sms['from'] . "','outbound', '" . $sms['created_at'] . "',  
                '" . $sms['created_at'] . "','2','" . $sms['messageId'] . "', '" . $sms['bulkId'] . "', '" . $sms['telco'] . "',  '" . $sms['type'] . "','" . $sms['link_id'] . "')";

                
        
                 if ($conn->query($sql) === TRUE) {
                   // echo "New msg record created successfully";
                } else {
                  //  echo "Error: " . $sql . "<br>" . $conn->error;
                }
           // }
            echo ('completed db save in:' . (string)(microtime(true) - $time_start). "\n");
            $time_start = microtime(true);
            $timestamp = time();
            $base_uri =  "http://127.0.0.1:8000/localsendsafaricomsms/";
            $SourceAddress = "127.0.0.1";
            $url =  $base_uri . 'public/CMS/bulksms';
            //make request
            $client = new Client();
            $body = (string)json_encode([
                "timeStamp" => $timestamp,
                "dataSet" => $sdp_smses
            ]);
            $token = "";
            $headers =  [
                'Content-Type' => 'application/json', 'Accept' => 'application/json', 'X-Authorization' => 'Bearer ' . $token, 'SourceAddress' =>  $SourceAddress
            ];
            $request = new Request('POST', $url, $headers, $body);
            $promise = $client->sendAsync($request)->then(function ($response) {
                echo $response->getBody();
            });
            $promise->wait();
            echo " [x http request] Done in:" . (string)(microtime(true) - $time_start) ."\n";

            break;
        case RD_KAFKA_RESP_ERR__PARTITION_EOF:
            echo "No more messages; will wait for more\n";
            break;
        case RD_KAFKA_RESP_ERR__TIMED_OUT:
            echo "Timed out\n";
            break;
        default:
            throw new \Exception($message->errstr(), $message->err);
            break;
    }
}
