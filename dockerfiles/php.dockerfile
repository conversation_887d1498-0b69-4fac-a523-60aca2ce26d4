FROM php:8.1-fpm-alpine

ARG UID
ARG GID

ENV UID=${UID} GID=${GID}


# Create laravel user and group
RUN delgroup dialout && \
    addgroup -g ${GID} --system laravel && \
    adduser -G laravel --system -D -s /bin/sh -u ${UID} laravel && \
    echo "laravel:laravel" | chpasswd && \
    apk add --no-cache sudo && \
    echo 'laravel ALL=(ALL) NOPASSWD: ALL' >> /etc/sudoers


    # Set ownership of /var/www/html to laravel user
RUN mkdir -p /var/www/html && \
chown -R laravel:laravel /var/www/html

# Update PHP-FPM configuration to use laravel user
RUN sed -i "s/user = www-data/user = laravel/g" /usr/local/etc/php-fpm.d/www.conf && \
    sed -i "s/group = www-data/group = laravel/g" /usr/local/etc/php-fpm.d/www.conf && \
    echo "php_admin_flag[log_errors] = on" >> /usr/local/etc/php-fpm.d/www.conf

# Install dependencies
RUN apk --no-cache add \
    mysql-client \
    sudo \
    autoconf \
    gcc \
    g++ \
    make \
    curl \
    linux-headers

# Allow laravel user to run mysqldump without a password
RUN echo 'laravel ALL=(ALL) NOPASSWD: /usr/bin/mysqldump' >> /etc/sudoers

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql

# Install phpredis
RUN pecl install redis && \
    docker-php-ext-enable redis

# Set the working directory
WORKDIR /var/www/html
# Switch to the laravel user
USER laravel

# Set ownership of /var/www/html to laravel user
RUN chown -R laravel:laravel /var/www/html

EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm", "-y", "/usr/local/etc/php-fpm.conf", "-R"]