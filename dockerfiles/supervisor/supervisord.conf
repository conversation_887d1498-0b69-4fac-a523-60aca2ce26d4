[supervisord]
logfile=/etc/supervisor/logs/supervisord.log ; main log file; default $CWD/supervisord.log
logfile_maxbytes=5MB         ; max main logfile bytes b4 rotation; default 50MB
logfile_backups=10           ; # of main logfile backups; 0 means none, default 10
loglevel=info                ; log level; default info; others: debug,warn,trace
pidfile=/tmp/supervisord.pid ; supervisord pidfile; default supervisord.pid
nodaemon=false               ; start in foreground if true; default false
minfds=1024                  ; min. avail startup file descriptors; default 1024
minprocs=200                 ; min. avail process descriptors;default 200

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock ; use a unix:// URL  for a unix socket


[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work redis --queue=otp,high,dlr,low,default --tries=1 --sleep=3 --timeout=360
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/worker.log
stopwaitsecs=3600


[program:payment-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/consumer/update-payment.php
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/html/consumer/logs/payment.log
stopwaitsecs=360

[program:message-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/consumer/index.php
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/html/consumer/logs/message.log
stopwaitsecs=360


[program:laravel-scheduler]
process_name=%(program_name)s_%(process_num)02d
command=/bin/sh -c "while [ true ]; do (php /var/www/html/artisan schedule:run --verbose --no-interaction &); sleep 60; done"
autostart=true
autorestart=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/scheduler.log


#[program:mysql-scheduler]
#process_name=%(program_name)s_%(process_num)02d
#command=/bin/sh -c "while [ true ]; do (/usr/bin/mysqldump -u 'root' -p'secret' talkzuri | gzip > /var/www/html/backups/liveDB_`date +\%Y\%m\%d_\%H`sozu3.sql.gz --verbose --no-interaction &); sleep 60; done"
#autostart=true
#autorestart=true
#numprocs=1
#redirect_stderr=true
#stdout_logfile=/var/www/html/storage/logs/scheduler.log


#[program:rabbitconsumer]
#process_name=%(program_name)s_%(process_num)02d
#command=php /var/www/rabbit/rabbit-consumer.php
#autostart=true
#autorestart=true
#numprocs=1
#redirect_stderr=true
#stdout_logfile=/var/www/rabbit/logs/rabbit-consumer.log
