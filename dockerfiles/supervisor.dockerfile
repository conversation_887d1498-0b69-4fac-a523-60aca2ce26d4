FROM php:8.3-fpm-alpine

ARG UID=1000
ARG GID=1000

ENV UID=${UID}
ENV GID=${GID}

# Copy the startup script
COPY startup.sh /usr/local/bin/startup.sh
RUN chmod +x /usr/local/bin/startup.sh

WORKDIR /var/www/html

# Create a group and user with specified UID and GID
RUN addgroup -g ${GID} laravel && \
    adduser -u ${UID} -G laravel -D -s /bin/sh laravel

# Install PHP extensions, mysql-client, and supervisor
RUN docker-php-ext-install pdo pdo_mysql && \
    apk --no-cache add mysql-client && \
    apk update && apk add --no-cache supervisor

# Create supervisor log directory
RUN mkdir -p "/etc/supervisor/logs"

# Copy supervisor configuration
COPY supervisor/supervisord.conf /etc/supervisor/supervisord.conf

# Grant permissions
RUN chown -R laravel:laravel /var/www/html && chmod -R 775 /var/www/html

# Ensure laravel user can run mysqldump with sudo without password
RUN apk --no-cache add sudo && \
    echo 'laravel ALL=(ALL) NOPASSWD: /usr/bin/mysqldump' >> /etc/sudoers

CMD ["/usr/bin/supervisord", "-n", "-c", "/etc/supervisor/supervisord.conf"]
