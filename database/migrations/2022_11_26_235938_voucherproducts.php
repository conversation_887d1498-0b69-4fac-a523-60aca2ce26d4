<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Voucherproducts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('voucherproducts', function (Blueprint $table) {
            $table->id();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('terminal_number')->nullable();
            $table->string('system_service_id')->nullable();
            $table->string('system_service_name')->nullable();
            $table->string('product_id')->nullable();
            $table->string('product_name')->nullable();
            $table->string('product_description')->nullable();
            $table->string('product_type')->nullable();
            $table->string('product_code')->nullable(); 
            $table->string('product_info')->nullable(); 
            $table->string('batch_id')->nullable(); 
            $table->string('batch_name')->nullable(); 
            $table->string('batch_type')->nullable();
            $table->string('batch_description')->nullable(); 
            $table->decimal('minimum_value',20,2)->nullable(); 
            $table->decimal('maximum_value',20,2)->nullable();
            $table->string('image_url')->nullable(); 
            $table->string('country')->nullable();
            $table->text('field_info')->nullable(); 
            $table->string('notification_info')->nullable(); 
            $table->string('allow_minor_currency')->nullable();
            $table->string('surcharge_type')->nullable();
            $table->decimal('surcharge_value',20,2)->nullable();
            $table->string('detail')->nullable();
            $table->integer('created_by')->nullable(); 
            $table->timestamps();
        });
        }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
