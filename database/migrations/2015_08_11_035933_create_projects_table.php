<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProjectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->unique();
            $table->boolean('trial')->default(true);//all projects are on trial until they are approed and backed by company docs
            $table->string('code')->unique()->nullable();
            $table->integer('user_id')->unsigned();
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('api_token', 80)
                        ->unique()
                        ->nullable()
                        ->default(null);//critical to retun project instance on authntication
            $table->string('api_key')->nullable();
            $table->string('shortcode',10)->unique()->nullable();
            $table->string('min_balance')->default(0);
            $table->string('min_balance_email')->nullable();
            $table->string('min_balance_tel')->nullable();
            $table->decimal('credits',20,2)->unsigned()->default(0);
            $table->decimal('max_credit',10,2)->default(0);
            $table->decimal('max_monthly_credit',10,2)->default(0);
            $table->string('industry')->nullable();
            $table->string('cantact_address')->nullable();
            $table->string('company_reg')->nullable();
            $table->string('tax_id')->nullable();
            $table->string('mobile')->nullable();
            $table->string('line_1')->nullable();
            $table->string('line_2')->nullable();
            $table->string('email_1')->nullable();
            $table->string('email_2')->nullable();
            $table->string('street_address')->nullable();
            $table->string('postcode')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('detail')->nullable();
            $table->text('hatewords')->nullable();
            $table->boolean('hatewordsActive')->default(false);
            $table->boolean('isTrial')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->string('details')->nullable();
            $table->string('account_type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('projects');
    }
}
