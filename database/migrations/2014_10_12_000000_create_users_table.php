<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->increments('id');
            $table->string('nickname')->nullable();
            $table->string('name')->nullable();
            $table->string('avatar')->nullable();
            $table->string('social')->nullable();
            $table->string('fname')->nullable();
            $table->string('mname')->nullable();
            $table->string('lname')->nullable();
            $table->string('mobile',20)->unique()->nullable();
            $table->string('address')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();
            $table->string('email',60)->unique();
            $table->text('about')->nullable();
            $table->string('national_id',15)->unique()->nullable();
            $table->string('password')->nullable();// social
            $table->string('password_salt')->nullable();
            $table->string('api_token', 80)//->after('password')
            ->unique()
            ->nullable()
            ->default(null);
            $table->string('signature')->nullable();
            $table->string('image')->nullable();
            $table->dateTime('last_login')->nullable();
            $table->string('last_login_ip')->nullable();
            $table->boolean('isClerk')->default(false);
            $table->boolean('isOfficer')->default(false);
            $table->boolean('isManager')->default(false);
            $table->boolean('isAdmin')->default(false);
            $table->boolean('isGlobalClerk')->default(false);
            $table->boolean('isGlobalOfficer')->default(false);
            $table->boolean('isGlobalManager')->default(false);
            $table->boolean('isGlobalAdmin')->default(false);
            $table->string('detail')->nullable();
            $table->string('otp')->nullable();
            $table->string('otp2')->nullable();
            $table->boolean('otpVerified')->default(false);
            $table->boolean('multiFactor')->default(false);
            $table->dateTime('email_verified_at')->nullable();
            $table->string('referral_code')->nullable();
            $table->boolean('receivemessages')->default(true);
            $table->integer('createdBy')->nullable();
            $table->integer('updatedBy')->nullable();
            $table->datetime('edited')->nullable(); //boolean timish
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
