<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRoyaltiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('royalties', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->integer('user_id')->unsigned();//owner of the project
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('royalty_type')->nullable();//self project or being rewarded from referrals achieved
            $table->decimal('expenditure', 8,2)->nullable(); //how much w<s spnt
            $table->decimal('monetized_amount', 8, 2)->nullable();
            $table->decimal('points', 8, 2)->nullable();
            $table->string('detail')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('royalties');
    }
}
