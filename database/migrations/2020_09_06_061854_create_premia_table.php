<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePremiaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('premia', function (Blueprint $table) { 
            $table->bigIncrements('id');
            $table->string('name')->unique();
            $table->string('code')->unique()->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('status')->default('inactive');
            $table->boolean('default')->default(false);
            $table->string('detail')->nullable();
            //provider
            $table->date('start_date')->nullable();
            $table->string('end_date')->nullable();
            $table->string('cp_name')->nullable();
            $table->string('cp_id')->nullable();
            $table->string('shortcode')->nullable();
            $table->unsignedBigInteger('shorcode_id')->nullable();
            $table->foreign('shorcode_id')->references('id')->on('shortcodes');
            $table->string('product_notes')->nullable();
            $table->string('product')->nullable();
            $table->integer('rate')->nullable();//

            $table->string('api_type')->nullable();
            $table->string('charging_system')->nullable();
            $table->string('subscription_type')->nullable();
            $table->string('offer_status')->nullable();
            $table->string('offer_type')->nullable();//sub or ondemand
            $table->string('subscriber_type')->nullable();
            $table->string('language')->nullable();
            $table->string('activation_keywords')->nullable();//ondemand needs just this subscription
            $table->string('deactivation_keywords')->nullable();//subscription
            //
            $table->string('offer_code')->nullable();//
            $table->string('callback')->nullable();//MAIN CALLBACK
            $table->string('unsubscribe_callback')->nullable();//
            $table->string('delivery_callback')->nullable();//
            $table->string('subscribe_callback')->nullable();//
            $table->string('charging_type')->nullable();//mo or mt
            //kyc
            $table->string('company_name')->nullable();
            $table->string('company_industry')->nullable();
            $table->string('company_address')->nullable();
            $table->string('company_email')->nullable();
            $table->string('company_vat')->nullable();
            $table->string('company_bank')->nullable();
            $table->string('company_bvn')->nullable();
            $table->string('company_check')->nullable();
            $table->string('company_kra')->nullable();
            $table->string('company_cert')->nullable();
            $table->string('company_tel')->nullable();
            $table->string('company_utility')->nullable();
            $table->string('company_representative_id')->nullable();
            $table->string('application_letter')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('premia');
    }
}
