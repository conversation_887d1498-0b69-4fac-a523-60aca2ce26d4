<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned()->nullable();//may not be necessary
            $table->foreign('project_id')->references('id')->on('projects');
            $table->unsignedBigInteger('transactiontype_id');
            $table->foreign('transactiontype_id')->references('id')->on('transactiontypes');
            $table->string('description')->nullable();
            $table->string('detail')->nullable();
            $table->decimal('amount',8,2)->nullable();
            $table->string('currency')->nullable();
            $table->string('status')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->decimal('credits',8,2)->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transactions');
    }
}
