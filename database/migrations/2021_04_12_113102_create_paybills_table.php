<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaybillsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('paybills', function (Blueprint $table) {//webhook1, webhook2, 
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('type')->nullable(); //
            $table->string('number')->nullable(); //Shortcode
            $table->string('consumer_key')->nullable(); //
            $table->string('consumer_secret')->nullable(); //
            $table->string('paybill_type')->nullable(); //till , paybill
            $table->string('code')->unique();//used on webhooks 
            $table->decimal('credit_balance',10,2)->nullable(); //
            $table->string('validate_url')->nullable(); //mpesa will hit http://sozuri.net/payconfirm/paybill/validate
            $table->string('confirm_url')->nullable(); //mpesa will hit http://sozuri.net/payconfirm/paybill/confirm
            $table->string('till_callback_url')->nullable();//mpesa will hit http://sozuri.net/payconfirm/lipa/{code-xyzgjsgd}/handle

            $table->integer('user_id')->unsigned();//owner
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('status')->nullable(); //active / inactive
            $table->string('business_name')->nullable(); //iiiii
            $table->string('national_id')->nullable(); //iiiii
            $table->string('authorized_mobile')->nullable(); //iiiii

            $table->string('sender_id')->nullable(); //transactional

            $table->string('webhook')->nullable(); //customer webhook. sozuri will hit this one...
            $table->string('numbers_to_notify')->nullable(); //staff
            $table->string('daily_summary')->nullable(); //yes
            $table->string('weekly_summary')->nullable(); //yes
            $table->string('monthly_summary')->nullable(); //yes

            $table->string('template_1')->nullable(); //   
            $table->string('template_2')->nullable(); //   
            $table->string('template_3')->nullable(); //

            $table->string('token')->nullable(); //

            $table->boolean('is_parent')->default(false); //

            $table->decimal('sozuri_credit_balance',10,2)->nullable(); //
            $table->string('auto_recharge')->nullable(); //
            $table->string('sozuri_auto_recharge')->nullable(); //
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('paybills');
    }
}
