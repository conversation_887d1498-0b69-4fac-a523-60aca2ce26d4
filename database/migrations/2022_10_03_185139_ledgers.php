<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Ledgers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ledgers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('date');
            $table->string('code')->nullable();
            $table->string('currency')->nullable();
            $table->string('matching_number')->nullable();//reconcilliation
            $table->string('journal');// double entry
            $table->string('journalentry_id');// double entry
            $table->string('project')->nullable();//
            $table->string('product')->nullable();//
            $table->string('account');// eg. cash
            $table->string('account_type'); //eg. current asset
            $table->string('transaction_id')->nullable();//invoice,bill,payment,refund, inventory
            $table->string('tag')->nullable();
            $table->string('entry_type');//dr or cr
            $table->decimal('debit',20,2);
            $table->decimal('credit',20,2);
            $table->decimal('balance',20,2);
            $table->decimal('quantity',20,2)->nullable();
            $table->string('partner')->nullable();
            $table->string('financialperiod')->nullable();
            $table->string('assettype')->nullable();
            $table->boolean('isArchived')->nullable();
            $table->boolean('active')->nullable();
            $table->dateTime('due_date')->nullable();
            $table->dateTime('date_posted')->nullable();
            $table->string('description')->nullable();
            $table->string('details')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ledgers');
    }
}
