<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->increments('id');
            //$table->integer('organization_id')->unsigned();
            //$table->foreign('organization_id')->references('id')->on('organizations');
            
//so that this table can be used with different settings irregardless of the model they affect. 
//Basically all settings will be strings or numbers
            $table->string('name');
            $table->integer('int_1');
            $table->integer('int_2');
            $table->integer('int_3');
            $table->integer('int_4');
            $table->integer('int_5');
            $table->integer('int_6');
            $table->decimal('dec_1',8,2);
            $table->decimal('dec_2',8,2);
            $table->decimal('dec_3',8,2);
            $table->decimal('dec_4',8,2);
            $table->decimal('dec_5',8,2);
            $table->decimal('dec_6',8,2);
            $table->string('str_1');
            $table->string('str_2');
            $table->string('str_3');
            $table->string('str_4');
            $table->string('detail')->nullable();
            $table->boolean('isActive')->default(true);
            $table->integer('modified_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settings');
    }
}
