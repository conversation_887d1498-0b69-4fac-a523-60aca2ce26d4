<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddClicksToSmsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sms', function (Blueprint $table) {
            $table->dateTime('clicked_at')->nullable();
            $table->integer('click_count')->nullable();
            $table->string('click_ip')->nullable();
            $table->string('clicked_from')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sms', function (Blueprint $table) {
            $table->dropColumn(['clicked_at', 'click_count', 'click_ip', 'clicked_from']);

        });
    }
}
