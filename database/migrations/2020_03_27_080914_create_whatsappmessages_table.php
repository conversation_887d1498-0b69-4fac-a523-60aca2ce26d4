<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhatsappmessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('whatsappmessages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->integer('campaign_id')->unsigned()->nullable();
            $table->foreign('campaign_id')->references('id')->on('campaigns');
            $table->string('account_sid')->nullable();   //
            $table->string('template')->nullable();   //
            $table->string('channel')->nullable();
            $table->text('message')->nullable();
            $table->string('clientMessageId')->nullable();   //
            $table->string('relatedMessageId')->nullable();
            $table->string('caption')->nullable();   //
            $table->string('charset')->nullable();   //
            $table->string('provider')->nullable();   //
            $table->string('preview_url')->nullable(); //previewFirstUrl
            $table->string('developer_message')->nullable();
            $table->string('from')->nullable();
            $table->string('to')->nullable();
            $table->decimal('cost', 8, 2)->nullable();
            $table->dateTime('send_at')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->string('bulk_id')->nullable();
            $table->string('status')->nullable();
            $table->string('detail')->nullable();
            $table->string('direction')->nullable(); //outbound-api, inbound-api
            $table->integer('num_media')->nullable();
            $table->integer('num_segments')->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->string('timestamp')->nullable();
            $table->string('recipient_id')->nullable();
            $table->string('price_unit')->nullable();
            $table->string('api_version')->nullable();
            $table->string('error_code')->nullable();
            $table->string('error_message')->nullable();
            $table->string('statusCode')->nullable();
            $table->string('messageId')->nullable();
            $table->integer('messagePart')->nullable();
            $table->string('wzId')->nullable();
            $table->string('grpId')->nullable();
            $table->string('failureReason')->nullable();
            $table->string('retryCount')->nullable();
            $table->string('linkId')->nullable();
            $table->string('networkCode')->nullable();
            $table->string('telco')->nullable();
            $table->string('desc')->nullable();
            $table->string('uri')->nullable();
            $table->text('body')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('whatsappmessages');
    }
}
