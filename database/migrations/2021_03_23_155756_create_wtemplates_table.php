<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWtemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wtemplates', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uid')->nullable(); //
            $table->string('name')->nullable(); //
            $table->string('url')->nullable(); //
            $table->string('code')->nullable();
            $table->string('language_code')->nullable();
            $table->string('attachment')->nullable();//media saved somewhere
            $table->string('text')->nullable();
            $table->string('category')->nullable(); //
            $table->unsignedBigInteger('waccount_id')->nullable();
            $table->foreign('waccount_id')->references('id')->on('waccounts');
            $table->unsignedBigInteger('wnumber_id')->nullable();
            $table->foreign('wnumber_id')->references('id')->on('wnumbers');
            $table->string('status')->nullable(); //new,submitted,approved, suspended, hold
            $table->string('rejected_reason')->nullable(); //new,submitted,approved, suspended, hold
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wtemplates');
    }
}
