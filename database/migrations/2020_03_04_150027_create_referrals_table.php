<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReferralsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('referrals', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('referrer')->unsigned();
            $table->foreign('referrer')->references('id')->on('users');
            $table->string('code')->nullable();
            $table->text('url')->nullable();
            $table->integer('referred')->unsigned();
            $table->foreign('referred')->references('id')->on('users');
            $table->decimal('referrer_reward',8,2)->nullable();
            $table->boolean('referrer_reward_redemption')->default(false);
            $table->decimal('referred_reward',8,2)->nullable();
            $table->boolean('referred_reward_redemption')->default(false);
            $table->string('status')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->datetime('edited')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('referrals');
    }
}
