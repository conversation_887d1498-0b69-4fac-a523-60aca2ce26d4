<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateKeywordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('keywords', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->unique();
            $table->string('code')->unique()->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->unsignedBigInteger('shortcode_id')->nullable();
            $table->foreign('shortcode_id')->references('id')->on('shortcodes');
            $table->string('detail')->nullable();
            //
            $table->string('country')->nullable();
            $table->string('keywords')->nullable();//to receive
            $table->string('category')->nullable();
            //provider
            $table->date('start_date')->nullable();
            $table->string('end_date')->nullable();
            $table->string('cpid')->nullable();
            $table->string('cp_name')->nullable();
            $table->string('twoway')->nullable(); //interactive, non-interactive
            $table->string('type')->nullable(); //shared, dedicated
            $table->string('provider_id')->nullable();
            $table->string('kyc')->nullable();
            $table->string('reject_reason')->nullable();
            $table->string('status')->nullable();
            //purchase
            $table->decimal('price',20,2)->nullable();//active
            $table->string('paid')->nullable();//yes or no
            $table->dateTime('paid_at')->nullable();//yes or no
            //telco
            $table->string('telco')->nullable();//safaricom, airtel, telkom
            $table->string('transaction_id')->unique()->nullable();// telco-KEYWORD
            //kyc
            $table->string('company_name')->nullable();
            $table->string('company_industry')->nullable();
            $table->string('company_address')->nullable();
            $table->string('company_email')->nullable();
            $table->string('company_vat')->nullable();
            $table->string('company_bank')->nullable();
            $table->string('company_bvn')->nullable();
            $table->string('company_check')->nullable();
            $table->string('company_kra')->nullable();
            $table->string('company_cert')->nullable();
            $table->string('company_tel')->nullable();
            $table->string('company_utility')->nullable();
            $table->string('company_representative_id')->nullable();
            //
            $table->string('application_letter')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('keywords');
    }
}
