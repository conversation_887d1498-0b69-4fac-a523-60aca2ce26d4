<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateContactsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('name')->nullable();
            $table->string('fname')->nullable();
            $table->string('mname')->nullable();
            $table->string('lname')->nullable();
            $table->string('mobile');
            $table->string('email',60)->nullable();
            $table->string('type')->nullable();
            $table->string('job')->nullable();
            $table->string('company')->nullable();
            $table->string('city')->nullable();
            $table->boolean('isStar')->default("0")->nullable();
            $table->boolean('isSpam')->default("0")->nullable();
            $table->boolean('optedOut')->default(false);//have they opted out
            $table->dateTime('optOut')->nullable();//when
            $table->string('optOutReason')->nullable();//why
            $table->boolean('isHidden')->default(false)->nullable();
            $table->boolean('exclude')->default(false)->nullable();
            $table->string('detail')->nullable();
            $table->integer('tag')->unsigned()->nullable();
            $table->foreign('tag')->references('id')->on('contact_lists');
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contacts');
    }
}
