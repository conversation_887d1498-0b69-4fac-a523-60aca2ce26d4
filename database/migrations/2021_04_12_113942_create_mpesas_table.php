<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMpesasTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mpesas', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->unsignedBigInteger('paybill_id');
            $table->foreign('paybill_id')->references('id')->on('paybills');
            $table->string('type')->nullable(); //
            $table->string('number')->nullable(); //
            $table->string('code')->unique()->nullable();
            $table->integer('user_id')->unsigned()->nullable();
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('status')->nullable(); //

            $table->string('TransactionType')->nullable(); //
            $table->string('TransID')->index(); //Mpesa code   LGYTJH7865K
            $table->dateTime('TransTime')->nullable(); // timestamp yyyymmddhhiiss.. TransactionDate for lipa na mpesa
            $table->string('TransAmount')->nullable(); //Amount for lipa na mpesa
            $table->string('BusinessShortCode')->nullable(); //

            $table->string('BillRefNumber')->nullable(); //MpesaReceiptNumber for lipa na mpesa online
            $table->string('AccountReference')->nullable(); //used with "lipa na mpesa paybills

            $table->string('InvoiceNumber')->nullable(); //
            $table->string('OrgAccountBalance')->nullable(); //on each C2B, beta: Amount on lipa with mpesa
            $table->string('ThirdPartyTransID')->nullable(); //
            $table->string('MSISDN')->nullable(); // //PhoneNumber for liupa na mpesa
            $table->string('FirstName')->nullable(); //
            $table->string('MiddleName')->nullable(); //
            $table->string('LastName')->nullable(); //

            $table->string('MerchantRequestID')->nullable(); //
            $table->string('CheckoutRequestID')->nullable(); //
            $table->string('ResultCode')->nullable(); //
            $table->string('ResultDesc')->nullable(); //

            $table->string('ResponseCode')->nullable(); //
            $table->string('ResponseDescription')->nullable(); //
            $table->string('CustomerMessage')->nullable(); //

            $table->boolean('is_parent')->default(false); //
            $table->string('paybill_type')->nullable(); //till , paybill
            $table->decimal('credit_balance',10,2)->nullable(); //
            $table->decimal('sozuri_credit_balance',10,2)->nullable(); //
            $table->string('auto_recharge')->nullable(); //
            $table->string('sozuri_auto_recharge')->nullable(); //
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mpesas');
    }
}
