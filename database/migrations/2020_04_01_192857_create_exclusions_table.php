<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateExclusionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exclusions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('name')->nullable();
            $table->string('fname')->nullable();
            $table->string('mname')->nullable();
            $table->string('lname')->nullable();
            $table->string('mobile');
            $table->string('email',60)->unique()->nullable();
            $table->string('type')->nullable();
            $table->string('job')->nullable();
            $table->string('company')->nullable();
            $table->string('city')->nullable();
            $table->boolean('isStar')->default("0")->nullable();
            $table->boolean('isSpam')->default("0")->nullable();
            $table->boolean('optedOut')->default(false);//have they opted out
            $table->dateTime('optOut')->nullable();//when
            $table->string('optOutReason')->nullable();//why
            $table->boolean('isHidden')->default("0")->nullable();
            $table->string('detail')->nullable();
            $table->integer('tag')->unsigned()->nullable();
            $table->foreign('tag')->references('id')->on('contact_lists');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exclusions');
    }
}
