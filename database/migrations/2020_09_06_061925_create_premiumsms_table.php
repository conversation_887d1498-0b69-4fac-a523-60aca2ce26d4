<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePremiumsmsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('premiumsms', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('request_id')->nullable();
            $table->string('msisdn');
            $table->string('channel')->nullable();
            $table->string('operation')->nullable();
            $table->string('offer_code')->nullable();
            $table->string('cp_id')->nullable();
            $table->string('Source_address')->nullable();
            $table->string('content')->nullable();
            $table->string('callback')->nullable();
            $table->string('message_id')->nullable();
            $table->string('language')->nullable();
            $table->string('type')->nullable();

            $table->string('link_id')->unique()->nullable();
            $table->boolean('reply')->nullable();

            $table->string('response_id')->nullable();
            $table->string('response_time_stamp')->nullable();
            $table->string('response_status')->nullable();
            $table->string('response_status_code')->nullable();
            $table->string('response_description')->nullable();

            $table->string('tz_id')->unique()->nullable();
            $table->string('grpId')->unique()->nullable();
            $table->string('bulk_id')->nullable();
            $table->string('message');
            $table->string('provider')->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->integer('campaign_id')->unsigned()->nullable();
            $table->foreign('campaign_id')->references('id')->on('campaigns');
            $table->unsignedBigInteger('subscriber_id')->nullable();
            $table->foreign('subscriber_id')->references('id')->on('subscribers');
            $table->unsignedBigInteger('shortcode_id')->nullable();
            $table->foreign('shortcode_id')->references('id')->on('shortcodes');
            $table->unsignedBigInteger('premium_id')->nullable();
            $table->foreign('premium_id')->references('id')->on('premia');
            $table->string('from')->nullable();
            $table->string('to')->nullable();//to
            $table->dateTime('send_at')->nullable();
            $table->dateTime('sent_at')->nullable();

            $table->string('direction')->nullable(); //outbound-api, inbound-api
            $table->string('api_version')->nullable();
            $table->decimal('rate',8,2)->nullable();
            $table->decimal('cost',8,2)->nullable();
            $table->decimal('price',8,2)->nullable();
            $table->string('price_unit')->nullable();

            $table->integer('message_parts')->nullable();
            $table->integer('tzmessage_part')->nullable();

            //whatsapp related
            $table->string('template')->nullable();   //
            $table->string('client_transaction_id')->nullable();   //
            $table->string('related_message_id')->nullable();
            $table->string('caption')->nullable();   //
            $table->string('charset')->nullable();   //
            $table->string('preview_url')->nullable(); //previewFirstUrl
            $table->string('developer_message')->nullable();
            $table->integer('num_media')->nullable();
            $table->integer('num_segments')->nullable();
            $table->string('timestamp')->nullable();
            $table->string('recipient_id')->nullable();

            $table->string('error_code')->nullable();
            $table->string('error_message')->nullable();

            $table->string('status')->nullable();
            $table->string('status_code')->nullable();

            $table->string('provider_status')->nullable();
            $table->string('provider_status_code')->nullable();

            $table->string('failure_reason')->nullable();
            $table->string('retry_rount')->nullable();
            $table->string('network_code')->nullable();
            $table->string('telco')->nullable();
            $table->string('desc')->nullable();
            $table->string('uri')->nullable();
            $table->text('incoming_body')->nullable();
            $table->string('request_body')->nullable();
            $table->string('response_body')->nullable();
            $table->text('provider_body')->nullable();
            $table->text('link_notify_to_customer_body')->nullable();
            $table->text('link_notify_response_from_customer_body')->nullable();
            $table->string('detail')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('premiumsms');
    }
}
