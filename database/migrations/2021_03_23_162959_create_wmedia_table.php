<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWmediaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wmedia', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uid')->nullable(); //
            $table->string('name')->nullable(); //
            $table->string('url')->nullable(); //
            $table->string('code')->unique()->nullable();
            $table->string('size')->nullable();
            $table->string('media_type')->nullable();
            $table->string('type')->nullable();//video etc
            $table->string('caption')->nullable();
            $table->string('category')->nullable(); //
            $table->unsignedBigInteger('wmessage_id')->nullable();
            $table->foreign('wmessage_id')->references('id')->on('wmessages');
            $table->unsignedBigInteger('waccount_id')->nullable();
            $table->foreign('waccount_id')->references('id')->on('waccounts');
            $table->unsignedBigInteger('wnumber_id')->nullable();
            $table->foreign('wnumber_id')->references('id')->on('wnumbers');
            $table->string('status')->nullable(); //new,submitted,approved, suspended, hold
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wmedia');
    }
}
