<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLinknoticesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('linknotices', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('tz_id')->unique();
            $table->string('name')->unique();

            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->unsignedBigInteger('premiumsms_id')->nullable();
            $table->foreign('premiumsms_id')->references('id')->on('premiumsms');
            $table->boolean('replied')->default(false);
            //provider
            $table->string('operation')->nullable();
            $table->string('request_id')->nullable();
            $table->string('request_time_stamp')->nullable();
            $table->string('link_id')->nullable(); //most vital
            $table->string('cp_id')->nullable();
            $table->string('cp_name')->nullable();
            $table->string('offer_code')->nullable(); //interactive, non-interactive
            $table->string('refernce_id')->nullable();
            $table->string('client_transaction_id')->nullable();
            $table->string('language')->nullable();
            $table->string('channel')->nullable();
            $table->string('type')->nullable();
            $table->string('user_data')->nullable();
            $table->string('msisdn')->nullable();
            $table->string('sent_at')->nullable();

            //from talkzuri to sdp
            $table->string('response_id')->nullable();
            $table->string('response_time_stamp')->nullable();
            $table->string('response_status')->nullable();
            $table->string('response_status_code')->nullable();
            $table->string('response_description')->nullable();
            //
            $table->text('link_notify_cp_notification_from_sdp_body')->nullable();
            $table->text('link_notify_response_from_customer_body')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('linknotices');
    }
}
