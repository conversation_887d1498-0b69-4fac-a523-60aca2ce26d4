<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmscallbacksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('smscallbacks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('sendCallbackUrl')->nullable();//ping on send
            $table->string('inboxCallbackUrl')->nullable();//ping on receive
            $table->string('deliveryCallbackUrl')->nullable();//ping with status
            $table->string('optOutCallbackUrl')->nullable();//ping for opt-out
            $table->string('subscriptionCallbackUrl')->nullable();//ping for opt-out

            $table->string('whatsappSendCallbackUrl')->nullable();
            $table->string('whatsappInboxCallbackUrl')->nullable();
            $table->string('whatsappDeliveryCallbackUrl')->nullable();
            $table->string('whatsappOptOutCallbackUrl')->nullable();

            $table->boolean('secure')->default(false);//enforce security

            $table->boolean('sendCallback_status')->default(false);
            $table->boolean('inboxCallback_status')->default(false);
            $table->boolean('deliveryCallback_status')->default(false);
            $table->boolean('defaultCallback')->default(false);//is it a default callback, ensure to check that no other urls are default
            $table->string('detail')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('smscallbacks');
    }
}
