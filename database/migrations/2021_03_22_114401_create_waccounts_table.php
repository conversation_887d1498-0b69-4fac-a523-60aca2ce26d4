<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWaccountsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('waccounts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uid')->nullable(); //
            $table->string('name')->nullable(); //
            $table->string('code')->unique()->nullable();
            $table->integer('user_id')->unsigned();
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('status')->nullable(); //
            $table->string('token')->nullable(); //
            $table->boolean('is_parent')->default(false); //
            $table->string('account_type')->nullable(); //
            $table->decimal('credit_balance',10,2)->nullable(); //
            $table->decimal('sozuri_credit_balance',10,2)->nullable(); //
            $table->string('auto_recharge')->nullable(); //
            $table->string('sozuri_auto_recharge')->nullable(); //
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('waccounts');
    }
}
