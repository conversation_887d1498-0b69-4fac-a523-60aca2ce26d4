<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SandboxNumbers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sandbox_numbers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uid')->nullable(); //
            $table->string('number')->nullable(); //
            $table->string('parent')->nullable(); //
            $table->string('code')->nullable();
            $table->unsignedBigInteger('wnumber_id');//parent
            $table->foreign('wnumber_id')->references('id')->on('wnumbers');
            $table->unsignedBigInteger('waccount_id');
            $table->foreign('waccount_id')->references('id')->on('waccounts');
            $table->unsignedBigInteger('wwebhook_id')->nullable();
            $table->foreign('wwebhook_id')->references('id')->on('wwebhooks');
            $table->string('status')->nullable(); //
            $table->string('webhook_id')->nullable(); //
            $table->string('number_type')->nullable(); //
            $table->string('country')->nullable(); //
            $table->string('service')->nullable(); //sms or wha
            $table->dateTime('rented_at')->nullable(); //
            $table->decimal('rental_rate',10,2)->nullable(); //
            $table->decimal('setup_rate',10,2)->nullable(); //
            $table->decimal('sozuri_rate',10,2)->nullable(); //
            $table->decimal('inbound_message_rate',10,2)->nullable(); //
            $table->decimal('credits_charged',10,2)->nullable(); //
            $table->string('platform_auto_recharge')->nullable(); //
            $table->string('sozuri_auto_recharge')->nullable(); //
            $table->decimal('platform_credit_balance',10,2)->nullable(); //
            $table->decimal('sozuri_credit_balance',10,2)->nullable(); //
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sandbox_numbers');

    }
}
