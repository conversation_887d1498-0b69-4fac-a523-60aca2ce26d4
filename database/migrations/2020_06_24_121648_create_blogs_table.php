<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBlogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('blogs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->unique()->nullable();
            $table->string('code')->nullable();
            $table->string('title')->nullable();
            $table->string('subtitle')->nullable();
            $table->text('introduction')->nullable();
            $table->text('body')->nullable();
            $table->string('image')->nullable();
            $table->string('image2')->nullable();
            $table->string('image3')->nullable();
            $table->integer('view_count')->nullable();
            $table->string('tags')->nullable();
            $table->boolean('published')->nullable();
            $table->boolean('active')->nullable();
            $table->text('description')->nullable();
            $table->integer('priority')->nullable();
            $table->integer('upvotes')->nullable();
            $table->integer('downvotes')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('blogs');
    }
}
