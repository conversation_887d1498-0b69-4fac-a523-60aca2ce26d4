<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAssignmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('mobile_number'); //customer
            $table->string('channel'); //watsa    sms

            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');

            $table->string('detail')->nullable();
            $table->dateTime('assigned_at')->nullable();
            $table->integer('assigned_by')->nullable();
            $table->dateTime('assigned_reason')->nullable();
            $table->integer('assigned_to');////.
            $table->string('crm_status'); //new,
            $table->boolean('isCurrent')->default(false); //current assignment

            $table->dateTime('closed_at')->nullable();
            $table->integer('closed_by')->nullable();
            $table->string('closed_reason')->nullable();

            $table->string('uri')->nullable();

            $table->string('file')->nullable();//
            $table->string('status')->nullable();//..

            $table->integer('customer_score')->nullable();
            $table->string('customer_score_reason')->nullable();

            $table->integer('agent_score')->nullable();
            $table->string('agent_score_reason')->nullable();

            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('assignments');
    }
}
