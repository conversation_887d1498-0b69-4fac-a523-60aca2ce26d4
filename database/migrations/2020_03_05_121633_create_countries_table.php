<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCountriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('countries', function (Blueprint $table) {
            //https://restcountries.eu/rest/v2/name/kenya
            $table->bigIncrements('id');
            $table->string('name')->nullable()->unique();
            $table->string('fullname')->nullable();
            $table->string('isocode')->nullable();
            $table->string('alpha2Code')->nullable();
            $table->string('alpha3Code')->nullable();
    
            $table->string('currency_name')->nullable();
            $table->string('currency_code')->nullable();
            $table->string('currency_symbol')->nullable();
            $table->string('language')->nullable();
            $table->string('capital')->nullable();
            $table->string('callingcode')->nullable();
            $table->string('region')->nullable();
            $table->string('regionalblock')->nullable();
            $table->string('flag')->nullable();
            $table->string('timezone')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('countries');
    }
}
