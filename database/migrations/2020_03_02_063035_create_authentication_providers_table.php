<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAuthenticationProvidersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /*
        Each login provider provides a unique key for the user. This is stored in ProviderKey. 
        The ProviderType contains information about which login provider this ProviderKey belongs to, 
        and finally, the userId column couples the information with the users table. So when you receive a succesful login from one of the login providers you find the corresponding 
        ProviderKey in the table and use set the authentication cookie for the user in question.
        */
        Schema::create('authentication_providers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('providerkey')->unique(); //unique
            $table->string('providertype');
            $table->integer('user_id')->unsigned();
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('nickname')->nullable();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('avatar')->nullable();
            $table->string('detail')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('authentication_providers');
    }
}
