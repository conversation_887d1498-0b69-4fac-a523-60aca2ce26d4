<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAutomationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('automations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->dateTime('date')->nullable();
            $table->string('uuid')->nullable();
            $table->string('code')->nullable();
            $table->integer('order')->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');

            $table->string('from')->nullable();
            $table->string('from_channel')->nullable();
            $table->string('content')->nullable();

            $table->string('reply_to')->nullable();
            $table->string('reply_channel')->nullable();
            $table->string('reply_from')->nullable();
            $table->string('reply')->nullable();

            $table->string('forward_to')->nullable();
            $table->string('forward_to_channel')->nullable();

            $table->string('reply_subject')->nullable();
            $table->string('subject')->nullable();
            
            $table->boolean('is_active')->nullable();
           
            $table->string('description')->nullable();

            $table->string('details')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('automations');
    }
}
