<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWhatsappnumbersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('whatsappnumbers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->unique();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->boolean('status')->default(false);
            $table->boolean('default')->default(false);
            $table->string('detail')->nullable();
            //kyc
            $table->string('company_name')->nullable();
            $table->string('company_industry')->nullable();
            $table->string('company_address')->nullable();
            $table->string('company_email')->nullable();
            $table->string('company_vat')->nullable();
            $table->string('company_bank')->nullable();
            $table->string('company_bvn')->nullable();
            $table->string('company_check')->nullable();
            $table->string('company_kra')->nullable();
            $table->string('company_cert')->nullable();
            $table->string('company_tel')->nullable();
            $table->string('company_utility')->nullable();
            $table->string('company_representative_id')->nullable();
            $table->string('application_letter')->nullable();
            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('whatsappnumbers');
    }
}
