<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVouchersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('vouchers', function (Blueprint $table) {
            $table->id();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('terminal_number')->nullable();
            $table->string('method_name')->nullable();
            $table->string('session_id')->nullable();
            $table->string('request_unique_id')->nullable();
            $table->string('transaction_key')->nullable();
            $table->string('referral_number')->nullable();
            $table->decimal('amount',20,2)->nullable();
            $table->string('from_ani')->nullable();
            $table->string('email')->nullable();
            $table->string('response_code')->nullable();
            $table->string('response_description')->nullable();
            $table->string('confirmation_code')->nullable();
            $table->string('audit_no')->nullable();
            $table->string('operator_request_id')->nullable();
            $table->string('provider_response')->nullable();
            $table->string('authorize_key')->nullable();
            $table->string('system_service_id')->nullable();
            $table->string('system_service_name')->nullable();
            $table->string('product_id')->nullable();
            $table->string('product_name')->nullable();
            $table->string('product_description')->nullable();
            $table->string('product_type')->nullable();
            $table->string('product_code')->nullable(); 
            $table->string('product_info')->nullable(); 
            $table->string('batch_id')->nullable(); 
            $table->string('batch_name')->nullable(); 
            $table->string('batch_type')->nullable();
            $table->string('batch_description')->nullable(); 
            $table->decimal('minimum_value',20,2)->nullable(); 
            $table->string('maximum_value')->nullable();
            $table->string('image_url')->nullable(); 
            $table->string('country')->nullable();
            $table->text('field_info')->nullable(); 
            $table->string('notification_info')->nullable(); 
            $table->string('allow_minor_currency')->nullable();
            $table->string('surcharge_type')->nullable();
            $table->decimal('surcharge_value',20,2)->nullable();
            $table->string('detail')->nullable();
            $table->integer('created_by')->nullable(); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vouchers');
    }
}
