<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePromptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('prompts', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('model')->nullable();
            $table->text('prompt')->nullable();
            $table->string('temperature')->nullable();
            $table->string('max_tokens')->nullable();
            $table->string('top_p')->nullable();
            $table->string('frequency_penalty')->nullable();
            $table->string('presence_penalty')->nullable();
            $table->string('stop')->nullable();
            $table->string('open_id')->nullable();
            $table->string('object')->nullable();
            $table->string('created')->nullable();
            $table->text('text')->nullable();
            $table->string('index')->nullable();
            $table->string('logprobs')->nullable();
            $table->string('finish_reason')->nullable();
            $table->string('prompt_tokens')->nullable();
            $table->string('completion_tokens')->nullable();
            $table->string('total_tokens')->nullable();
            $table->string('description')->nullable();
            $table->decimal('cost', 8, 2)->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('prompts');
    }
}
