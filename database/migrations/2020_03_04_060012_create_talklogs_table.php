<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTalklogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('talklogs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('level');
            $table->string('resource')->nullable();
            $table->string('action')->nullable();
            $table->string('message')->nullable();
            $table->string('size')->nullable();
            $table->integer('user_id')->nullable();
            $table->string('clientIp')->nullable();
            $table->string('clientRequestMethod')->nullable();
            $table->string('clientRequestPath')->nullable();
            $table->string('clientRequestUri')->nullable();
            $table->string('ClientRequestUserAgent')->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
                        /*
            0       Emergency: system is unusable
            1       Alert: action must be taken immediately
            2       Critical: critical conditions
            3       Error: error conditions
            4       Warning: warning conditions
            5       Notice: normal but significant condition
            6       Informational: informational messages
            7       Debug: debug-level messages
            */
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('talklogs');
    }
}
