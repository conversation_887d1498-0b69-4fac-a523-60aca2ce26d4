<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned()->nullable();//may not be necessary
            $table->foreign('project_id')->references('id')->on('projects');
            $table->unsignedBigInteger('transaction_id')->nullable();//referred
            $table->foreign('transaction_id')->references('id')->on('transactions');
            $table->unsignedBigInteger('paymentmethod_id')->nullable();//referred
            $table->foreign('paymentmethod_id')->references('id')->on('paymentmethods');
            $table->unsignedBigInteger('paymentstatus_id')->nullable();//referred
            $table->foreign('paymentstatus_id')->references('id')->on('paymentstatuses');
            $table->string('name')->nullable();
            $table->string('status')->nullable();
            $table->string('txRef')->nullable();
            $table->string('flwRef')->nullable();

            $table->decimal('amount', 20, 2);
            $table->decimal('safaricom', 20, 2)->nullable();
            $table->decimal('airtel', 20, 2)->nullable();
            $table->decimal('telkom', 20, 2)->nullable();
            $table->decimal('international', 20, 2)->nullable();
            $table->decimal('other', 20, 2)->nullable();
            $table->decimal('balance', 20, 2)->nullable();

            $table->string('telco')->nullable();//can only be safaricom,airtel,telkom,other for primary account reload. 
            //all other payments will have this as null


            $table->decimal('credits',20,2)->nullable();
            $table->decimal('unit_cost', 20, 2)->nullable();
            $table->string('credit')->nullable();
            $table->string('debit')->nullable();
            $table->string('package_id')->nullable();
            $table->date('date')->nullable();
            $table->date('expiry')->nullable();

            $table->string('type')->nullable();//transactional or promotional


            $table->string('currency')->nullable();
            $table->string('respcode')->nullable();

            $table->string('resp_respmsg')->nullable();

            $table->text('body')->nullable();

            $table->string('tx_id')->nullable();
            $table->string('tx_orderRef')->nullable();
            $table->string('tx_flwRef')->nullable();
            $table->string('tx_redirectUrl')->nullable();
            $table->string('tx_device_fingerprint')->nullable();
            $table->string('tx_settlement_token')->nullable();
            $table->string('tx_cycle')->nullable();
            $table->decimal('tx_amount', 8, 2)->nullable();
            $table->decimal('tx_charged_amount', 8, 2)->nullable();
            $table->decimal('tx_appfee', 8, 2)->nullable();
            $table->decimal('tx_merchantfee', 8, 2)->nullable();

            $table->boolean('tx_merchantbearsfee')->nullable();
            $table->string('tx_chargeResponseCode')->nullable();
            $table->string('tx_raveRef')->nullable();

            $table->string('tx_chargeResponseMessage')->nullable();
            $table->string('tx_authModelUsed')->nullable();
            $table->string('tx_currency')->nullable();
            $table->string('tx_IP')->nullable();
            $table->string('tx_narration')->nullable();
            $table->string('tx_status')->nullable();//here
            $table->string('tx_modalauditid')->nullable();
            $table->string('tx_vbvrespmessage')->nullable();
            $table->string('tx_authurl')->nullable();
            $table->string('tx_vbvrespcode')->nullable();
            $table->string('tx_acctvalrespmsg')->nullable();
            $table->string('tx_acctvalrespcode')->nullable();
            $table->string('tx_paymentType')->nullable();
            $table->string('tx_paymentPlan')->nullable();
            $table->string('tx_paymentPage')->nullable();
            $table->string('tx_paymentId')->nullable();
            $table->string('tx_fraud_status')->nullable();
            $table->string('tx_charge_type')->nullable();
            $table->string('tx_is_live')->nullable();
            $table->string('tx_retry_attempt')->nullable();
            $table->string('tx_getpaidBatchId')->nullable();
            $table->dateTimeTz('tx_createdAt')->nullable();
            $table->dateTimeTz('tx_updatedAt')->nullable();
            $table->dateTimeTz('tx_deletedAt')->nullable();
            $table->string('tx_customerId')->nullable();
            $table->string('tx_AccountId')->nullable();
            $table->string('tx_customer_id')->nullable();
            $table->string('tx_customer_phone')->nullable();

            $table->string('tx_customer_fullName')->nullable();
            $table->string('tx_customer_customertoken')->nullable();
            $table->string('tx_customer_email')->nullable();

            $table->dateTimeTz('tx_customer_createdAt')->nullable();
            $table->dateTimeTz('tx_customer_updatedAt')->nullable();
            $table->dateTimeTz('tx_customer_deletedAt')->nullable();
            $table->string('tx_customer_AccountId')->nullable();
            $table->string('tx_chargeToken_user_token')->nullable();
            $table->string('tx_chargeToken_embed_token')->nullable();
            $table->string('airtime_flag')->nullable();

            $table->string('detail')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payments');
    }
}
