<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCampaignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('name');
            $table->string('goal')->nullable();
            $table->decimal('max_budget',20,2)->nullable();
            $table->decimal('min_budget',20,2)->nullable();
            $table->decimal('budget',20,2)->nullable();
            $table->bigInteger('target_messages')->nullable();
            $table->bigInteger('target_sms')->nullable();
            $table->bigInteger('target_whatsapp')->nullable();
            $table->bigInteger('counter')->nullable();
            $table->string('sponsor')->nullable();
            $table->string('demographics')->nullable();
            $table->dateTime('start')->nullable();
            $table->dateTime('end')->nullable();
            $table->string('detail')->nullable();
            $table->json('delivery')->nullable();
            $table->boolean('isActive')->nullable();
            $table->boolean('isArchived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('campaigns');
    }
}
