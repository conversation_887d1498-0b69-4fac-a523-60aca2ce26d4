<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Subresource extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::create('subresources', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();

            $table->unsignedBigInteger('whatsappmessage_id')->nullable();
            $table->foreign('whatsappmessage_id')->references('id')->on('whatsappmessages');
            $table->unsignedBigInteger('sms_id')->nullable();
            $table->foreign('sms_id')->references('id')->on('sms');

            $table->string('channel')->nullable();
            $table->string('type')->nullable(); //media print ettc
            $table->string('caption')->nullable(); //media print ettc

            $table->string('template')->nullable();
            $table->text('parameters')->nullable();

            $table->string('status')->nullable();
            $table->string('detail')->nullable();
            $table->string('messageId')->nullable();
            $table->string('uri')->nullable();
            $table->string('media_uri')->nullable();
            $table->string('mime_type')->nullable();
            $table->string('file_id')->nullable();
            $table->string('filename')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
        Schema::dropIfExists('subresources');
    }
}
