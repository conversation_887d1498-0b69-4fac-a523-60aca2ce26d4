<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLogDownloadsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('log_downloads', function (Blueprint $table) {
            $table->id();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->integer('user_id')->unsigned()->nullable();//referred
            $table->foreign('user_id')->references('id')->on('users');
            $table->string('email')->nullable();
            $table->string('file_name'); 
            $table->string('file_path');
            $table->string('file_type');
            $table->string('file_size');
            $table->string('downloaded_by')->nullable();
            $table->string('downloaded_at')->nullable();
            $table->string('download_status')->default('pending'); // pending, completed, failed
            $table->string('error_message')->nullable(); // for failed downloads
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('log_downloads');
    }
}
