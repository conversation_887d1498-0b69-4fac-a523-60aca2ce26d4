<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWappmessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wappmessages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uuid')->nullable();
            $table->string('message_id')->nullable();
            $table->string('recipient_type')->nullable();
            $table->string('context_message_id')->nullable();
            $table->string('conversation_id')->nullable();
            $table->string('expiration_timestamp')->nullable();
            $table->string('conversation_origin')->nullable();
            $table->string('timestamp')->nullable();

            $table->unsignedBigInteger('waccount_id')->nullable();
            $table->foreign('waccount_id')->references('id')->on('waccounts');
            $table->unsignedBigInteger('wnumber_id')->nullable();
            $table->foreign('wnumber_id')->references('id')->on('wnumbers');
            $table->string('channel')->nullable();
            $table->string('to')->nullable();
            $table->string('from')->nullable();
            $table->string('source')->nullable();
            $table->string('destination')->nullable();
            $table->string('direction')->nullable();

            $table->string('type')->nullable(); //template/
            $table->json('data')->nullable(); //template/

            $table->text('body')->nullable();//text
            $table->boolean('preview_url')->nullable();//text


            $table->string('longitude')->nullable();
            $table->string('latitude')->nullable();
            $table->string('name')->nullable(); //label
            $table->string('address')->nullable(); 
            
            $table->string('link')->nullable();
            $table->string('media_id')->nullable();
            $table->string('media_caption')->nullable(); //

            $table->string('emoji')->nullable();


            $table->string('events_url')->nullable(); //
            $table->string('refund')->nullable(); //
            $table->decimal('cost',10,2)->nullable(); //buy credits_charged
            $table->boolean('billable')->nullable(); //whtsapp rate
            $table->string('pricing_category')->nullable(); //business_initiated or user
            $table->string('provider_pricing_model')->nullable(); //business_initiated

            $table->decimal('whatsapp_fee',10,2)->nullable(); //whtsapp rate
            $table->decimal('platform_fee',10,2)->nullable(); //credits_charged
            $table->decimal('provider_fee',10,2)->nullable(); //
            $table->decimal('provider_balance',10,2)->nullable(); //available credits

            $table->decimal('price',10,2)->nullable(); //amount charged

            $table->string('content_type')->nullable(); //text, 

            $table->string('status')->nullable(); //n failed error message
            $table->string('status_code')->nullable(); //error code
            $table->string('description')->nullable(); //n error message
            $table->string('details')->nullable(); //n

            $table->string('redact')->nullable(); //n

            $table->string('rejected_reason')->nullable(); //new,submitted,approved, suspended, hold
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->dateTime('delivered_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wappmessages');
    }
}
