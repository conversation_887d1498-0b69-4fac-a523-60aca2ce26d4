<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWmessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wmessages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uid')->nullable(); //
            $table->string('name')->nullable(); //
            $table->string('url')->nullable(); //
            $table->string('code')->unique()->nullable();

            $table->unsignedBigInteger('waccount_id')->nullable();
            $table->foreign('waccount_id')->references('id')->on('waccounts');
            $table->unsignedBigInteger('wnumber_id')->nullable();
            $table->foreign('wnumber_id')->references('id')->on('wnumbers');
            $table->string('channel')->nullable();
            $table->string('source')->nullable();
            $table->string('destination')->nullable();
            $table->string('direction')->nullable();

            $table->string('type')->nullable(); //template/nitification or conversational

            $table->text('text')->nullable(); //
            $table->string('label')->nullable(); //
            $table->string('address')->nullable(); //

            $table->string('country')->nullable(); //
            $table->string('longitude')->nullable(); //
            $table->string('latitude')->nullable(); //
            $table->unsignedBigInteger('media_id')->nullable();
            $table->unsignedBigInteger('media_url')->nullable();

            $table->string('media_caption')->nullable(); //
            $table->string('events_url')->nullable(); //
            $table->string('refund')->nullable(); //
            $table->decimal('cost',10,2)->nullable(); //buy credits_charged

            $table->decimal('whatsapp_fee',10,2)->nullable(); //whtsapp rate
            $table->decimal('platform_fee',10,2)->nullable(); //credits_charged
            $table->decimal('provider_fee',10,2)->nullable(); //
            //$table->decimal('provider_balance',10,2)->nullable(); //available credits

            $table->decimal('price',10,2)->nullable(); //amount charged

            $table->string('content_type')->nullable(); //text, 

            $table->string('status')->nullable(); //n failed error message
            $table->string('status_code')->nullable(); //error code
            $table->string('description')->nullable(); //n error message
            $table->string('details')->nullable(); //n

            $table->string('redact')->nullable(); //n

            $table->string('rejected_reason')->nullable(); //new,submitted,approved, suspended, hold
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->dateTime('delivered_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wmessages');
    }
}
