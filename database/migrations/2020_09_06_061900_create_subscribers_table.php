<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSubscribersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('subscribers', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('msisdn',20); //may be shared amont several premiums
            $table->string('cp_id')->nullable();
            $table->string('cp_name')->nullable();
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->unsignedBigInteger('premium_id');
            $table->foreign('premium_id')->references('id')->on('premia');
            $table->integer('campaign_id')->unsigned()->nullable();
            $table->foreign('campaign_id')->references('id')->on('campaigns');
            $table->string('provider_statusCode')->nullable();
            $table->string('provider_status')->nullable();//inactive or active
            $table->string('provider_description')->nullable();
            $table->decimal('rate',8,2)->nullable();
            $table->string('type')->nullable();//ondemand or subscription
            $table->string('country')->nullable();
            $table->string('last_message')->nullable();
            $table->boolean('verified')->nullable();
            $table->string('status')->nullable();//inacctive or active
            $table->string('activate_body')->nullable();
            $table->string('deactivate_body')->nullable();
            $table->integer('createdBy')->nullable();
            $table->integer('updatedBy')->nullable();
            $table->datetime('edited')->nullable(); //boolean timish
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscribers');
    }
}
