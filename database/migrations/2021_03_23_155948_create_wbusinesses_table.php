<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWbusinessesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wbusinesses', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('uid')->nullable(); //
            $table->string('name')->nullable(); //
            $table->string('email')->nullable(); //
            $table->string('code')->unique()->nullable();
            $table->string('address')->nullable(); //
            $table->string('description')->nullable(); //
            $table->string('vertical')->nullable(); //
            $table->string('category')->nullable(); //
            $table->unsignedBigInteger('waccount_id')->nullable();
            $table->foreign('waccount_id')->references('id')->on('waccounts');
            $table->unsignedBigInteger('wnumber_id')->nullable();
            $table->foreign('wnumber_id')->references('id')->on('wnumbers');
            $table->unsignedBigInteger('wprofilepic_id')->nullable();
            $table->foreign('wprofilepic_id')->references('id')->on('wprofilepics');
            $table->text('websites')->nullable(); //comma separated
            $table->string('about_text')->nullable(); //
            $table->string('opening_hours')->nullable(); //
            $table->string('optin_keywords')->nullable(); //
            $table->string('optout_keywords')->nullable(); //
            $table->string('touchpoint_url')->nullable(); //
            $table->string('supported_countries')->nullable(); //
            $table->string('unsupported_media')->nullable(); //
            $table->string('autoresponse_start')->nullable(); //
            $table->string('autoresponse_idle')->nullable(); //
            $table->string('autoresponse_toend')->nullable(); //
            $table->string('autoresponse_end')->nullable(); //
            $table->string('auto_assign')->nullable(); //
            $table->string('company_cert')->nullable(); //
            $table->string('tax_cert')->nullable(); //
            $table->string('national_id')->nullable(); //
            $table->string('application')->nullable(); //
            $table->string('facebook_id')->nullable(); //
            $table->string('status')->nullable(); //new,submitted,approved, suspended, hold
            $table->string('rejected_reason')->nullable(); //new,submitted,approved, suspended, hold
            $table->boolean('active')->nullable();
            $table->boolean('archived')->nullable();
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wbusinesses');
    }
}
