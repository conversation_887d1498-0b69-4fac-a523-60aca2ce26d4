<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ProjectEmails extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('project_emails', function (Blueprint $table) {
            $table->id();
            $table->dateTime('date');
            $table->string('uuid')->nullable();
            $table->string('code')->nullable();
            $table->integer('project_id')->unsigned()->nullable();
            $table->foreign('project_id')->references('id')->on('projects');

            $table->string('to');
            $table->string('recipient');
            $table->string('sender')->nullable();
            $table->string('from');
            $table->string('subject')->nullable();
            $table->text('body_plain')->nullable();
            $table->text('stripped_text')->nullable();
            $table->text('stripped_signature')->nullable();
            $table->longText('body_html')->nullable();
            $table->text('stripped_html')->nullable();
            $table->integer('attachment_count');
            $table->json('attachment_details')->nullable();
            $table->string('timestamp')->nullable();
            $table->boolean('forwarded')->default(false);
            $table->boolean('read')->default(false);
            $table->string('message_id')->nullable();
            $table->string('in_reply_to')->nullable();
            $table->string('references')->nullable();
            $table->string('content_type')->nullable();
            $table->string('user_agent')->nullable();

            $table->string('token')->nullable();
            $table->string('signature')->nullable();
            $table->json('message_headers')->nullable(); 
            $table->json('content_id_map')->nullable();
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('project_emails');

    }
}
