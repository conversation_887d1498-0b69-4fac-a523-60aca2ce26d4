<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('channel');//text,whatsapp
            $table->string('provider')->nullable();

            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->integer('campaign_id')->unsigned()->nullable();
            $table->foreign('campaign_id')->references('id')->on('campaigns');
            $table->string('message')->nullable();
            $table->string('from')->nullable();
            $table->string('to')->nullable();//to
            $table->dateTime('send_at')->nullable();
            $table->dateTime('sent_at')->nullable();


            $table->string('direction')->nullable(); //outbound-api, inbound-api
            $table->string('api_version')->nullable();
            $table->decimal('cost',8,2)->nullable();
            $table->decimal('price',8,2)->nullable();
            $table->string('price_unit')->nullable();

            $table->integer('message_part')->nullable();
            $table->integer('tz_message_part')->nullable();

            $table->string('message_id')->nullable();
            $table->string('link_id')->nullable();
            $table->string('tz_id')->nullable();
            $table->string('grp_id')->nullable();
            $table->string('bulk_id')->nullable();

//whatsapp related
            $table->string('account_sid')->nullable();   //
            $table->string('template')->nullable();   //
            $table->string('client_message_id')->nullable();   //
            $table->string('related_messageId')->nullable();
            $table->string('caption')->nullable();   //
            $table->string('charset')->nullable();   //
            $table->string('preview_url')->nullable(); //previewFirstUrl
            $table->string('developer_message')->nullable();
            $table->integer('num_media')->nullable();
            $table->integer('num_segments')->nullable();
            $table->string('timestamp')->nullable();
            $table->string('recipient_id')->nullable();

            $table->string('error_code')->nullable();
            $table->string('error_message')->nullable();

            $table->string('status')->nullable();
            $table->string('status_code')->nullable();

            $table->string('failure_reason')->nullable();
            $table->string('retry_count')->nullable();
            $table->string('network_code')->nullable();
            $table->string('telco')->nullable();
            $table->string('desc')->nullable();
            $table->string('uri')->nullable();
            $table->text('incoming_body')->nullable();
            $table->text('provider_body')->nullable();
            $table->string('detail')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('messages');
    }
}
