<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSmsMonthlyCopyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sms_monthly_copy', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('message_id')->nullable(); //////
            $table->integer('project_id')->unsigned();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->integer('campaign_id')->unsigned()->nullable();
            $table->foreign('campaign_id')->references('id')->on('campaigns');
            $table->unsignedBigInteger('alphanumeric_id')->nullable(); //sender name
            $table->foreign('alphanumeric_id')->references('id')->on('alphanumerics');
            $table->dateTime('time_stamp')->nullable();
            $table->string('channel')->nullable();
            $table->string('oa')->nullable(); //oa originating address
            $table->string('package_id')->nullable();
            $table->string('from')->nullable(); //oa originating address
            $table->string('sender_name')->nullable(); //Bulk
            $table->string('to')->nullable(); //to
            $table->text('message')->nullable();
            $table->string('unique_id')->nullable(); //ALPHANUMERIC
            $table->string('action_response_url')->nullable(); //Bulk
            //from talkzuri to sdp
            $table->string('response_id')->nullable();
            $table->string('response_time_stamp')->nullable();
            $table->string('response_status')->nullable();
            $table->string('response_status_code')->nullable();
            $table->string('response_description')->nullable();
            $table->string('response_body')->nullable();
            //sdp to soz
            $table->string('request_id')->nullable();
            $table->string('request_time_stamp')->nullable();
            $table->string('trace_id')->nullable();
            $table->string('correlator_id')->nullable();
            $table->string('delivery_status')->nullable();
            $table->string('delivery_description')->nullable();
            $table->text('provider_body')->nullable();
            $table->text('link_notify_to_customer_body')->nullable();
            $table->text('link_notify_response_from_customer_body')->nullable();
            $table->string('keyword')->nullable(); //
            $table->string('status')->nullable();
            $table->string('status_code')->nullable();
            $table->string('direction')->nullable(); //outbound-api, inbound-api
            $table->string('api_version')->nullable();
            $table->decimal('cost', 8, 2)->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->string('price_unit')->nullable();
            $table->string('error_code')->nullable();
            $table->string('error_message')->nullable();
            $table->dateTime('send_at')->nullable();
            $table->dateTime('sent_at')->nullable();
            $table->string('bulk_id')->nullable();
            $table->string('tz_status_code')->nullable();
            $table->string('tz_status')->nullable();
            $table->string('detail')->nullable();
            $table->integer('message_part')->nullable();
            $table->integer('tz_message_part')->nullable();
            $table->string('wz_id')->nullable();
            $table->string('tz_id')->nullable();
            $table->string('grp_id')->nullable();
            $table->string('failure_reason')->nullable();
            $table->string('retry_count')->nullable();
            $table->string('link_id')->nullable();
            $table->string('network_code')->nullable();
            $table->string('telco')->nullable();
            $table->string('desc')->nullable();
            $table->string('uri')->nullable();
            $table->string('request_body')->nullable();
            $table->string('callback')->nullable();
            $table->string('description')->nullable();
            $table->string('provider_status')->nullable();
            $table->string('provider_status_code')->nullable();
            $table->string('campaign_name')->nullable()->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sms_monthly_copy');
    }
}
