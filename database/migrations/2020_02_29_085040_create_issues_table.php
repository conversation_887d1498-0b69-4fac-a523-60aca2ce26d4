<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIssuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('issues', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('telephone')->nullable();
            $table->string('preferred_communication')->nullable();
            $table->string('url')->nullable();
            $table->string('description')->nullable();
            $table->string('source')->nullable();
            $table->integer('user_id')->unsigned()->nullable();//owner/creator
            $table->foreign('user_id')->references('id')->on('users');
            $table->integer('assisted_by')->unsigned()->nullable();
            $table->foreign('assisted_by')->references('id')->on('users');
            $table->integer('project_id')->unsigned()->nullable();
            $table->foreign('project_id')->references('id')->on('projects');
            $table->string('message_id')->nullable(); //message ref
            $table->string('query')->nullable();
            $table->date('date')->nullable();
            $table->time('time')->nullable();
            $table->string('urgency')->nullable();
            $table->string('resolution')->nullable();
            $table->string('file')->nullable();//doc
            $table->string('status')->nullable();
            $table->string('severety')->nullable();
            $table->string('detail')->nullable();

            $table->boolean('isActive')->default(true);
            $table->boolean('isArchived')->default(false);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('issues');
    }
}
