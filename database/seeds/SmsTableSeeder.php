<?php

use Illuminate\Database\Seeder;
use App\Sms;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class SmsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting SMS table seeding...');

        // Seed different types of SMS data
        $this->seedBulkSms(1000000);      // 1M bulk SMS messages
        $this->seedCampaignSms(500000);   // 500k campaign-specific SMS
        $this->seedRecentSms(100000);     // 100k recent SMS (last 30 days)

        $this->command->info('SMS table seeding completed!');
    }

    /**
     * Seed bulk SMS messages with realistic data patterns
     */
    private function seedBulkSms($count)
    {
        $this->command->info("Seeding {$count} bulk SMS messages...");

        // Disable foreign key checks for performance
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $chunkSize = 10000;
        $chunks = ceil($count / $chunkSize);

        $bar = $this->command->getOutput()->createProgressBar($chunks);

        for ($i = 0; $i < $chunks; $i++) {
            $this->seedSmsChunk($chunkSize, 'bulk');
            $bar->advance();
        }

        $bar->finish();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $this->command->line('');
    }

    /**
     * Seed campaign-specific SMS messages
     */
    private function seedCampaignSms($count)
    {
        $this->command->info("Seeding {$count} campaign SMS messages...");

        $chunkSize = 5000;
        $chunks = ceil($count / $chunkSize);

        $bar = $this->command->getOutput()->createProgressBar($chunks);

        for ($i = 0; $i < $chunks; $i++) {
            $this->seedSmsChunk($chunkSize, 'campaign');
            $bar->advance();
        }

        $bar->finish();
        $this->command->line('');
    }

    /**
     * Seed recent SMS messages (for testing current performance)
     */
    private function seedRecentSms($count)
    {
        $this->command->info("Seeding {$count} recent SMS messages...");

        $chunkSize = 5000;
        $chunks = ceil($count / $chunkSize);

        $bar = $this->command->getOutput()->createProgressBar($chunks);

        for ($i = 0; $i < $chunks; $i++) {
            $this->seedSmsChunk($chunkSize, 'recent');
            $bar->advance();
        }

        $bar->finish();
        $this->command->line('');
    }

    /**
     * Seed a chunk of SMS data
     */
    private function seedSmsChunk($size, $type = 'bulk')
    {
        $data = [];
        $faker = Faker::create();

        // Define realistic data patterns
        $telcos = ['safaricom', 'airtel', 'telkom'];
        $telcoWeights = [70, 20, 10]; // Safaricom dominance

        $statuses = ['success', 'failed', 'sent', 'unknown_error'];
        $statusWeights = [75, 10, 10, 5]; // 75% success rate

        $senderIds = ['SOZURI', 'INFO', 'ALERT', 'NOTICE', 'UPDATE'];

        for ($i = 0; $i < $size; $i++) {
            $createdAt = $this->getCreatedAtByType($type, $faker);

            $data[] = [
                'project_id' => $faker->numberBetween(1, 10),
                'campaign_id' => $faker->numberBetween(1, 50),
                'message' => $this->generateRealisticMessage($faker, $type),
                'from' => $faker->randomElement($senderIds),
                'to' => '+254' . $faker->numberBetween(700000000, 799999999),
                'cost' => $faker->randomFloat(2, 0.5, 2.0),
                'price' => $faker->randomFloat(2, 0.5, 2.0),
                'status' => $this->weightedRandomElement($statuses, $statusWeights, $faker),
                'telco' => $this->weightedRandomElement($telcos, $telcoWeights, $faker),
                'message_id' => 'SMS_' . uniqid() . '_' . $i,
                'message_part' => $faker->numberBetween(1, 3),
                'direction' => 'outbound-api',
                'channel' => 'sms',
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ];
        }

        DB::table('sms')->insert($data);
    }

    /**
     * Generate realistic SMS messages based on type
     */
    private function generateRealisticMessage($faker, $type)
    {
        switch ($type) {
            case 'campaign':
                $campaignMessages = [
                    'Special offer: 50% off all products this weekend only!',
                    'Your order has been confirmed. Delivery within 24 hours.',
                    'Reminder: Your appointment is tomorrow at 2PM.',
                    'Thank you for your purchase. Rate us on our app!',
                    'New arrivals in store. Visit us today for exclusive deals.',
                ];
                return $faker->randomElement($campaignMessages);

            case 'recent':
                $recentMessages = [
                    'Your payment of KES 500 has been received. Thank you!',
                    'Account balance: KES 1,250.50. Last transaction: -KES 100',
                    'Welcome to our service. Reply HELP for assistance.',
                    'Your subscription expires in 3 days. Renew now to continue.',
                    'Security alert: Login detected from new device.',
                ];
                return $faker->randomElement($recentMessages);

            default: // bulk
                return $faker->realText(160); // SMS character limit
        }
    }

    /**
     * Get created_at timestamp based on SMS type
     */
    private function getCreatedAtByType($type, $faker)
    {
        switch ($type) {
            case 'recent':
                return $faker->dateTimeBetween('-30 days', 'now');
            case 'campaign':
                return $faker->dateTimeBetween('-6 months', 'now');
            default: // bulk
                return $faker->dateTimeBetween('-2 years', 'now');
        }
    }

    /**
     * Select random element with weights
     */
    private function weightedRandomElement($elements, $weights, $faker)
    {
        $totalWeight = array_sum($weights);
        $random = $faker->numberBetween(1, $totalWeight);

        $currentWeight = 0;
        foreach ($elements as $index => $element) {
            $currentWeight += $weights[$index];
            if ($random <= $currentWeight) {
                return $element;
            }
        }

        return $elements[0]; // fallback
    }
}
