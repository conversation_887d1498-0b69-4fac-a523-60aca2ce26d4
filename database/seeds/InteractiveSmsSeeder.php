<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\InteractiveSms;
use Illuminate\Support\Facades\DB;

class InteractiveSmsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('Starting Interactive SMS seeding...');

        // Seed different scenarios
        $this->seedBasicInteractiveSms(100000); // 100k individual messages
        $this->seedConversationThreads(10000);  // 10k conversation threads (50k messages)
        $this->seedHighVolumeCustomers(1000);   // 1k high-volume customers (100k messages)

        $this->command->info('Interactive SMS seeding completed!');
    }

    /**
     * Seed basic interactive SMS messages
     */
    private function seedBasicInteractiveSms($count)
    {
        $this->command->info("Seeding {$count} basic interactive SMS messages...");

        $chunkSize = 1000;
        $chunks = ceil($count / $chunkSize);

        $bar = $this->command->getOutput()->createProgressBar($chunks);

        for ($i = 0; $i < $chunks; $i++) {
            InteractiveSms::factory()->count($chunkSize)->create();
            $bar->advance();
        }

        $bar->finish();
        $this->command->line('');
    }

    /**
     * Seed conversation threads between customers and business
     */
    private function seedConversationThreads($threadCount)
    {
        $this->command->info("Seeding {$threadCount} conversation threads...");

        $bar = $this->command->getOutput()->createProgressBar($threadCount);

        for ($i = 0; $i < $threadCount; $i++) {
            $customerNumber = '+254700' . rand(100000, 999999);
            $messagesInThread = rand(3, 8); // 3-8 messages per conversation

            InteractiveSms::factory()
                ->conversation($customerNumber, $messagesInThread)
                ->create();

            $bar->advance();
        }

        $bar->finish();
        $this->command->line('');
    }

    /**
     * Seed high-volume customers (simulate active chat users)
     */
    private function seedHighVolumeCustomers($customerCount)
    {
        $this->command->info("Seeding {$customerCount} high-volume customers...");

        $bar = $this->command->getOutput()->createProgressBar($customerCount);

        for ($i = 0; $i < $customerCount; $i++) {
            $customerNumber = '+254700' . str_pad($i, 6, '0', STR_PAD_LEFT);
            $messageCount = rand(50, 200); // 50-200 messages per customer

            // Create multiple conversation threads for this customer
            $threadCount = rand(3, 8);
            $messagesPerThread = ceil($messageCount / $threadCount);

            for ($j = 0; $j < $threadCount; $j++) {
                InteractiveSms::factory()
                    ->conversation($customerNumber, $messagesPerThread)
                    ->create();
            }

            $bar->advance();
        }

        $bar->finish();
        $this->command->line('');
    }
}
