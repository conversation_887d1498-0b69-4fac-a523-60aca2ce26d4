<?php

namespace Database\Factories;

use App\Models\InteractiveSms;
use Illuminate\Database\Eloquent\Factories\Factory;

class InteractiveSmsFactory extends Factory
{
    protected $model = InteractiveSms::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $directions = ['inbound-api', 'outbound-api'];
        $statuses = ['success', 'failed', 'sent'];
        $telcos = ['safaricom', 'airtel', 'telkom'];

        // Generate realistic Kenyan phone numbers for customers
        $customerNumbers = [
            '+254700' . $this->faker->numberBetween(100000, 999999),
            '+254701' . $this->faker->numberBetween(100000, 999999),
            '+254702' . $this->faker->numberBetween(100000, 999999),
            '+254703' . $this->faker->numberBetween(100000, 999999),
            '+254704' . $this->faker->numberBetween(100000, 999999),
            '+254705' . $this->faker->numberBetween(100000, 999999),
            '+254706' . $this->faker->numberBetween(100000, 999999),
            '+254707' . $this->faker->numberBetween(100000, 999999),
            '+254708' . $this->faker->numberBetween(100000, 999999),
            '+254709' . $this->faker->numberBetween(100000, 999999),
        ];

        $shortcode = '254367';
        $direction = $this->faker->randomElement($directions);

        // Determine from/to based on direction
        if ($direction === 'inbound-api') {
            $from = $this->faker->randomElement($customerNumbers);
            $to = $shortcode;
        } else {
            $from = $shortcode;
            $to = $this->faker->randomElement($customerNumbers);
        }

        return [
            'project_id' => $this->faker->numberBetween(1, 10),
            'campaign_id' => $this->faker->numberBetween(1, 50),
            'channel' => 'interactive',
            'from' => $from,
            'to' => $to,
            'message' => $this->generateInteractiveMessage($direction),
            'direction' => $direction,
            'status' => $this->faker->randomElement($statuses),
            'telco' => $this->faker->randomElement($telcos),
            'message_id' => 'INT_' . $this->faker->uuid,
            'message_part' => 1,
            'cost' => $this->faker->randomFloat(2, 0.5, 2.0),
            'price' => $this->faker->randomFloat(2, 0.5, 2.0),
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'updated_at' => function (array $attributes) {
                return $attributes['created_at'];
            },
        ];
    }

    /**
     * Generate realistic interactive SMS messages
     */
    private function generateInteractiveMessage($direction)
    {
        if ($direction === 'inbound-api') {
            // Customer messages
            $inboundMessages = [
                'Hello, I need help with my account',
                'What are your business hours?',
                'How can I check my balance?',
                'I want to unsubscribe',
                'Thank you for the service',
                'Can you send me more information?',
                'STOP',
                'HELP',
                'INFO',
                'BALANCE',
                'Yes, please proceed',
                'No, cancel that',
                'I have a complaint',
                'When will my order arrive?',
                'Please call me back',
            ];
            return $this->faker->randomElement($inboundMessages);
        } else {
            // Outbound messages from business
            $outboundMessages = [
                'Hello! Welcome to our service. Reply HELP for assistance.',
                'Your account balance is KES 150.50. Thank you for using our service.',
                'Thank you for your inquiry. Our team will get back to you shortly.',
                'Your order has been confirmed. Delivery within 24 hours.',
                'Reminder: Your subscription expires in 3 days. Reply RENEW to continue.',
                'We have received your payment. Thank you!',
                'Our business hours are Mon-Fri 8AM-6PM, Sat 9AM-2PM.',
                'To unsubscribe, reply STOP. To get help, reply HELP.',
                'Your request has been processed successfully.',
                'Special offer: 50% off this weekend only! Reply YES to claim.',
            ];
            return $this->faker->randomElement($outboundMessages);
        }
    }

    /**
     * Create a conversation thread between customer and business
     */
    public function conversation($customerNumber = null, $messageCount = 5)
    {
        $customerNumber = $customerNumber ?: '+254700' . $this->faker->numberBetween(100000, 999999);
        $shortcode = '254367';
        $projectId = $this->faker->numberBetween(1, 10);
        $campaignId = $this->faker->numberBetween(1, 50);

        return $this->count($messageCount)->sequence(function ($sequence) use ($customerNumber, $shortcode, $projectId, $campaignId) {
            $isCustomerMessage = $sequence->index % 2 === 0;

            return [
                'project_id' => $projectId,
                'campaign_id' => $campaignId,
                'from' => $isCustomerMessage ? $customerNumber : $shortcode,
                'to' => $isCustomerMessage ? $shortcode : $customerNumber,
                'direction' => $isCustomerMessage ? 'inbound-api' : 'outbound-api',
                'created_at' => now()->subMinutes(($messageCount - $sequence->index) * 5),
            ];
        });
    }
}
