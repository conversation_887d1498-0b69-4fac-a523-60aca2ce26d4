<?php

use <PERSON>aker\Generator as Faker;

$factory->define(App\Deposit::class, function (Faker $faker) {
    return [
        //
    
        'project_id' => $faker->regexify('[1]'),
        'amount' => $faker->numberBetween($min = 1000, $max = 9000),
        'rate' => $num = $faker->numberBetween($min = 1000, $max = 9000),
        'balance' => $num,
        'deposit_credits' => $num,
        'from' => $faker->word,
        'status' => $faker->word,
        'payment_method' => $faker->word,
        'type' => $faker->word,
        'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),

    ];
});
