<?php

use <PERSON><PERSON>\Generator as Faker;

$factory->define(App\Help::class, function (Faker $faker) {
    return [
        //
        'user_id' => $faker->regexify('[1]'),
        'assisted_by' => $faker->regexify('[1]'),
        'sms_id' => $faker->regexify('[1]'),
        'query' => $faker->sentence($nbWords = 6, $variableNbWords = true),
        'happened' => $faker->dateTime($max = 'now', $timezone = null),
        'urgency' => $faker->word,
        'solution' => $faker->catchPhrase,
        'status' => $faker->word,
        'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),

    ];
});
