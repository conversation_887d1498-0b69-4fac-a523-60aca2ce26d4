<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Message;
use Faker\Generator as Faker;

$factory->define(Message::class, function (Faker $faker) {
    return [
        //

        'project_id' => $faker->regexify('[1]'),

        'channel' => $faker->regexify('[sms][whatsapp]'),//'sms',

        'provider' => $faker->regexify('[safaricom]'),
        'campaign_id' => $faker->regexify('[1]'),

        'direction' => $faker->regexify('[inbound][outbound]'), //change


        //'deposit_id' => $faker->regexify('[1]'),
        'message' => $faker->sentence($nbWords = 6, $variableNbWords = true),
        'from' => $faker->e164PhoneNumber,
        'to' => $faker->e164PhoneNumber,
        //'number' => $faker->e164PhoneNumber,

        'cost' => $faker->regexify('[1]'),
        'price' => $faker->regexify('[1]'),

        'send_at' => $faker->dateTimeBetween($startDate = '-1 year', $endDate='now',$timezone = null),
        'sent_at' => $faker->dateTimeBetween($startDate = '-1 year', $endDate='now',$timezone = null),

        'messagePart' => $faker->regexify('[1]'),
        'tzmessagePart' => $faker->regexify('[1]'),


        'created_at' => $faker->dateTimeBetween($startDate = '-1 year', $endDate='now',$timezone = null),
        'updated_at' => $faker->dateTimeBetween($startDate = '-1 year', $endDate='now',$timezone = null),
        //'bulk_id' => $faker->randomDigit,
        'status' => $faker->word,
        'statusCode' => $faker->word,

        'messageId' => $faker->word,
        //'messagePart' => $faker->regexify('[1]'),
        'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),
    ];
});
