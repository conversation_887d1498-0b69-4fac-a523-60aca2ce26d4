<?php

use <PERSON>aker\Generator as Faker;

$factory->define(App\Sms::class, function (Faker $faker) {
    return [
        //

        'project_id' => $faker->regexify('[1]'),
        //'deposit_id' => $faker->regexify('[1]'),
        'message' => $faker->sentence($nbWords = 6, $variableNbWords = true),
        'from' => $faker->name,
        'to' => $faker->e164PhoneNumber,
        'cost' => $faker->regexify('[1]'),
        'price' => $faker->regexify('[1]'),
        'send_at' => $faker->dateTime($max = 'now', $timezone = null),
        'sent_at' => $faker->dateTime($max = 'now', $timezone = null),
        'bulk_id' => $faker->randomDigit,
        'status' => $faker->word,
        'status_code' => $faker->word,
        'message_id' => $faker->word,
        'message_part' => $faker->regexify('[1]'),
        'request_body' => "hhhhhhhh",
        'callback' => "hhhhhhhh",
        'link_notify_response_from_customer_body' => 'jjjjjj',
        'provider_status_code' => 'kkkkk',
        'description' => 'sss',
        'provider_status' => 'status',
        //
        'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),
        //'created_at' => $faker->dateTime($max = 'now', $timezone = null),
        'created_at' => $faker->dateTimeBetween($startDate = '-1 year', $endDate='now',$timezone = null),
        'updated_at' => $faker->dateTime($max = 'now', $timezone = null),
    ];
});
