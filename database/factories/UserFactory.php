<?php
namespace Database\Factories;
 
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;


class UserFactory extends Factory
{
        /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'fname' => $this->faker()->name,
            'email' => $this->faker()->unique()->safeEmail,
            'password' => '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', // secret
            'remember_token' => str_random(10),
            //'organization_id' => $this->faker()->regexify('[1]'),
            'mname' => $this->faker()->firstNameFemale,
            'lname' => $this->faker()->lastName,
            'mobile' => $this->faker()->e164PhoneNumber,
            //'tel' => $this->faker()->tollFreePhoneNumber,
            //'national_id' => $this->faker()->unique(),
            'signature' => $this->faker()->word,
            'image' => $this->faker()->word,
            'last_login' => $this->faker()->dateTime($max = 'now', $timezone = null),
            'last_login_ip' => $this->faker()->localIpv4,
            'isClerk' => $this->faker()->boolean,
            'isOfficer' => $this->faker()->boolean,
            'isGlobalAdmin' => $this->faker()->boolean,
            'detail' => $this->faker()->sentence($nbWords = 6, $variableNbWords = true),
        ];
    }
}



