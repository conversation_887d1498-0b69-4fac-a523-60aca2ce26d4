<?php

use <PERSON><PERSON>\Generator as Faker;

$factory->define(App\Setting::class, function (Faker $faker) {
    return [
        //


        'name' => $faker->regexify('/^rateamount$/'),
        'int_1' => $faker->regexify('[0-9]'),
        'int_2' => $faker->regexify('[0-9]'),
        'int_3' => $faker->regexify('[0-9]'),
        'int_4' => $faker->regexify('[0-9]'),
        'int_5' => $faker->regexify('[0-9]'),
        'int_6' => $faker->regexify('[0-9]'),
        'dec_1' => $faker->regexify('[1][0][0][0]'),
        'dec_2' => $faker->regexify('[2][0][0][0][0]'),
        'dec_3' => $faker->regexify('[5][0][0][0][0]'),
        'dec_4' => $faker->regexify('[1][0][0][0][0][0]'),
        'dec_5' => $faker->regexify('[2][0][0][0][0][0]'),
        'dec_6' => $faker->regexify('[3][0][0][0][0][0]'),
        'str_1' => $faker->regexify('^'),
        'str_2' => $faker->regexify('[0-2]'),
        'str_3' => $faker->word,
        'str_4' => $faker->word,
        'detail' => $faker->word,
        'isActive' => $faker->regexify('[1]'),

    ];
});
