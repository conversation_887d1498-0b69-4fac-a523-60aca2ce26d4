<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Whatsappmessage;
use Faker\Generator as Faker;

$factory->define(Whatsappmessage::class, function (Faker $faker) {
    return [
        //
        'project_id' => $faker->regexify('[1]'),
        'provider' => $faker->regexify('[twilio]'),

        //'deposit_id' => $faker->regexify('[1]'),
        'message' => $faker->sentence($nbWords = 6, $variableNbWords = true),
        'from' => $faker->e164PhoneNumber,
        'to' => $faker->e164PhoneNumber,
        'cost' => $faker->regexify('[1]'),
        'send_at' => $faker->dateTime($max = 'now', $timezone = null),
        'sent_at' => $faker->dateTime($max = 'now', $timezone = null),
        //'created_at' => $faker->dateTime($max = 'now', $timezone = null),
        'created_at' => $faker->dateTimeBetween($startDate = '-1 year', $endDate='now',$timezone = null),
        'updated_at' => $faker->dateTime($max = 'now', $timezone = null),
        //'bulk_id' => $faker->randomDigit,
        'status' => $faker->word,
        'statusCode' => $faker->word,
        'messageId' => $faker->word,
        //'messagePart' => $faker->regexify('[1]'),
        'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),
    ];
});
