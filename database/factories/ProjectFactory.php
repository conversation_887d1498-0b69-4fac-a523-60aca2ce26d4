<?php

use <PERSON>aker\Generator as Faker;

$factory->define(App\Project::class, function (Faker $faker) {
    return [
        //

        'name' => $faker->name,
        'user_id' => 1,
        'api_key' => '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', // secret
        //'shortcode' => $faker->unique()->word, 
       // 'code' => $faker->unique(), 

        //'service' => $faker->catchPhrase,
        'min_balance' => $faker->numberBetween($min = 100, $max = 900),
        'max_credit' => $faker->numberBetween($min = 100, $max = 900),
        'credits' => $faker->numberBetween($min = 1000, $max = 90000),
        'industry' => $faker->word,
        'cantact_address' => $faker->tollFreePhoneNumber,
        //'compay_reg' => $faker->unique()->postcode,
        'tax_id' => $faker->unique()->postcode,
        'mobile' => $faker->unique()->e164PhoneNumber,
        'line_1' => $faker->unique()->phoneNumber,
        'line_2' => $faker->unique()->phoneNumber,
        'email_1' => $faker->unique()->safeEmail,
        'email_2' => $faker->unique()->safeEmail, 
        'street_address' => $faker->streetAddress,
        'postcode' => $faker->postcode,
        'city' => $faker->city,
        'country' => $faker->country,
     
        'detail' => $faker->catchPhrase,
        //'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),

    ];
});
