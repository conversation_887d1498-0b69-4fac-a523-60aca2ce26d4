<?php

use <PERSON>aker\Generator as Faker;

$factory->define(App\Contact::class, function (Faker $faker) {
    return [
        //

        'project_id' => $faker->regexify('[1]'),
        'fname' => $faker->name,
        'mname' => $faker->firstNameFemale,
        'lname' => $faker->lastName,
        'mobile' => $faker->e164PhoneNumber,
        'email' => $faker->unique()->safeEmail,
        'type' => $faker->tollFreePhoneNumber,
        'job' => $faker->word,
        'company' => $faker->word,
        'city' => $faker->word,
        'isStar' => $faker->iso8601($max = 'now'),
        'isStar' => $faker->boolean,
        'isSpam' => $faker->boolean,
        'isHidden' => $faker->boolean,
        'detail' => $faker->sentence($nbWords = 6, $variableNbWords = true),
        'tag' => $faker->regexify('1'),

    ];
});
