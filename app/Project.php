<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Auth\Authenticatable;

class Project extends Model
{
    use Authenticatable;
    protected $guarded = ['id','code'];

    public function campaigns(){
        return $this->hasMany('App\Campaign');
    }
    public function contacts(){
        return $this->hasMany('App\Contact');
    }

    public function sms(){
        return $this->hasMany('App\Sms');
    }
    public function user(){
        return $this->belongsTo('App\User');
    }
    public function deposits(){
        return $this->hasMany('App\Deposit');
    }
    public function contactlists(){
        return $this->hasMany('App\ContactList');
    }
    public function royalties(){
        return $this->hasMany('App\Royalty');
    }
    public function collaborations(){
        return  $this->hasMany('App\Collaboration');
     }
     public function payments() {
         return $this->hasMany('App\Payment');
     }
     public function enrollments() {
        return $this->hasMany('App\Enrollment');
    }
    public function messages() {
        return $this->hasMany('App\Message');
    }
    public function smscallbacks() {
        return $this->hasMany('App\Smscallback');
    }
    public function alphanumerics() {
        return $this->hasMany('App\Alphanumeric');
    }
    public function paybills() {
        return $this->hasMany('App\Paybill');
    }
    public function mpesas() {
        return $this->hasMany('App\Mpesa');
    }

}

