<?php

namespace App\Http\Controllers;

use App\Wmedia;
use Illuminate\Http\Request;

class WmediaController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Wmedia  $wmedia
     * @return \Illuminate\Http\Response
     */
    public function show(Wmedia $wmedia)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wmedia  $wmedia
     * @return \Illuminate\Http\Response
     */
    public function edit(Wmedia $wmedia)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wmedia  $wmedia
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wmedia $wmedia)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wmedia  $wmedia
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wmedia $wmedia)
    {
        //
    }
}
