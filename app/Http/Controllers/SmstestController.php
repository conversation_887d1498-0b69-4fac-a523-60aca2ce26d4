<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;
use App\Sms;
use App\Jobs\ProcessSendsafaricombulk;
use App\Jobs\ProcessSendtelkombulk;
use App\Jobs\ProcessSendairtelbulk;

use App\Contact;
use App\ContactList;

use App\Deposit;
use App\Campaign;
use App\Alphanumeric;
use App\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Imports\ContactsImport;
use Illuminate\Database\Eloquent\Builder;
use App\Enrollment;
use GuzzleHttp\Client;
use App\Payment;

class SmsController extends Controller
{

    public function __construct()
    {
        //   $this->authorizeResource(Sms::class, 'sms');
    }
    private function redirectAndContinue($url )
    {
        ignore_user_abort(true);
        header("Location: $url");
        header("Connection: close");
        header("Content-Length: 0");
        flush();
    }
    private function costEstimate($numbers){
        $prefix = function($number){
            return substr($number, -9, 3);
        };
        $safaricom_numbers = count( array_intersect(array_map($prefix, $numbers)  ,explode(',', config('app.safaricom_prefixes')) ) );
        $airtel_numbers = count( array_intersect(array_map($prefix, $numbers), explode(',', config('app.airtel_prefixes')) ) );
        $telkom_numbers = count( array_intersect(array_map($prefix, $numbers), explode(',', config('app.telkom_prefixes')) ) );
        $equitel__numbers = count( array_intersect(array_map($prefix, $numbers), explode(',', config('app.equitel_prefixes')) ) );
        $eferio_numbers = count( array_intersect(array_map($prefix, $numbers) ,explode(',', config('app.eferio_prefixes'))) );
        $faiba4G_numbers = count( array_intersect(array_map($prefix, $numbers)  , explode(',', config('app.faiba4G_prefixes'))) );
        $semamobile_numbers = count( array_intersect(array_map($prefix, $numbers)  , explode(',', config('app.semamobile_prefixes'))) );
        $mobilepay_numbers = count( array_intersect(array_map($prefix, $numbers), explode(',', config('app.mobilepay_prefixes')) ) );

        $homelandsmedia_numbers = count( array_intersect(array_map($prefix, $numbers), explode(',', config('app.homelandsmedia_prefixes')) ) );
        return ($safaricom_numbers * 0.75) + ($airtel_numbers * 0.75) + ($telkom_numbers * 0.75) +  ($equitel__numbers * 0.75) +  ($eferio_numbers * 1) + ($faiba4G_numbers * 1) +
        ($semamobile_numbers * 1) + ($mobilepay_numbers * 1) +  ($homelandsmedia_numbers  * 1) ;
    }
    private function checkTelco($number) {
        $prefix = substr($number, -9, 3);

        $safaricom_prefixes= array_flip([110,111,112,113,114,700,701,702,703,704,705,706,707,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,740,741,742,743,745,746,747,748,749,757,758,759,768,769,790,791,792,793,794,795,796,797,798,799]);
        $airtel_prefixes=array_flip([100,101,102,103,730,731,732,733,734,735,736,737,738,739,750,751,752,753,754,755,756,762,780,781,782,783,784,785,786,787,788,789]);
        $telkom_prefixes=array_flip([770,771,772,773,774,775,776,777,778,779]);
        $equitel_prefixes=array_flip([765]);
        $eferio_prefixes=array_flip([761]);
        $faiba4G_prefixes=array_flip([747]);
        $semamobile_prefixes=array_flip([767]);
        $mobilepay_prefixes=array_flip([760]);
        $homelandsmedia_prefixes= array_flip([744]);
 
         if (isSet($safaricom_prefixes  [$prefix])) {
             return 'safaricom';
         } elseif ( isSet($airtel_prefixes[$prefix])) {
             return 'airtel';
         } elseif  (isSet($telkom_prefixes[$prefix])) {
             return 'telkom';
         } elseif ( isSet($equitel_prefixes[$prefix])) {
             return 'equitel';
         } elseif (isSet($eferio_prefixes[$prefix])) {
             return 'eferio';
         } elseif (  isSet($faiba4G_prefixes__[$prefix])) {
             return 'faiba4G';
         } elseif (isSet($semamobile_prefixes[$prefix])) {
             return 'semamobile';
         } elseif ( isSet($mobilepay_prefixes[$prefix]) ) {
             return 'mobilepay';
         } elseif ( isSet($homelandsmedia_prefixes[$prefix]) ) {
             return 'homelandsmedia';
         } else {
             return 'unsupported';
         }
    }
    function cleanNumber($phone) {
        $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
        return $clean_number;
    }
    private function verifyNumber($phone) {
            return preg_match("/^[+]?[0-9]{7,13}$/", $phone) ? 'valid' : 'invalid';
    }

    private function e164Formatter254($phone) {
        $forced_numbber = substr($phone, -9);
        return '254' . $forced_numbber;
    }
    private function statusGenerator($phone) {
        if (strlen($phone) == 12) {
            return "accepted";
        } else {
            return "unsupported_number";
        }
    }
    private function status_codeGenerator($phone) {
        if (strlen($phone) == 12) {
            return "11";
        } else {
            return "12";
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        ini_set('memory_limit', '1024M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');

        $this->authorize('viewAny', [Sms::class, $id]);
        $project = Project::find($id);
        $smses = SMS::where('project_id', $id)->latest()->get();
        //$smses = SMS::where('project_id', $id)->orderBy('created_at','asc')->get();

       // return view('sms.index', compact('smses', 'project'));
        return view('sms.log', compact('project'));

    }
    public function search(Request $request, $id)
    {
        ini_set('memory_limit', '1024M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');

        $arr = explode(' ', $request->between);
        $project = Project::find($id);
        $this->authorize('viewAny', [Sms::class, $id]);
        if ($arr[0] == "") { //empty form submitted
            $smses = Sms::where('project_id', $project->id)->latest()->get();
            return view('sms.index', compact('smses', 'project'));
        }
        $from  = $arr[0];
        $to = $arr[2];
        $smses = Sms::whereDate('created_at', '>=', $from)
            ->whereDate('created_at', '<=', $to)
            // whereBetween('created_at', [Carbon::parse($to), Carbon::parse($from) ] )
            ->where('project_id', $id)
            ->latest()->get();
        return view('sms.index', compact('smses', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        $project = Project::find($id);
        $this->authorize('create', [Sms::class, $id]);
        return view('sms.create', compact('project'))->withMessage("3 ways or all at once! Sms will go to all, contacts in Lists you have selected,
        numbers you have typed manually, and all numbers in the mobile column of your excel");
    }
    public function createhsm($id)
    {
        $project = Project::find($id);
        $this->authorize('create', [Sms::class, $id]);
        return view('sms.hsm', compact('project'))->with("Message", "3 ways or all at once! Sms will go to all, contacts in Lists you have selected,
        numbers you have typed manually, and all numbers in the mobile column of your excel");
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id = null)
    {
       $request->validate(['type'=>'required']);

        ini_set('memory_limit', '1024M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        if ($request->filled('project_id')) {
            $this->authorize('create', [Sms::class, $id]);
        }
        $request->validate(['recipients' => 'nullable', 'recipients_file' => 'nullable', 'channel' => 'nullable']);
        $response_shortener = function ($arr) {
            array_splice($arr, -8);
            return $arr;
        };

        $project = $request->filled('project_id') ? Project::find($request->project_id) :
            Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        //get the payment to be used for sending
        $payments = Payment::where('project_id',$project->id)->where('balance','>',0)->where('status','like','success')->get() ;
        if ($request->filled('campaign')) {
            $campaign_id = Campaign::where('name','like', $request->campaign)->where('project_id', $project->id)->exists() ? Campaign::where('name' ,'like', $request->campaign)->where('project_id', $project->id)->first()->id :
                DB::table('campaigns')->insertGetId([ 'name' => $request->campaign,'goal' => $request->campaign,'project_id' => $project->id]);
        } else {
            $campaign_id = null;
        }
        //make type promotional by default
        $type =   strtolower($request->type) == 'transactional' ? 'transactional' : 'promotional' ;
        log::info('original type'. $request->type);

        log::info($type);
        //now at this point
        Log::info('original from'. $request->from );
       // $request_from = $project->alphanumerics()->where(['status'=> 'active','name'=>$request->from, 'type'=> $type])->exists() ?
              //   $project->alphanumerics()->where(['status'=> 'active','name'=>$request->from, 'type'=> $type])->get()->first()->name : null;

        $request_from = $project->alphanumerics()->where(['status'=> 'active', 'type'=> $type])->exists() ?
                 $project->alphanumerics()->where(['status'=> 'active', 'type'=> $type])->get()->first()->name : null;

        log::info('request from'.$request_from);

        //is provided among them ?
        //$request_from =  $request_from->count() > 0 ? $request_from->first()->name : null;
        //log::info('request from'.$request_from);

        //$from = ($request->from !== null  && $request->from == $request_from) || ($request_from !== null) ? $request_from : 'Sozuri';
        $from = $request_from !== null  ? $request_from : 'Sozuri';
        log::info('from'.$from);
        log::info('project'.$project->id);

        //type must match sender ID...
        $project_saf_promo_from = Alphanumeric::where(['project_id' => $project->id, 'name'=> $from,'type'=>'promotional','telco'=>'safaricom','status' => 'active'])->get();
        $project_saf_trans_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'transactional','telco'=>'safaricom','status' => 'active'])->get();
        $project_airtel_promo_from = Alphanumeric::where(['project_id'  => $project->id,'name'=> $from,'type'=>'promotional','telco'=>'airtel','status' => 'active'])->get();
        $project_airtel_trans_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'transactional','telco'=>'airtel','status' => 'active'])->get();
        $project_telkom_promo_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'promotional','telco'=>'telkom','status' => 'active'])->get();
        $project_telkom_trans_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'transactional','telco'=>'telkom','status' => 'active'])->get();

        log::info('from'.$project_saf_promo_from);

        $primary_id = Project::where('name','primary_project')->value('id');
        $saf_promo_from =  $project_saf_promo_from->count() > 0 ? $project_saf_promo_from->first()->name :
        ( Alphanumeric::where(['project_id'=> $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'safaricom','status' => 'active'])->exists() ? 
        Alphanumeric::where(['project_id'=> $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'safaricom','status' => 'active'])->value('name')   : null);
        $saf_trans_from =  $project_saf_trans_from->count() > 0 ? $project_saf_trans_from->first()->name : 
        (Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'safaricom','status' => 'active'])->exists() ? 
        Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'safaricom','status' => 'active'])->value('name') : null );
        $airtel_promo_from =  $project_airtel_promo_from->count() > 0 ? $project_airtel_promo_from->first()->name : 
        (Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'airtel','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'airtel','status' => 'active'])->value('name') : null );
        $airtel_trans_from =  $project_airtel_trans_from->count() > 0 ? $project_airtel_trans_from->first()->name : 
        (Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'airtel','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'airtel','status' => 'active'])->value('name') : null );
        $telkom_promo_from =  $project_telkom_promo_from->count() > 0 ? $project_telkom_promo_from->first()->name : 
        (Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'telkom','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'telkom','status' => 'active'])->value('name') : null );
        $telkom_trans_from =  $project_telkom_trans_from->count() > 0 ? $project_telkom_trans_from->first()->name : 
        (Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'telkom','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'telkom','status' => 'active'])->value('name') : null);
     
       Log::info(  $saf_promo_from .'1,'.  $saf_trans_from .'2,'.  $airtel_promo_from .'3,'.  $airtel_trans_from .'4,'.  $telkom_promo_from .'5,'.  $telkom_trans_from .'6,') ;
       Log::info( $request->type ); 
       $project_hate_words = explode(",", $project->hatewords);
        $message_words = explode(" ", $request->message);
        $hateword_compare = array_intersect($project_hate_words, $message_words);
        if (!empty($hateword_compare) && $request->has('project')) {
            //$request->session()->flash('message', 'Your message appears to have banned word(s): ' . implode(",", $hateword_compare));
            // return response()->json(['message' => 'Your message appears to have banned word(s): ' . implode(",", $hateword_compare)]);
        } elseif (!empty($hateword_compare) && $request->filled('project_id')) {
            //  $request->session()->flash('message', 'Your message appears to have banned word(s): ' . implode(",", $hateword_compare));
            //  return back();
        }
        $estimated_chars = strlen($request->message); //message parts
        $estimated_sms = ceil($estimated_chars  / 160); //constant for sms
        $valid_api_recipients = array();
        //factor api numbers
        if (!empty($request->apiKey) || !empty($request->bearerToken())) {
            $recipient_api_numbers = explode(",", $request->to);
            $clean_api_recipients = (array_filter(array_map(array($this,'cleanNumber'), $recipient_api_numbers))); //array_filter-If no callback is supplied, all empty entries of array will be removed
            $api_recipients = array_map(array($this,'e164Formatter254'), $clean_api_recipients);
            $valid_api_recipients =  $api_recipients;
            $status_messages = array_map(array($this,'statusGenerator'), $api_recipients);
            $status_codes = array_map(array($this,'status_codeGenerator'), $api_recipients);
            $telcos = array_map(array($this,'checkTelco'), $api_recipients);
            $magic_number = $estimated_sms * count($valid_api_recipients);

            if ( ($payments->sum('balance')/0.78) < $magic_number) {
                return response()->json(["MessageData" => ["Error" => "Insufficient balance. Top up and try again."]]);
            }  else {

                $sms_count = count($valid_api_recipients);
                $sms  = $request->message;
                $bulk_unique_id = uniqid('bulk') . time();
                $froms = array_fill(0, $sms_count, $from);
                $types = array_fill(0, $sms_count, $type);
                $costs = array_fill(0, $sms_count, ceil($estimated_chars  / 160));
                $message_parts = array_fill(0, $sms_count, ceil($estimated_chars  / 160));
                $tos = $valid_api_recipients;
                $statuses =  $status_messages;
                $status_codes = $status_codes;
                $project_ids = array_fill(0, $sms_count,  $project->id);
                $messages = array_fill(0, $sms_count, $sms);
                $created_ats = array_fill(0, $sms_count, Carbon::now());
                $send_ats = array_fill(0, $sms_count,  $request->sendAt);
                $campaign_ids = array_fill(0, $sms_count, $campaign_id);
                $tem_wzIds = array_fill(0, $sms_count, 'soz');
                $make_internalId = function () {
                    return strtoupper(uniqid('msgblk') . time());
                };
                $message_ids = array_map($make_internalId, $tem_wzIds);  //important to give each message a unique id
                $bulk_ids = array_fill(0, $sms_count, $bulk_unique_id);
                $raw_smses = array_map(function ( $message_id, $to, $status, $status_code, $bulk_id, $message_part, $type, $cost,$project_id, $message, $from,  $created_at, $send_at,  $campaign_id, $telco) {
                    return array_combine(
                        ['messageId',  'to', 'status', 'statusCode', 'bulkId', 'messagePart', 'type','credits','project_id', 'message', 'from', 'created_at', 'send_at', 'campaign_id', 'telco'],
                        [ $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $cost,$project_id, $message, $from, $created_at, $send_at, $campaign_id, $telco]
                    );
                }, $message_ids, $tos, $statuses, $status_codes, $bulk_ids, $message_parts,  $types,$costs,$project_ids, $messages, $froms,  $created_ats, $send_ats, $campaign_ids, $telcos);
                $raw_smses_response = $raw_smses;
                $short_smses_response = array_map($response_shortener, $raw_smses_response);
                $sms_payload = $raw_smses;
                //echo response()->json(["messageData"  => ["messages" => $sms_count ] , "recipients" => $short_smses_response]);
                echo json_encode(["messageData"  => ["messages" => $sms_count ] , "recipients" => $short_smses_response ]); //echo is marginally faster than print

             $my_var  =    $this->shootMessage($sms_payload, $project, $saf_promo_from, $saf_trans_from, $airtel_promo_from, $airtel_trans_from, $telkom_promo_from, $telkom_trans_from);
             return ;
               // // return response()->json(["messageData"  => ["messages" => $sms_count ] , "recipients" => $short_smses_response]);
            }
        }
        $valid_file_recipients = array();
        $valid_list_recipients = array();
        $valid_contactlist_recipients = array();

        if ($request->filled('normal_message')) {
Log::info( $request->hasFile('recipients_file') );
            if ($request->hasFile('recipients_file')) {
                $file = $request->file('recipients_file');
                $isExcel = $this->checkExcel($file);
                if (!$isExcel) {
                    $request->session()->flash('message', 'You appear to have uploaded unallowed types. Use MS Excel xlsx, xls or CSV');
                    return back();
                }
                $file_collection = (new ContactsImport)->toCollection($request->file('recipients_file'), \Maatwebsite\Excel\Excel::XLSX); //remove type to test with other types
                $file_recipients = array_column($file_collection->collapse()->toArray(), 'mobile');
                $clean_file_recipients = array_map(array($this,'cleanNumber'), $file_recipients);
                $formatted_file_recipients = array_map(array($this,'e164Formatter254'), $clean_file_recipients);
                $validation_truthy_array = array_map(array($this,'verifyNumber'), $formatted_file_recipients); //returns a new truthy array with valid or invalid without destroying our clean file recipients
                $valid_invalid_array = preg_grep("/invalid/", $validation_truthy_array);
                if (!empty($valid_invalid_array)) {
                    $filtered = preg_grep("/invalid/", $validation_truthy_array); //filter for where verify num set to invalid
                    $filtered_rows = array_keys($filtered); //send this back to view as error
                    $request->session()->flash('message', 'Your File Upload appears to have error(s) near row(s): ' . implode(", ", $filtered_rows));
                    return back();
                } else {
                    $valid_file_recipients = array_unique($formatted_file_recipients);
                }
            }
            if ($request->has('recipient')) {
                //$recipients = $request->recipients;
                //$recipient_list_numbers = explode("\n", $request->recipient);
                $recipient_list_numbers = explode(",", $request->recipient);
                $list_recipients = array_unique(array_filter(array_map(array($this,'cleanNumber'), $recipient_list_numbers)));
                $formatted_list_recipients = array_map(array($this,'e164Formatter254'), $list_recipients);
                $valid_list_recipients = array_map(array($this,'verifyNumber'), $formatted_list_recipients);
                $labeled_list_array = preg_grep("/invalid/", $valid_list_recipients);
                if (!empty($labeled_list_array)) {
                    $filtered_list = preg_grep("/invalid/", $valid_list_recipients); //filter for where verify num set to invalid
                    $filtered_list_rows = array_keys($filtered_list); //send this back to view as error
                    $request->session()->flash('message', 'Your contact List had errors near line(s): ' . implode(", ", $filtered_list_rows));
                    return back();
                } else { //
                    $valid_list_recipients = $formatted_list_recipients;
                }
            }
            //
            if ($request->has('contactlists')) {
                $possible_lists = $request->has('contactlists') ? $request->contactlists : array(); //array of ids of lists selected
                $numbers  = [];
                foreach( $possible_lists  as $possible_lists => $value) {
                  $numbers = array_merge( ContactList::find($value)->contacts()->pluck('mobile')->toArray() );
                }
                $contactlist_recipients =  $numbers;
                $group_recipients = array_unique(array_filter(array_map(array($this,'cleanNumber'), $contactlist_recipients)));
                $valid_contactlist_recipients = array_map(array($this,'e164Formatter254'), $group_recipients);
        
            }
            $recipients = array_merge(array_merge($valid_file_recipients, array_merge($valid_contactlist_recipients, $valid_list_recipients))); //lists if some are attached
            $status_messages = array_map(array($this,'statusGenerator'), $recipients);
            $status_codes = array_map(array($this,'status_codeGenerator'), $recipients);
            $telcos = array_map(array($this,'checkTelco'), $recipients);
            $magic_number = $estimated_sms * count($recipients);
            //if ( $payments->sum('balance') <  $magic_number) {
            if ( ($payments->sum('balance')/0.78) < $magic_number) {
                $request->session()->flash('message', 'Error: Insufficient balance. Kindly top up and retry. Go to the billing menu to see how to make payment');
                return back();
            } else {
                // 
                $sms  = $request->filled('optout') ?  $request->message . ' STOP*456*9*5#' : $request->message;
                //$sms  = $request->message;
                $bulk_unique_id = uniqid('bulk') . time();
                $froms = array_fill(0, count($recipients), $from);
                $costs = array_fill(0, count($recipients), 1);
                $message_parts = array_fill(0, count($recipients), ceil($estimated_chars  / 160));
                $tos = $recipients;
                $statuses = $status_messages;
                $status_codes = $status_codes;
                $project_ids = array_fill(0, count($recipients),  $project->id);
                $types = array_fill(0, count($recipients), $type);//type
                $messages = array_fill(0, count($recipients), $sms);
                $created_ats = array_fill(0, count($recipients), Carbon::now());
                $send_ats = array_fill(0, count($recipients),  $request->sendAt);
                $campaign_ids = array_fill(0, count($recipients), $campaign_id);
                $tem_wzIds = array_fill(0, count($recipients), 'soz');
                $make_wzId = function () {
                    return strtoupper(uniqid('msgblk') . time());
                };
                $message_ids = array_map($make_wzId, $tem_wzIds);
                $bulk_ids = array_fill(0, count($recipients), $bulk_unique_id);
                $raw_smses = array_map(function ($type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from,  $created_at, $send_at,  $campaign_id, $bulk_id, $telco) {
                    return array_combine(
                        ['type', 'messageId', 'credits', 'messagePart', 'to', 'status', 'statusCode', 'project_id', 'message', 'from', 'created_at', 'send_at', 'campaign_id', 'bulkId', 'telco'],
                        [$type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from, $created_at, $send_at, $campaign_id, $bulk_id, $telco]
                    );
                }, $types, $message_ids, $costs, $message_parts, $tos, $statuses, $status_codes, $project_ids, $messages, $froms,  $created_ats, $send_ats, $campaign_ids, $bulk_ids, $telcos);
                $sms_payload = $raw_smses;
                //
                $request->session()->flash('message', 'All good! Message accepted. To be sent at ' . (Carbon::parse($request->sendAt)));
                //do something
                try{
                    echo "true";

                }finally {
             $this->shootMessage($sms_payload,$project,$saf_promo_from, $saf_trans_from, $airtel_promo_from, $airtel_trans_from, $telkom_promo_from, $telkom_trans_from);
                }
         
            }
        }
        if ($request->filled('hsm_message')) {
            $message_array = json_decode($request->recipient_list, true);
            Log::info($message_array);
            $numbers =  array_column($message_array, 'mobile');//first check if this is an array...
            $recipient_count = count($numbers);
            $estimated_sms = ceil($estimated_chars  / 160); //constant for sms
            $magic_number = $estimated_sms * $recipient_count;
            if ($request->hasFile('recipients_file')) {
                $file = $request->file('recipients_file');
                $isExcel = $this->checkExcel($file);
                if (!$isExcel) {
                    $request->session()->flash('message', 'You appear to have uploaded unallowed types. Use MS Excel xlsx, xls or CSV');
                    return back();
                }
            }
            if (($payments->sum('balance')/0.78) < $estimated_sms * $recipient_count) {
                $request->session()->flash('message', 'Error: Insufficient balance. Kindly top up and retry. Go to the billing menu to see how to make payment');
                return back();
            } else {
               // $sms  = $request->message;
                $sms  = $request->filled('optout') ?  $request->message . ' STOP*456*9*5#' : $request->message;

                $bulk_unique_id = uniqid('bulk') . time();
                $recipient_list = json_decode(json_encode($request->recipient_list));
                $newArray = array();
                $hsm_sms = function ($array, $string) {
                    foreach (array_keys($array) as $value) {
                        $string = preg_replace('/\{\{' . $value . '\}\}/i', $array[$value], $string);
                    }
                    return ($string);
                };
                $sms_count = function ($string) {
                    return (int) ceil(strlen($string) / 160);
                };
                foreach ($message_array as $key => $message) {
                    $newArray[$key] = array(
                        'number' => $message['mobile'],   //'message' => $sms,   //hapa kuna mambo
                        'message' =>  $hsm_sms($message, $sms)
                    );
                }
                $numbers =  array_column($newArray, 'number');
                $clean_numbers = array_filter(array_map(array($this,'cleanNumber'), $numbers));
                $formatted_numbers = array_map(array($this,'e164Formatter254'), $clean_numbers);
                $status_messages = array_map(array($this,'statusGenerator'), $formatted_numbers);
                $status_codes = array_map(array($this,'status_codeGenerator'), $formatted_numbers);
                $messages =  array_column($newArray, 'message');
                $froms = array_fill(0,  $recipient_count, $from);
                $costs = array_fill(0, $recipient_count, 1);
                $message_ids = array_fill(0, $recipient_count, null);
                $tos =  $formatted_numbers;
                $message_parts = array_map($sms_count, $messages);
                $statuses = $status_messages;
                $status_codes = $status_codes;
                $project_ids = array_fill(0,  $recipient_count, $project->id);
                $types = array_fill(0,   $recipient_count, $type);
                $created_ats = array_fill(0,  $recipient_count, Carbon::now());
                $send_ats = array_fill(0, $recipient_count,  $request->sendAt);
                $campaign_ids = array_fill(0,  $recipient_count, $campaign_id);
                $tem_wzIds = array_fill(0,  $recipient_count, 'soz');
                $make_wzId = function () {
                    return (uniqid('msgblk') . time());
                };
                $message_ids = array_map($make_wzId, $tem_wzIds);  //important to give each message a unique id
                $bulk_ids = array_fill(0, $recipient_count, $bulk_unique_id);
                $telcos = array_map(array($this,'checkTelco'), $numbers);
                $raw_smses = array_map(function ($type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from,  $created_at, $send_at,  $campaign_id, $bulk_id, $telco) {
                    return array_combine(
                        ['type','messageId', 'credits', 'messagePart', 'to', 'status', 'statusCode', 'project_id', 'message', 'from', 'created_at', 'send_at', 'campaign_id', 'bulkId', 'telco'],
                        [$type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from, $created_at, $send_at, $campaign_id, $bulk_id, $telco]
                    );
                },$types , $message_ids, $costs, $message_parts, $tos, $statuses, $status_codes, $project_ids, $messages, $froms,  $created_ats, $send_ats, $campaign_ids, $bulk_ids, $telcos);
                $sms_payload = $raw_smses;
                $result =  $this->shootMessage($sms_payload,$project,$saf_promo_from, $saf_trans_from, $airtel_promo_from, $airtel_trans_from, $telkom_promo_from, $telkom_trans_from);
                $request->session()->flash('message', 'All good! Custom Message accepted. To be sent at' . (Carbon::parse($request->sendAt)));
                return back();
            }
        }
    }

    
    public function trysms(Request $request) {   
        $project = Project::where('name', 'primary_project')->first();
        $payments = Payment::where('project_id',$project->id)->where('status','like','success')->where('balance','>',0)->get() ;
        $to = $request->to;
        $message = 'Thank you for testing Sozuri SMS service. Keep your customers close by sending them relevant promotion and transaction SMS';//update this
        $request->validate(['to' => 'required|regex:/^[+]?[0-9]{9,13}$/']);
        $bulk_unique_id = uniqid('blk');
        $project =   Project::where('name', 'primary_project')->first();
        $campaign_id = Campaign::where(['name' => 'test_sms', 'project_id' => $project->id])->exists() ? Campaign::where(['name' => 'test_sms', 'project_id' => $project->id])->first()->id :
            DB::table('campaigns')->insertGetId(['name' => 'test_sms', 'goal' => 'test_sms', 'project_id' => $project->id]);
        //get sender names
        $from = 'Sozuri';
        $primary_id = Project::where('name','primary_project')->value('id');
        $saf_promo_from = Alphanumeric::where(['project_id'=> $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'safaricom','status' => 'active'])->exists() ? 
        Alphanumeric::where(['project_id'=> $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'safaricom','status' => 'active'])->value('name')   : null ;
        $saf_trans_from = Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'safaricom','status' => 'active'])->exists() ? 
        Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'safaricom','status' => 'active'])->value('name') : null ;
        $airtel_promo_from =  Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'airtel','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'airtel','status' => 'active'])->value('name') : null ;
        $airtel_trans_from = Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'airtel','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'airtel','status' => 'active'])->value('name') : null ;
        $telkom_promo_from =  Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'telkom','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'telkom','status' => 'active'])->value('name') : null ;
        $telkom_trans_from =  Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'telkom','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'telkom','status' => 'active'])->value('name') : null ;

        $recipients =  [$to];
        $clean_recipients = array_unique(array_filter(array_map(array($this,'cleanNumber'), $recipients)));
        $formatted_recipients = array_map(array($this,'e164Formatter254'), $clean_recipients);
        $valid_recipients = array_map(array($this,'verifyNumber'), $formatted_recipients);
        $labeled_list_array = preg_grep("/invalid/", $valid_recipients);
        if (!empty($labeled_list_array)) {
            $filtered_list = preg_grep("/invalid/", $valid_recipients); //filter for where verify num set to invalid
            $filtered_list_rows = array_keys($filtered_list); //send this back to view as error
            $request->session()->flash('message', 'Your contact List had errors near line(s): ' . implode(", ", $filtered_list_rows));
            return back();
        } else {
            $valid_recipients = $formatted_recipients;
        }
        if (DB::table('testtexts')->where('mobile', 'like', $formatted_recipients[0] . '%')->count() > 5) {
            return response()->json(array('msg' => "You have Exceeded the number of Test SMS. <br>Create a Sozuri
            account  <a href='register'>HERE</a> :to send more", 'status' => "accepted", 'to' => '9999'), 200);
        }
        DB::table('testtexts')->insert(['mobile' => $formatted_recipients[0], 'message' => $message]);
        $recipients =   $formatted_recipients;
        $status_messages = array_map(array($this,'statusGenerator'), $recipients);
        $status_codes = array_map(array($this,'status_codeGenerator'), $recipients);
        $telcos = array_map(array($this,'checkTelco'), $recipients);
        $estimated_sms = 1; //constant for sms
        $magic_number = $estimated_sms * count($recipients);

        if (($payments->sum('balance')/0.78) <  $magic_number) {
            $request->session()->flash('message', 'Error: Insufficient balance. Kindly top up and retry. Go to the billing menu to see how to make payment');
            return back();
        } else {
            $sms  = $message;
            $bulk_unique_id = uniqid('bulk') . time();
            $froms = array_fill(0, count($recipients), $from);
            $costs = array_fill(0, count($recipients), 1);
            $message_parts = array_fill(0, count($recipients), 1);
            $tos = $recipients;
            $statuses =  $status_messages;
            $status_codes = $status_codes;
            $project_ids = array_fill(0, count($recipients),  $project->id);
            $types = array_fill(0,   count($recipients), "promotional");
            $messages = array_fill(0, count($recipients), $sms);
            $created_ats = array_fill(0, count($recipients), Carbon::now());
            $send_ats = array_fill(0, count($recipients),  $request->sendAt);
            $campaign_ids = array_fill(0, count($recipients), $campaign_id);
            $tem_wzIds = array_fill(0, count($recipients), 'soz');
            $make_wzId = function () {
                return strtoupper(uniqid('msgblk') . time());
            };
            $message_ids = array_map($make_wzId, $tem_wzIds);  //important to give each message a unique id
            $bulk_ids = array_fill(0, count($recipients), $bulk_unique_id);
            $raw_smses = array_map(function ($type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from,  $created_at, $send_at,  $campaign_id, $bulk_id, $telco) {
                return array_combine(
                    ['type','messageId', 'credits', 'messagePart', 'to', 'status', 'statusCode', 'project_id', 'message', 'from', 'created_at', 'send_at', 'campaign_id', 'bulkId', 'telco'],
                    [$type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from, $created_at, $send_at, $campaign_id, $bulk_id, $telco]
                );
            }, $types,$message_ids, $costs, $message_parts, $tos, $statuses, $status_codes, $project_ids, $messages, $froms,  $created_ats, $send_ats, $campaign_ids, $bulk_ids, $telcos);
            $sms_payload = $raw_smses;
            $result =  $this->shootMessage($sms_payload,$project,$saf_promo_from, $saf_trans_from, $airtel_promo_from, $airtel_trans_from, $telkom_promo_from, $telkom_trans_from);
            return response()->json(array('msg' => $message, 'status' => "accepted", 'to' => $to, 'time' => Carbon::now()), 200);
        }
    }
    public function simulatesms(Request $request, $pid) {
        // $request->validate(['to' => 'required', 'message' => 'required']);
         $request->validate(['to' => 'required|regex:/^[+]?[0-9]{9,13}$/','project_id' => 'required']);
         $this->authorize('create', [Sms::class, $request->project_id]);
         $project = Project::find($request->project_id);
         $payments = Payment::where('project_id',$project->id)->where('balance','>',0)->where('status','like','success')->get() ;
         $to = $request->to;
         $message = $request->message;
         $bulk_unique_id = uniqid('blk');
         $campaign_id = Campaign::where(['name' => 'test_sms', 'project_id' => $project->id])->exists() ? Campaign::where(['name' => 'test_sms', 'project_id' => $project->id])->first()->id :
             DB::table('campaigns')->insertGetId(['name' => 'test_sms', 'goal' => 'test_sms', 'project_id' => $project->id]);
        $from = $request->from !== null  ? $request->from : 'Sozuri';
        //type must match sender ID...
        $project_saf_promo_from = Alphanumeric::where(['project_id' => $project->id, 'name'=> $from,'type'=>'promotional','telco'=>'safaricom','status' => 'active'])->get();
        $project_saf_trans_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'transactional','telco'=>'safaricom','status' => 'active'])->get();
        $project_airtel_promo_from = Alphanumeric::where(['project_id'  => $project->id,'name'=> $from,'type'=>'promotional','telco'=>'airtel','status' => 'active'])->get();
        $project_airtel_trans_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'transactional','telco'=>'airtel','status' => 'active'])->get();
        $project_telkom_promo_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'promotional','telco'=>'telkom','status' => 'active'])->get();
        $project_telkom_trans_from = Alphanumeric::where(['project_id' => $project->id,'name'=> $from,'type'=>'transactional','telco'=>'telkom','status' => 'active'])->get();

        $primary_id = Project::where('name','primary_project')->value('id');
        $saf_promo_from =  $project_saf_promo_from->count() > 0 ? $project_saf_promo_from->first()->name :
        ( Alphanumeric::where(['project_id'=> $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'safaricom','status' => 'active'])->exists() ? 
        Alphanumeric::where(['project_id'=> $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'safaricom','status' => 'active'])->value('name')   : null);
        $saf_trans_from =  $project_saf_trans_from->count() > 0 ? $project_saf_trans_from->first()->name : 
        (Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'safaricom','status' => 'active'])->exists() ? 
        Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'safaricom','status' => 'active'])->value('name') : null );
        $airtel_promo_from =  $project_airtel_promo_from->count() > 0 ? $project_airtel_promo_from->first()->name : 
        (Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'airtel','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' => $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'airtel','status' => 'active'])->value('name') : null );
        $airtel_trans_from =  $project_airtel_trans_from->count() > 0 ? $project_airtel_trans_from->first()->name : 
        (Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'airtel','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'airtel','status' => 'active'])->value('name') : null );
        $telkom_promo_from =  $project_telkom_promo_from->count() > 0 ? $project_telkom_promo_from->first()->name : 
        (Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'telkom','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'promotional','telco'=>'telkom','status' => 'active'])->value('name') : null );
        $telkom_trans_from =  $project_telkom_trans_from->count() > 0 ? $project_telkom_trans_from->first()->name : 
        (Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'telkom','status' => 'active'])->exists() ?
        Alphanumeric::where(['project_id' =>  $primary_id,'name'=>'Sozuri','type'=>'transactional','telco'=>'telkom','status' => 'active'])->value('name') : null);
     
         $recipients =  [$to];
         $clean_recipients = array_unique(array_filter(array_map(array($this,'cleanNumber'), $recipients)));
         $formatted_recipients = array_map(array($this,'e164Formatter254'), $clean_recipients);
         $valid_recipients = array_map(array($this,'verifyNumber'), $formatted_recipients);
         $labeled_list_array = preg_grep("/invalid/", $valid_recipients);
         if (!empty($labeled_list_array)) {
             $filtered_list = preg_grep("/invalid/", $valid_recipients); //filter for where verify num set to invalid
             $filtered_list_rows = array_keys($filtered_list); //send this back to view as error
             $request->session()->flash('message', 'Your contact List had errors near line(s): ' . implode(", ", $filtered_list_rows));
             return back();
         } else {
             $valid_recipients = $formatted_recipients;
         }
         $recipients =   $formatted_recipients;
         $status_messages = array_map(array($this,'statusGenerator'), $recipients);
         $status_codes = array_map(array($this,'status_codeGenerator'), $recipients);
         $telcos = array_map(array($this,'checkTelco'), $recipients);
         $estimated_sms = 1; //constant for sms
         $magic_number = $estimated_sms * count($recipients);
         if (($payments->sum('balance')/0.78) <  $magic_number) {
             $request->session()->flash('message', 'Error: Insufficient balance. Kindly top up and retry. Go to the billing menu to see how to make payment');
             return back();
         } else {
             /*$enrollment = $project->whereHas('enrollments', function (Builder $query) {
                 $query->where('end', '>', Carbon::now())->where('isActive', '=', 1)->where('status', 'like', 'completed%');
             })->exists() ?
                 $project->whereHas('enrollments', function (Builder $query) {
                     $query->where('end', '>', Carbon::now())->where('isActive', '=', 1)->where('status', 'like', 'completed%');
                 })->first() : null;
             if ($enrollment) {
                 $enrollment_credits = $enrollment->credit_balance;
                 $remaining_credits = 0;
                 $enrollment = Enrollment::find($enrollment->id);
                 $remaining_credits = $magic_number - $enrollment_credits; //all - enrollments'
                 if ($remaining_credits > 0) {
                     $enrollment->credit_balance = 0;
                     $enrollment->save();
                 } else {
                     $enrollment->credit_balance -= $magic_number;
                     $enrollment->save();
                 }
             }*/
             $sms  = $message;
             $bulk_unique_id = uniqid('bulk') . time();
             $froms = array_fill(0, count($recipients), $from);
             $costs = array_fill(0, count($recipients), 1);
             $message_parts = array_fill(0, count($recipients), 1);
             $tos = $recipients;
             $statuses =  $status_messages;
             $status_codes = $status_codes;
             $project_ids = array_fill(0, count($recipients),  $project->id);
             $types = array_fill(0,   count($recipients), "promotional");
             $messages = array_fill(0, count($recipients), $sms);
             $created_ats = array_fill(0, count($recipients), Carbon::now());
             $send_ats = array_fill(0, count($recipients),  $request->sendAt);
             $campaign_ids = array_fill(0, count($recipients), $campaign_id);
             $tem_wzIds = array_fill(0, count($recipients), 'soz');
             $make_wzId = function () {
                 return strtoupper(uniqid('msgblk') . time());
             };
             $message_ids = array_map($make_wzId, $tem_wzIds);  //important to give each message a unique id
             $bulk_ids = array_fill(0, count($recipients), $bulk_unique_id);
             $raw_smses = array_map(function ($type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from,  $created_at, $send_at,  $campaign_id, $bulk_id, $telco) {
                 return array_combine(
                     ['type','messageId', 'credits', 'messagePart', 'to', 'status', 'statusCode', 'project_id', 'message', 'from', 'created_at', 'send_at', 'campaign_id', 'bulkId', 'telco'],
                     [$type, $message_id, $cost, $message_part, $to, $status, $status_code, $project_id, $message, $from, $created_at, $send_at, $campaign_id, $bulk_id, $telco]
                 );
             }, $types, $message_ids, $costs, $message_parts, $tos, $statuses, $status_codes, $project_ids, $messages, $froms,  $created_ats, $send_ats, $campaign_ids, $bulk_ids, $telcos);
             $sms_payload = $raw_smses;
          $result =    $this->shootMessage($sms_payload,$project,$saf_promo_from, $saf_trans_from, $airtel_promo_from, $airtel_trans_from, $telkom_promo_from, $telkom_trans_from);
            // return response()->json(array('msg' => $message, 'status' => "Accepted", 'to' => $to,'from' => $from, 'time' => Carbon::now()), 200);
          // Log::info( $raw_smses);
            return response()->json(array(
                'msg' => $message,
                'from' => $raw_smses[0]['from'],
                 'messageId' => $raw_smses[0]['messageId'],
                 'to' =>$raw_smses[0]['to'],
                 'status' => $raw_smses[0]['status'],
                 'statusCode' => $raw_smses[0]['statusCode'],
                 'bulkId' => $raw_smses[0]['bulkId'],
                 'messagePart' => $raw_smses[0]['messagePart'],
                 'type' => $raw_smses[0]['type'],
                 'count' => 1,
                 'time' => Carbon::now()), 200);

         }
     }

    /**
     * Display the specified resource.
     *
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function show(Sms $sms)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function edit(Sms $sms)
    {
        //
        $this->authorize('update', $sms);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Sms $sms)
    {
        $this->authorize('update', $sms);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function destroy(Sms $sms)
    {
        $this->authorize('delete', $sms);
    }

    public function shootMessage($sms_payload,$project,$saf_promo_from, $saf_trans_from, $airtel_promo_from, $airtel_trans_from, $telkom_promo_from, $telkom_trans_from){
        $token = $this->getToken();

        foreach ($sms_payload as $raw_smses) {
            //log each sms before pushing to queue
            Log::info( 'raw sms log '.  $raw_smses['telco'] . $raw_smses['messageId'] . $raw_smses['to'] . $raw_smses['status'] );
            //if message isnt accepted, save it for logging purposes. We want to see rejected once too
            if (  $raw_smses['status'] !== 'accepted' ) {
            $sms = new Sms();
            $sms->cost = number_format((float) $raw_smses['messagePart'], 2);
            $sms->message_id = $raw_smses['messageId'];
            $sms->message_part =  $raw_smses['messagePart'];
            $sms->to = $raw_smses['to'];
            $sms->detail = 'rejected';
            $sms->status = $raw_smses['status']."rejected";
            $sms->status_code = $raw_smses['statusCode'];
            $sms->channel = 'sms';
            $sms->package_id = 'noSenderFound';
            $sms->project_id =   $raw_smses['project_id'];
            $sms->message =    $raw_smses['message'];
            $sms->from =   $raw_smses['from'];
            $sms->direction = "outbound";
            $sms->created_at =    $raw_smses['created_at'];
            $sms->sent_at = Carbon::now();
            $sms->campaign_id = array_key_exists('campaign_id',   $raw_smses) ?    $raw_smses['campaign_id'] : null;
            $sms->tz_id =   $raw_smses['messageId'];
            $sms->bulk_id =   $raw_smses['bulkId'];
            $sms->telco =   $raw_smses['telco'];
            $sms->save();
            }
            //if telco is not unknown and is safaricom
            elseif (($raw_smses['type'] == "transactional") && ($saf_trans_from !== null) && $raw_smses['telco'] == 'safaricom' && $raw_smses['status'] == 'accepted') {
               // $token = $this->getToken();
                $payment = Payment::where('project_id',$project->id)->where('balance','>', $raw_smses['messagePart'])->where('status','like','success')->first();
                $payment->balance -= $payment->safaricom * $raw_smses['messagePart']; //deduct safaricom rate.
                $payment->save();
                //primary payment used
                $primary_payment = Payment::where(['project_id'=> Project::where('name','primary_project')->value('id'),'type' => 'transactional','telco' => 'safaricom'])->where('balance','>',$raw_smses['messagePart'])->first();
                $primary_payment->balance -= $raw_smses['messagePart']; //deduct one csp unit
                $primary_payment->save();

                $raw_smses['credits'] = $payment->safaricom * $raw_smses['messagePart']; //update real cost of message
                //$raw_smses['credits'] = $payment->safaricom *  $raw_smses['messagePart']; //update real cost of message
                $raw_smses['from'] = $saf_trans_from; //update sender
                ProcessSendsafaricombulk::dispatch($raw_smses, $token, $primary_payment->package_id)->onQueue('high');//->delay(Carbon::parse($request->sendAt));
             
            } elseif (($raw_smses['type'] == "promotional")  && ($saf_promo_from !== null) && $raw_smses['telco'] == 'safaricom' && $raw_smses['status'] == 'accepted') {
               // $token = $this->getToken();
               $time_start = microtime(true);
                //project payment update
                $payment = Payment::where('project_id',$project->id)->where('balance','>',$raw_smses['messagePart'])->where('status','like','success')->first();
                $payment->balance -= $payment->safaricom * $raw_smses['messagePart']; //deduct safaricom rate.
                $payment->save();
                //primary payment used
                $primary_payment = Payment::where(['project_id'=> Project::where('name','primary_project')->value('id'), 'type' => 'promotional','telco' => 'safaricom'])->where('balance','>',$raw_smses['messagePart'])->first();
                $primary_payment->balance -= $raw_smses['messagePart']; //deduct one csp unit
                $primary_payment->save();

                $raw_smses['credits'] = $payment->safaricom * $raw_smses['messagePart']; //update real cost of message
                //$raw_smses['credits'] = $payment->safaricom * $raw_smses['messagePart']; //update real cost of message
                $raw_smses['from'] = $saf_promo_from; //update sender
               ProcessSendsafaricombulk::dispatch($raw_smses, $token, $primary_payment->package_id)->delay(Carbon::parse());
               $time_end = microtime(true);
               $time = $time_end - $time_start;
               Log::info("processed in $time seconds");

            } 
            //airtel
            elseif (($raw_smses['type'] == "transactional")  && ($airtel_trans_from !== null) && $raw_smses['telco'] == 'airtel' && $raw_smses['status'] == 'accepted') {
                $payment = Payment::where('project_id',$project->id)->where('balance','>',$raw_smses['messagePart'])->where('status','like','success')->first();
                $payment->balance -= $payment->airtel * $raw_smses['messagePart']; //deduct airtel rate.
                $payment->save();
                $token = '1';

                $primary_payment = Payment::where(['project_id'=> Project::where('name','primary_project')->value('id'),'type' => 'transactional', 'telco' => 'airtel'])->where('balance','>',$raw_smses['messagePart'])->first();
                $primary_payment->balance -= $raw_smses['messagePart']; //deduct one csp unit
                $primary_payment->save();

                $raw_smses['credits'] = $payment->airtel * $raw_smses['messagePart']; //update real cost of message
                $raw_smses['from'] =  $airtel_trans_from; //update sender
                ProcessSendairtelbulk::dispatch($raw_smses, $token, $primary_payment->package_id)->onQueue('high');//->delay(Carbon::parse($request->sendAt));

            } elseif (($raw_smses['type'] == "promotional")  && ($airtel_promo_from !== null)  && $raw_smses['telco'] == 'airtel'   && $raw_smses['status'] == 'accepted') {
                $token = '1';

                $payment = Payment::where('project_id',$project->id)->where('balance','>',$raw_smses['messagePart'])->where('status','like','success')->first();
                $payment->balance -= $payment->airtel * $raw_smses['messagePart']; //deduct airtel rate.
                $payment->save();
                //primary payment used
                $primary_payment = Payment::where(['project_id'=> Project::where('name','primary_project')->value('id'),'type' => 'promotional','telco' => 'airtel'])->where('balance','>',$raw_smses['messagePart'])->first();
                $primary_payment->balance -= $raw_smses['messagePart']; //deduct one csp unit
                $primary_payment->save();

                $raw_smses['credits'] = $payment->airtel * $raw_smses['messagePart']; //update real cost of message
                $raw_smses['from'] =  $airtel_promo_from; //update sender
                ProcessSendairtelbulk::dispatch($raw_smses, $token, $primary_payment->package_id);//->delay(Carbon::parse($request->sendAt));
            }
            //telkom
            elseif (($raw_smses['type'] == "transactional")  && ($telkom_trans_from !== null)  && $raw_smses['telco'] == 'telkom' && $raw_smses['status'] == 'accepted') {
                $token = '1';

                $payment = Payment::where('project_id',$project->id)->where('balance','>', $raw_smses['messagePart'])->where('status','like','success')->first();
                $payment->balance -= $payment->telkom * $raw_smses['messagePart']; //deduct telkom rate.
                $payment->save();
                //primary payment used
                $primary_payment = Payment::where(['project_id'=> Project::where('name','primary_project')->value('id'),'type' => 'transactional','telco' => 'telkom'])->where('balance','>',$raw_smses['messagePart'])->first();
                $primary_payment->balance -= $raw_smses['messagePart']; //deduct one csp unit
                $primary_payment->save();

                $raw_smses['credits'] = $payment->telkom * $raw_smses['messagePart']; //update real cost of message
                $raw_smses['from'] =  $telkom_trans_from; //update sender
                ProcessSendtelkombulk::dispatch($raw_smses, $token, $primary_payment->package_id)->onQueue('high');//->delay(Carbon::parse($request->sendAt));

            } elseif (($raw_smses['type'] == "promotional")  && ($telkom_promo_from !== null) && $raw_smses['telco'] == 'telkom'   && $raw_smses['status'] == 'accepted') { 
                $token = '1';
                $payment = Payment::where('project_id',$project->id)->where('balance','>',$raw_smses['messagePart'])->where('status','like','success')->first();
                $payment->balance -= $payment->telkom * $raw_smses['messagePart']; //deduct telkom rate.
                $payment->save();
                //primary payment used
                $primary_payment = Payment::where(['project_id'=> Project::where('name','primary_project')->value('id'),'type' => 'promotional', 'telco' => 'telkom'])->where('balance','>',$raw_smses['messagePart'])->first();
                $primary_payment->balance -= $raw_smses['messagePart']; //deduct one csp unit
                $primary_payment->save();

                $raw_smses['credits'] = $payment->telkom * $raw_smses['messagePart']; //update real cost of message
                $raw_smses['from'] =  $telkom_promo_from; //update sender
                ProcessSendtelkombulk::dispatch($raw_smses, $token, $primary_payment->package_id);//->delay(Carbon::parse($request->sendAt));
            }
            //ADD LOGIC FOR NO MATCHING SENDER_ID
            else {
                $sms = new Sms();
                $sms->cost = number_format((float) $raw_smses['messagePart'], 2);
                $sms->message_id = $raw_smses['messageId'];
                $sms->message_part =  $raw_smses['messagePart'];
                $sms->to = $raw_smses['to'];
                $sms->detail = 'noSenderFound';
                $sms->status = $raw_smses['status']."noSenderFound";
                $sms->status_code = $raw_smses['statusCode'];
                $sms->channel = 'sms';
                $sms->package_id = 'noSenderFound';
                $sms->project_id =   $raw_smses['project_id'];
                $sms->message =    $raw_smses['message'];
                $sms->from =   $raw_smses['from'];
                $sms->direction = "outbound";
                $sms->created_at =    $raw_smses['created_at'];
                $sms->sent_at = Carbon::now();
                $sms->campaign_id = array_key_exists('campaign_id',   $raw_smses) ?    $raw_smses['campaign_id'] : null;
                $sms->tz_id =   $raw_smses['messageId'];
                $sms->bulk_id =   $raw_smses['bulkId'];
                $sms->telco =   $raw_smses['telco'];
                $sms->save();
            }
        }
        return true;
    }
    protected function getToken()
    {
        $base_uri = config('app.sdp_base_uri'); //'https://dtsvc.safaricom.com:8480/api/';
        $sdp_username = config('app.sdp_username'); //'https://dtsvc.safaricom.com:8480/api/';
        $sdp_password = config('app.sdp_password'); //'https://dtsvc.safaricom.com:8480/api/';
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'auth/login', [
            'headers' => [
                'Content-Type'     => 'application/json',
                'Accept'     => 'application/json',
                'X-Requested-With'     => 'XMLHttpRequest',
            ],
            'json' => ['username' => $sdp_username, 'password' =>  $sdp_password]
        ]);
        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body; // Explicitly cast the body to a string
        //Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
       // Log::info('token stringbody' . $stringBody);
        $data = json_decode($stringBody, true);
        //Log::info($data);
        $token =  $data['token'];
        $msg =  $data['msg'];
        $refreshToken =  $data['refreshToken'];
        return $token;
    }

    public function cleanPhoneNumber($phone)
    {
        $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
        return $clean_number;
    }
    public function verifyPhoneNumber($phone)
    {
        return preg_match("/[^[+]?[0-9]{7-13}$/", $phone) ? true : false;
    }
    public function arrayHasDuplicates(array $input_array)
    {
        return count($input_array) === count(array_flip($input_array));
    }
    public function checkExcel($file)
    {
        $allowedfileExtension = ['xlsx', 'xls', 'csv'];
        //$filename = $image->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        $check = in_array($extension, $allowedfileExtension);
        if ($check) {
            return true;
        } else {
            return false;
        }
    }

}
