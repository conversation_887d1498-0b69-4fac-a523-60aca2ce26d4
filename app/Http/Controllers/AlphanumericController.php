<?php

namespace App\Http\Controllers;

use App\Alphanumeric;
use App\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\AppNotification;


class AlphanumericController extends Controller

{
    public function __construct()
    {
        // $this->authorizeResource(Alphanumeric::class, 'alphanumerics');
    }

    // Upload files/images
    public function checkSaveFile($file)
    {
        $allowedfileExtension = ['jpg', 'jpeg', 'png', 'docx', 'doc', 'pdf'];
        $extension = strtolower($file->getClientOriginalExtension());
        //$name = $file->getClientOriginalName();
        $check = in_array($extension, $allowedfileExtension);
        if ($check) {
            //return $file->storeAs( 'documents', $name );
            return $path = $file->store('public/sendernamekycdocs');
            return $path;
            // $extension = $request->file('avatar')->extension();
        } else {
            return null; //redirect('consumables/create')->withErrors('Only  jpeg or png images are allowed');
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        $this->authorize('viewAny', [Alphanumeric::class, $id]);
        $project = Project::find($id);
        $alphanumerics = Alphanumeric::where('project_id', $id)->orderBy('created_at', 'desc')->get();
        return view('alphanumerics.index', compact('alphanumerics', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Alphanumeric::class, $id]);
    }
    function cleanNumber($phone)
    {
        return  preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
    }

    private function e164Formatter254($phone)
    {
        $forced_numbber = substr($phone, -9);
        return '254' . $forced_numbber;
    }
    public function banNumber(Request $request, $id)
    {
        $request->validate([
            'mobile' => 'required|string',
            'project_id' => 'required|numeric',
            'sender_name' => 'required|string'
        ]);
        $this->authorize('viewAny', [Alphanumeric::class, $id]);
        $message = [];
        $cleanNumbersString = preg_replace('/\s+/', '', $request->mobile);
        $numbers = explode(",", $cleanNumbersString);
        Log::info($cleanNumbersString);
        foreach ($numbers as $number) {
            try {
                $project = Project::find($id);
                if ($request->add) {
                    DB::table('banned_numbers')->updateOrInsert([
                        'project_id' => $id,
                        'mobile' => $number,
                        'sender_name' => $request->sender_name,
                    ]);
                } else {
                    DB::table('banned_numbers')->where([
                        'project_id' => $id,
                        'mobile' => $number,
                        'sender_name' => $request->sender_name
                    ])->delete();
                }
            } catch (Exception $e) {
                Log::info($e->getMessage());
                $message[] = $number;
            }
        }
        $numbers = implode(",", DB::table('banned_numbers')->where('project_id', $project->id)->pluck('mobile')->toArray());

        return response()->json(['message' => 'success', 'numbers' => $numbers], 200);
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        $this->authorize('create', [Alphanumeric::class, $id]);

        $request->validate([
            'name' => 'required|unique:premia',
            'project_id' => 'required',
            'detail' => 'required',
            //'product' => 'required',
            //'offer_type' => 'required',
            //'rate' => 'required',
            'type' => 'required',
            //'company_name' => 'required',
            //'company_industry' => 'required',
            //'company_address' => 'required',
            //'company_email' => 'required',
            'application_letter' => 'required',
            //'company_cert' => 'required',
            //'company_kra' => 'required',
            //'company_representative_id' => 'required'
        ]);

        $application_letter = $request->file('application_letter');
        $company_cert = $request->file('company_cert');
        $company_kra = $request->file('company_kra');
        $company_representative_id = $request->file('company_representative_id');

        /*$request->merge(['code' => uniqid('sozalpha'),
        'application_letter' =>  $this->checkSaveFile($application_letter),
        'company_cert' =>  $this->checkSaveFile($company_cert),
        'company_kra' =>  $this->checkSaveFile($company_kra),
        'company_representative_id' =>  $this->checkSaveFile($company_representative_id)
         ]);*/
        $alphanumeric = new Alphanumeric();
        $alphanumeric->project_id =  $id;
        $alphanumeric->application_letter =  $this->checkSaveFile($application_letter);
        $alphanumeric->company_cert =  $this->checkSaveFile($company_cert);
        $alphanumeric->company_kra =  $this->checkSaveFile($company_kra);
        $alphanumeric->company_representative_id =  $this->checkSaveFile($company_representative_id);
        $alphanumeric->name =  $request->name;
        $alphanumeric->detail =  $request->detail;
        $alphanumeric->type =  $request->type;
        $alphanumeric->telco =  $request->telco;
        $alphanumeric->company_name =  $request->company_name;
        $alphanumeric->company_industry =  $request->company_industry;
        $alphanumeric->company_address =  $request->company_address;
        $alphanumeric->company_email =  $request->company_email;
        $alphanumeric->save();

        $project = Project::find($id)->name;
        //Mail user owning the project  ->send mail to default queue
        $emailTo = "<EMAIL>";
        $bcc = "<EMAIL>";
        $message = "Hi, someone just requested for a sender ID for project: " . $project;
        $subject = "New Sender ID request";
        Mail::to($emailTo)->bcc($bcc)->queue(new AppNotification($message, $subject));


        // Alphanumeric::create($request->all());
        $request->session()->flash('message', 'Ready to go! Alphanumeric Requested');
        //return redirect('projects/' . $id . '/alphanumerics');
        return redirect('projects/'.$id.'/sms/create');

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function show(Alphanumeric $alphanumeric)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function edit($pid, $id)
    {
        $alphanumeric = Alphanumeric::find($id);
        $this->authorize('update', $alphanumeric);

        $project = Project::find($pid);
        return view('alphanumerics.edit', compact('project', 'alphanumeric'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $pid, $id)
    {
        $alphanumeric = Alphanumeric::find($request->alphanumeric_id);
        $this->authorize('update', $alphanumeric);
        //$this->authorize('update', User::class, Alphanumeric::class);
        $project = Project::find($request->project_id);
        //$this->authorize('update', Auth::user(), $alphanumeric, $project);
        $request->validate([
            'name' => 'required|unique:premia',
            'project_id' => 'required',
            'alphanumeric_id' => 'required',
            'detail' => 'required',
            //'product' => 'required',
            // 'offer_type' => 'required',
            // 'rate' => 'required',
            'type' => 'required',
            //'company_name' => 'required',
            //'company_industry' => 'required',
            //'company_address' => 'required',
            //'company_email' => 'required',
            'application_letter' => 'required',
            //'company_cert' => 'required',
            //'company_kra' => 'required',
            //'company_representative_id' => 'required'
        ]);
        $application_letter = $request->file('application_letter');
        $company_cert = $request->file('company_cert');
        $company_kra = $request->file('company_kra');
        $company_representative_id = $request->file('company_representative_id');

        $request->merge([
            'application_letter' =>  $this->checkSaveFile($application_letter),
            'company_cert' =>  $this->checkSaveFile($company_cert),
            'company_kra' =>  $this->checkSaveFile($company_kra),
            'company_representative_id' =>  $this->checkSaveFile($company_representative_id)
        ]);


        $alphanumeric->update($request->except('alphanumeric_id'));
        $request->session()->flash('message', 'Alphanumeric Updated');
        return redirect('projects/' . $request->project_id . '/alphanumerics');
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $pid, $id)
    {
        //$this->authorize('delete', Alphanumeric::class);
        $alphanumeric = Alphanumeric::find($id);
        $alphanumeric->delete();
        $request->session()->flash('message', 'Alphanumeric: ' . $alphanumeric->name . ' deleted');
        return redirect('projects/' . $pid . '/alphanumerics');
    }
}
