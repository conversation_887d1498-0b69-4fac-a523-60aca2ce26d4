<?php

namespace App\Http\Controllers;

use App\Wtemplate;
use Illuminate\Http\Request;

class WtemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Wtemplate  $wtemplate
     * @return \Illuminate\Http\Response
     */
    public function show(Wtemplate $wtemplate)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wtemplate  $wtemplate
     * @return \Illuminate\Http\Response
     */
    public function edit(Wtemplate $wtemplate)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wtemplate  $wtemplate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wtemplate $wtemplate)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wtemplate  $wtemplate
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wtemplate $wtemplate)
    {
        //
    }
}
