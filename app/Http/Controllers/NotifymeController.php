<?php

namespace App\Http\Controllers;

use App\Notifyme;
use Illuminate\Http\Request;

class NotifymeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Notifyme  $notifyme
     * @return \Illuminate\Http\Response
     */
    public function show(Notifyme $notifyme)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Notifyme  $notifyme
     * @return \Illuminate\Http\Response
     */
    public function edit(Notifyme $notifyme)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Notifyme  $notifyme
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Notifyme $notifyme)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Notifyme  $notifyme
     * @return \Illuminate\Http\Response
     */
    public function destroy(Notifyme $notifyme)
    {
        //
    }
}
