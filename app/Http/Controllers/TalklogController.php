<?php

namespace App\Http\Controllers;

use App\Talklog;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TalklogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //
        $talklogs = Talklog::all();
        Log::info('User accessed logs' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User ".Auth::id()." accessed logs",
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMehod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);
       // $request->session()->flash('status', 'Accessing logs');
        return view('talklogs.index', compact('talklogs'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Talklog  $talklog
     * @return \Illuminate\Http\Response
     */
    public function show(Talklog $talklog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Talklog  $talklog
     * @return \Illuminate\Http\Response
     */
    public function edit(Talklog $talklog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Talklog  $talklog
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Talklog $talklog)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Talklog  $talklog
     * @return \Illuminate\Http\Response
     */
    public function destroy(Talklog $talklog)
    {
        //
    }
}
