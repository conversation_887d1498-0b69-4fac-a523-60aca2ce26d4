<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Symphony\Component\Process\Process;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Project;

use App\User;
use App\Sms;
use App\Models\SmsMonthlyCopy;
use App\Models\SmsCopy;
use App\Alphanumeric;
use App\Shortcode;
use App\Campaign;
use App\Contact;
use App\Models\ProjectEmail;
use App\Models\InteractiveSms;
use App\Models\Automation;
use App\Waccount;
use App\Wbusiness;
use App\Wtemplate;
use App\Wprofilepic;
use App\Wnumber;
use App\Wwebhook;
use App\Wappmessage;
use App\Models\Prompt;
use App\Payment;
use App\Models\Topup;
use App\Smscallback;
use Illuminate\Support\Facades\File;

class AdminController extends Controller
{
    //
    protected function getMemoryUsage(): float
    {
 /*       $fh = fopen('/proc/meminfo', 'r');
        $mem = 0;
        $all_str = '';

        while ($line = fgets($fh)) {
            $all_str .= $line;
        }
        fclose($fh);
        preg_match_all('/(\d+)/', $all_str, $pieces);
        $used = round($pieces[0][6] / $pieces[0][0], 2);
*/                                                   
        $mem = shell_exec("free | grep Mem | awk '{print $3/$2 * 100.0}'") ;
        $mem = preg_replace('/\n/', "", $mem);
        return $mem;
    }


    protected function getCPUUsagePercentage(): float
    {
        $cpu = shell_exec("grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {print usage}'");
        $cpu = preg_replace('/\n/', "", $cpu);
        return $cpu;
    }
    protected function getNginxStatus() {
        // $command = 'service apache2 status';
          if (str_contains('process->getOutput()', 'active (running)')) {
            return 'Nginx Up';
          }
          return 'Nginx Down';

    }

    protected function getDiskSize()
    {        // disk_total_space — Returns the total size of a filesystem or disk partition "/"
        $ds = disk_total_space("/");
        return number_format($ds/1000000000) ; //mb
    }
    protected function getDiskUsage()
    {
        //disk_free_space — Returns available space on filesystem or disk partition
        $dfs = (integer)disk_total_space("/") - (integer)disk_free_space('/');
        // $ds contains the total number of bytes available on "/"
        return number_format($dfs/1000000000 , 0) ; //mb
    }

    public function getAttempts()
    {
        $filePath = storage_path('logs/attempts.log');

        if (!File::exists($filePath)) {
            return  [
                'webCount' => 0,
                'apiCount' => 0,
            ];
        }

        // Get file contents
      $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

      $webCount = 0;
      $apiCount = 0;

      foreach ($lines as $line) {
          if (str_contains($line, 'WEB')) {
              $webCount++;
          }
          if (str_contains($line, 'API')) {
              $apiCount++;
          }
      }
        // Pass counts to the view
        return  [
          'webCount' => $webCount,
          'apiCount' => $apiCount,
      ];
    
    }


    public function index()
    {

        $cpu = $this->getCPUUsagePercentage();
        $memory = $this->getMemoryUsage();
        $diskUse = $this->getDiskUsage();
        $diskSize = $this->getDiskSize();
        $realtimeSmses = DB::table('sms')->whereDate('created_at', Carbon::today())->select('from', 'status','telco',DB::raw('count(*) as total'))->groupBy('from','status','telco')->get();

        ///

$startOfLastWeek = Carbon::now()->subWeek()->startOfWeek();
$endOfLastWeek = Carbon::now()->subWeek()->endOfWeek();
$startOfLastMonth = Carbon::now()->subMonth()->startOfMonth();
$endOfLastMonth = Carbon::now()->subMonth()->endOfMonth();
$startOfLastTwoMonths = Carbon::now()->subMonths(2)->startOfMonth();
$endOfLastTwoMonths = Carbon::now()->subMonths(2)->endOfMonth();
$startOfLastThreeMonths = Carbon::now()->subMonths(3)->startOfMonth();
$endOfLastThreeMonths = Carbon::now()->subMonths(3)->endOfMonth();

$usersTotal = User::count();
$usersThisWeek = User::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$usersLastWeek = User::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$usersThisMonth = User::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$usersLastMonth = User::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$usersLastTwoMonths = User::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$usersLastThreeMonths = User::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$projectsTotal = Project::count();
$projectsThisWeek = Project::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$projectsLastWeek = Project::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$projectsThisMonth = Project::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$projectsLastMonth = Project::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$projectsLastTwoMonths = Project::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$projectsLastThreeMonths = Project::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$smsToday = Sms::count();
$smsThisWeek = 0;//SmsMonthlyCopy::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$smsLastWeek = 0;//SmsMonthlyCopy::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$smsThisMonth = 0;//SmsMonthlyCopy::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
//$smsLastMonth = SmsCopy::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
//$smsLastTwoMonths = SmsCopy::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
//$smsLastThreeMonths = SmsCopy::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();
$alphanumericsTotal = Alphanumeric::count();
$alphanumericsThisWeek = Alphanumeric::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$alphanumericsLastWeek = Alphanumeric::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$alphanumericsThisMonth = Alphanumeric::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$alphanumericsLastMonth = Alphanumeric::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$alphanumericsLastTwoMonths = Alphanumeric::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$alphanumericsLastThreeMonths = Alphanumeric::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$shortcodesTotal = Shortcode::count();
$shortcodesThisWeek = Shortcode::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$shortcodesLastWeek = Shortcode::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$shortcodesThisMonth = Shortcode::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$shortcodesLastMonth = Shortcode::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$shortcodesLastTwoMonths = Shortcode::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$shortcodesLastThreeMonths = Shortcode::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$campaignsTotal = Campaign::count();
$campaignsThisWeek = Campaign::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$campaignsLastWeek = Campaign::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$campaignsThisMonth = Campaign::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$campaignsLastMonth = Campaign::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$campaignsLastTwoMonths = Campaign::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$campaignsLastThreeMonths = Campaign::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$contactsTotal = Contact::count();
$contactsThisWeek = 0; //Contact::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$contactsLastWeek = 0; //Contact::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$contactsThisMonth = 0; //Contact::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$contactsLastMonth = 0; //Contact::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$contactsLastTwoMonths = 0; //Contact::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$contactsLastThreeMonths = 0; //Contact::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$interactiveSmsTotal = InteractiveSms::count();
$interactiveSmsThisWeek = InteractiveSms::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$interactiveSmsLastWeek = InteractiveSms::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$interactiveSmsThisMonth = InteractiveSms::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$interactiveSmsLastMonth = InteractiveSms::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$interactiveSmsLastTwoMonths = InteractiveSms::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$interactiveSmsLastThreeMonths = InteractiveSms::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$smsCallbacksTotal = Smscallback::count();
$projectEmailsTotal = ProjectEmail::count();
$automationsTotal = Automation::count();
$waccountsTotal= Waccount::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$wbusinessesTotal = Wbusiness::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$wtemplatesTotal = Wtemplate::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$wprofilepicsTotal = Wprofilepic::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$wnumbersTotal = Wnumber::count();
$wwebhooksTotal = Wwebhook::count();
$wappmessagesTotal = Wappmessage::count();

$promptsTotal = Prompt::count();
$promptsThisWeek = Prompt::where('created_at', '>=', Carbon::now()->startOfWeek())->count();
$promptsLastWeek = Prompt::whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->count();
$promptsThisMonth = Prompt::where('created_at', '>=', Carbon::now()->startOfMonth())->count();
$promptsLastMonth = Prompt::whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->count();
$promptsLastTwoMonths = Prompt::whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->count();
$promptsLastThreeMonths = Prompt::whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->count();

$paymentsTotal = Payment::where('status', 'success')->sum('amount');
$paymentsThisWeek = Payment::where('status', 'success')->where('created_at', '>=', Carbon::now()->startOfWeek())->sum('amount');
$paymentsLastWeek = Payment::where('status', 'success') ->whereBetween('created_at', [$startOfLastWeek, $endOfLastWeek])->sum('amount');
$paymentsThisMonth = Payment::where('status', 'success')->where('created_at', '>=', Carbon::now()->startOfMonth())->sum('amount');
$paymentsLastMonth = Payment::where('status', 'success')->whereBetween('created_at', [$startOfLastMonth, $endOfLastMonth])->sum('amount');
$paymentsLastTwoMonths = Payment::where('status', 'success')->whereBetween('created_at', [$startOfLastTwoMonths, $endOfLastTwoMonths])->sum('amount');
$paymentsLastThreeMonths = Payment::where('status', 'success')->whereBetween('created_at', [$startOfLastThreeMonths, $endOfLastThreeMonths])->sum('amount');
//page visits, user activity
//billing low bal alerts, monthly budgets


        ///
        return view('admin.dashboard', with([
         'cpu' => $cpu,
         'memory' => $memory,
         'diskUse' => $diskUse,
         'diskSize' => $diskSize,
         'realtimeSmses' => $realtimeSmses,
         // Alerts last month
    'startOfLastWeek' => $startOfLastWeek,
    'endOfLastWeek' => $endOfLastWeek,
    'startOfLastMonth' => $startOfLastMonth,
    'endOfLastMonth' => $endOfLastMonth,
    'startOfLastTwoMonths' => $startOfLastTwoMonths,
    'endOfLastTwoMonths' => $endOfLastTwoMonths,
    'startOfLastThreeMonths' => $startOfLastThreeMonths,
    'endOfLastThreeMonths' => $endOfLastThreeMonths,

    // Users
    'usersTotal' => $usersTotal,
    'usersThisWeek' => $usersThisWeek,
    'usersLastWeek' => $usersLastWeek,
    'usersThisMonth' => $usersThisMonth,
    'usersLastMonth' => $usersLastMonth,
    'usersLastTwoMonths' => $usersLastTwoMonths,
    'usersLastThreeMonths' => $usersLastThreeMonths,

    'projectsTotal' => $projectsTotal,
    'projectsThisWeek' => $projectsThisWeek,
    'projectsLastWeek' => $projectsLastWeek,
    'projectsThisMonth' => $projectsThisMonth,
    'projectsLastMonth' => $projectsLastMonth,
    'projectsLastTwoMonths' => $projectsLastTwoMonths,
    'projectsLastThreeMonths' => $projectsLastThreeMonths,

    // SMS
    'smsToday' => $smsToday,
    'smsThisWeek' => $smsThisWeek,
    'smsLastWeek' => $smsLastWeek,
    'smsThisMonth' => $smsThisMonth,
    //'smsLastMonth' => $smsLastMonth,
    //'smsLastTwoMonths' => $smsLastTwoMonths,
    //'smsLastThreeMonths' => $smsLastThreeMonths,

    'alphanumericsTotal' => $alphanumericsTotal,
    'alphanumericsThisWeek' => $alphanumericsThisWeek,
    'alphanumericsLastWeek' => $alphanumericsLastWeek,
    'alphanumericsThisMonth' => $alphanumericsThisMonth,
    'alphanumericsLastMonth' => $alphanumericsLastMonth,
    'alphanumericsLastTwoMonths' => $alphanumericsLastTwoMonths,
    'alphanumericsLastThreeMonths' => $alphanumericsLastThreeMonths,

    'shortcodesTotal' => $shortcodesTotal,
    'shortcodesThisWeek' => $shortcodesThisWeek,
    'shortcodesLastWeek' => $shortcodesLastWeek,
    'shortcodesThisMonth' => $shortcodesThisMonth,
    'shortcodesLastMonth' => $shortcodesLastMonth,
    'shortcodesLastTwoMonths' => $shortcodesLastTwoMonths,
    'shortcodesLastThreeMonths' => $shortcodesLastThreeMonths,

    'campaignsTotal' => $campaignsTotal,
    'campaignsThisWeek' => $campaignsThisWeek,
    'campaignsLastWeek' => $campaignsLastWeek,
    'campaignsThisMonth' => $campaignsThisMonth,
    'campaignsLastMonth' => $campaignsLastMonth,
    'campaignsLastTwoMonths' => $campaignsLastTwoMonths,
    'campaignsLastThreeMonths' => $campaignsLastThreeMonths,

    'contactsTotal' => $contactsTotal,
    'contactsThisWeek' => $contactsThisWeek,
    'contactsLastWeek' => $contactsLastWeek,
    'contactsThisMonth' => $contactsThisMonth,
    'contactsLastMonth' => $contactsLastMonth,
    'contactsLastTwoMonths' => $contactsLastTwoMonths,
    'contactsLastThreeMonths' => $contactsLastThreeMonths,

    'interactiveSmsTotal' => $interactiveSmsTotal,
    'interactiveSmsThisWeek' => $interactiveSmsThisWeek,
    'interactiveSmsLastWeek' => $interactiveSmsLastWeek,
    'interactiveSmsThisMonth' => $interactiveSmsThisMonth,
    'interactiveSmsLastMonth' => $interactiveSmsLastMonth,
    'interactiveSmsLastTwoMonths' => $interactiveSmsLastTwoMonths,
    'interactiveSmsLastThreeMonths' => $interactiveSmsLastThreeMonths,

    'projectEmailsTotal' => $projectEmailsTotal,
    'automationsTotal' => $automationsTotal,
    'smsCallbacksTotal' => $smsCallbacksTotal,
    'waccountsTotal' => $waccountsTotal,
    'wbusinessesTotal' => $wbusinessesTotal,
    'wtemplatesTotal' => $wtemplatesTotal,
    'wprofilepicsTotal' => $wprofilepicsTotal,
    'wnumbersTotal' => $wnumbersTotal,
    'wwebhooksTotal' => $wwebhooksTotal,
    'wappmessagesTotal' => $wappmessagesTotal,
    

    // Prompts
    'promptsTotal' => $promptsTotal,
    'promptsThisWeek' => $promptsThisWeek,
    'promptsLastWeek' => $promptsLastWeek,
    'promptsThisMonth' => $promptsThisMonth,
    'promptsLastMonth' => $promptsLastMonth,
    'promptsLastTwoMonths' => $promptsLastTwoMonths,
    'promptsLastThreeMonths' => $promptsLastThreeMonths,

    // Payments
    'paymentsTotal' => $paymentsTotal,
    'paymentsThisWeek' => $paymentsThisWeek,
    'paymentsLastWeek' => $paymentsLastWeek,
    'paymentsThisMonth' => $paymentsThisMonth,
    'paymentsLastMonth' => $paymentsLastMonth,
    'paymentsLastTwoMonths' => $paymentsLastTwoMonths,
    'paymentsLastThreeMonths' => $paymentsLastThreeMonths,

        ]));
    }


    /*

public function resolveCpu(Process $process)
{
  $percentage = $this->getCPUUsagePercentage();
  $usage = round($percentage, 2);

  $message = "usage at {$usage}%";
  $thresholds = config('server-monitor.cpu_usage_threshold');

  if ($percentage >= $thresholds['fail']) {
    $this->check->fail($message);
    return;
  }

  if ($percentage >= $thresholds['warning']) {
    $this->check->warn($message);
    return;
  }

  $this->check->succeed($message);
}


public function resolveMemory(Process $process)
{
  $percentage = $this->getMemoryUsage();

  $message = "usage at {$percentage}%";
  $thresholds = config('server-monitor.memory_usage_threshold');

  if ($percentage >= $thresholds['fail']) {
    $this->check->fail($message);
    return;
  }

  if ($percentage >= $thresholds['warning']) {
    $this->check->warn($message);
    return;
  }

  $this->check->succeed($message);
}


*/
}
