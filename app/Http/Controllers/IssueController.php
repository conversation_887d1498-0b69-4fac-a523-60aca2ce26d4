<?php

namespace App\Http\Controllers;

use App\Issue;
use App\Talklog;
use App\Project;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class IssueController extends Controller
{
    public function checkSaveImage($image)
    {
        $allowedfileExtension = ['jpg', 'jpeg', 'png'];
        $extension = $image->getClientOriginalExtension();
        $check = in_array($extension, $allowedfileExtension);

        if ($check) {
            return $image->store('issues');
        } else {
            return back()->withErrors('Only jpeg or png images are allowed');
            // Sorry Only Upload png , jpg , doc
        }
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request,$pid)
    {
        //
        $this->authorize('viewAny', [Issue::class, $pid]);
        $project = Project::find($pid);
        $issues = Issue::where('project_id',$pid)->get();

        Log::info('User accessed issues' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Issue",
            'action' => "View",
            'message' => "User accessed issues" . Auth::id(),
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);
        return view('issues.index', compact('issues','project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request,$pid)
    {
        //
        $this->authorize('create', [Issue::class, $pid]);


        Log::info('User attempt create issues' . Auth::id());
        Talklog::create([
            'level' => 6,'resource' => "Issue",'action' => "Create", 'message' => "User attempted to create issue" . Auth::id(),
            'size' => "Issue",'user_id' => Auth::id(),'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),'clientRequestPath' => $request->path(),'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),'status' => "Success"
        ]);
       // $request->session()->flash('status', 'Accessing issues');
        return view('issues.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        //
        //$this->authorize('create', Issue::class);
        $this->authorize('create', [Issue::class, $id]);


        $issue = new Issue();
        $issue->fill( $request->all());
        $issue->project_id = $id;
        $path = "";
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $path = $this->checkSaveImage($file);
        }
        $issue->file =  $path;

        $issue->save();

        Log::info('User created issue' . Auth::id());
        Talklog::create([
            'level' => 6,'resource' => "Issue",'action' => "Create", 'message' => "User create issue" . Auth::id(),
            'size' => "Issue",'user_id' => Auth::id(),'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),'clientRequestPath' => $request->path(),'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),'status' => "Success"
        ]);


        //$request->session()->flash('status', 'All good! Issue created!');

        return back()->withMessage('All good! Message: ('.$issue->name.') submitted! We will get back ASAP');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Issue  $issue
     * @return \Illuminate\Http\Response
     */
    public function show(Issue $issue)
    {
        //b
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Issue  $issue
     * @return \Illuminate\Http\Response
     */
    public function edit($issueId)
    {
        //
        $this->authorize('update', $issueId);

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Issue  $issue
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Issue $issue,$pid)
    {
        //
        $this->authorize('update', [$issue, $pid]);

        $issue->update($request->all());
        Log::info('User created issue' . Auth::id());
        Talklog::create([
            'level' => 6,'resource' => "Issue",'action' => "update", 'message' => "User updated issue" . Auth::id(),
            'size' => "Issue",'user_id' => Auth::id(),'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),'clientRequestPath' => $request->path(),'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),'status' => "Success"
        ]);
        $request->session()->flash('message', 'Issue Updated');
        return redirect('issues');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Issue  $issue
     * @return \Illuminate\Http\Response
     */
    public function destroy($issueId, Request $request)
    {
        
       // $this->authorize('delete', $issue);
        //$issue->delete();

        Log::info('User deleting issue' . Auth::id());
        Talklog::create([
            'level' => 6,'resource' => "Issue",'action' => "delete", 'message' => "User ".Auth::id()." deleted issue" ,
            'size' => "Issue",'user_id' => Auth::id(),'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),'clientRequestPath' => $request->path(),'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),'status' => "Success"
        ]);

        $request->session()->flash('message', 'Error');
        return back();

    }
}
