<?php

namespace App\Http\Controllers;

use App\Wnumber;
use Illuminate\Http\Request;

class WnumberController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Wnumber  $wnumber
     * @return \Illuminate\Http\Response
     */
    public function show(Wnumber $wnumber)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wnumber  $wnumber
     * @return \Illuminate\Http\Response
     */
    public function edit(Wnumber $wnumber)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wnumber  $wnumber
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wnumber $wnumber)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wnumber  $wnumber
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wnumber $wnumber)
    {
        //
    }
}
