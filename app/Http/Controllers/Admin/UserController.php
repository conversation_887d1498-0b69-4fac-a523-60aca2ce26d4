<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\User;

class UserController extends Controller
{
    //
        /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        //$this->authorize('viewAny', [User::class, $id]);

        $users = User::paginate('100');

        return view('admin.users', compact('users'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        $this->authorize('create', User::class);

        return view('users.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $this->authorize('create', User::class);

       // $user = User::create($request->all());
       // $request->session()->flash('message', 'User ' . $user->fname . ' Created');
        return redirect('users');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
        $user = User::find($id);

        $this->authorize('update', $user);

        return view('users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,User $user)
    {
       // $user = User::find($request->user_id);
        $this->authorize('update', $user);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->password = $request->password;
        $user->address = $request->address;
        $user->postal_code = $request->postal_code;

        $user->city = $request->city;
        $user->country = $request->country;
        $user->multiFactor = $request->filled('multiFactor') ? true : false;
        $user->isOfficer = $request->filled('isOfficer') ? true : false;
        $user->isManager = $request->filled('isManager') ? true : false;
        $user->isAdmin = $request->filled('isAdmin') ? true : false;
        $user->updatedBy = $request->user()->id;
        //otp....
        $user->avatar = $request->avatar;
        $user->about = $request->about;
        $user->save();

        $request->session()->flash('message', 'User ' . $request->name . ' Updated');
        return back();

        // return redirect('dashboard');
    }
        /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user, Request $request)
    {
        //

        $this->authorize('delete', $user);

        $user->delete($request->all());
        $request->session()->flash('message', 'User Deleted');
        return redirect('users');
    }
}
