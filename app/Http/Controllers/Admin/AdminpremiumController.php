<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Premium;
use App\Project;

class AdminpremiumController extends Controller
{
    //
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Premium::class]);


        $premia = Premium::orderBy('created_at','desc')->get();
        return view('admin.premia.index', compact('premia'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Premium::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Premium::class]);

        $request->validate([
            'name' => 'required|unique:premiums',
          //  'project_id' => 'required',
           // 'premium_id' => 'required'
        ]);

        $project_id = Project::where('name','like',$request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Premium::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Premium Created');
        return redirect('admin/premiums');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function show(Premium $premium)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $premium = Premium::find($id);
        $project = Project::find($premium->project_id);

        $this->authorize('update', $premium);

        return view('admin.premia.edit', compact('project','premium'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Premium $premium)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'premium_id' => 'required'
        ]);


        $premium = Premium::find($request->premium_id);
        $premium->fill($request->except('premium_id'));
        $premium->save();

        $request->session()->flash('message','Premium Updated');
        return redirect('admin/premia');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $premium = Premium::find($id);
      $this->authorize('delete', $premium);

      $premium->delete();
      $request->session()->flash('message','Premium: '.$premium->name.' deleted');
      return redirect('admin/premiums');
    }
}
