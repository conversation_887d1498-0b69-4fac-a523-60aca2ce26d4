<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Project;
use App\Collaboration;
use Illuminate\Support\Facades\DB;
use App\User;
use App\Policies\ProjectPolicy;
use App\Talklog;
use Illuminate\Support\Facades\Log;
use App\Paymentmethod;
use App\Paymentstatus;
use App\Payment;
use App\Campaign;
class ProjectController extends Controller
{
    //


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $this->authorize('viewAny', Project::class);
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 300); //default=30.You can not change this setting with ini_set() when running in safe mode
        set_time_limit(300); //same as above
        ini_set('default_socket_timeout', '-1');
        $collabo_projects_ids = Collaboration::where('user_id', Auth::id())->where('status', '=', 1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->latest()->get(); //doesnt apply to collabo ones. for collabo
        $projects = Project::where('isArchived', '=', 0)->latest()->get();
        $archivedProjects = Project::where('isArchived', '=', 1)->latest()->get();
        return view('admin.projects.index', compact('collabo_projects', 'projects', 'archivedProjects'));
    }

    public function historyProjects()
    {
        $this->authorize('viewAny', Project::class);
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 300); //default=30.You can not change this setting with ini_set() when running in safe mode
        set_time_limit(300); //same as above
        ini_set('default_socket_timeout', '-1');
       $projects = Project::where('isArchived', '=', 0)->latest()->get();
      $createdYear = date('Y');
      $createdMonth = date('m');
      $currentDate = date('Y-m');

        $projects_sms = DB::table('sms_copy')
        ->where('created_at', 'LIKE', $createdYear.'%')
        ->select(
            DB::raw("
            project_id,telco,
            sum(price) as price,
            sum(message_part) as sms,
            YEAR(created_at) year,
            MONTH(created_at) month"),
        )
            ->groupBy('project_id', 'year','month', 'telco')
            ->get(); 

        $createdMonth = date("F", mktime(0, 0, 0, $createdMonth, 10)) ;
        return view('admin.projects.history', compact( 'projects', 'projects_sms')); 
    }

    public function monthlyHistoryProjects()
    {
        $this->authorize('viewAny', Project::class);
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 300); //default=30.You can not change this setting with ini_set() when running in safe mode
        set_time_limit(300); //same as above
        ini_set('default_socket_timeout', '-1');
        $projects = Project::where('isArchived', '=', 0)->latest()->get();
      $currentDate = date('Y-m');

        $projects_sms = DB::table('sms_monthly_copy')
        ->select(
            DB::raw("
            project_id,telco,
            sum(price) as price,
            sum(message_part) as sms,
            YEAR(created_at) year,
            MONTH(created_at) month"),
        )
            ->groupBy('project_id', 'year','month', 'telco')
            ->get();
        return view('admin.projects.history', compact( 'projects', 'projects_sms')); 
    }
 
    public function archive(Request $request, $id)
    {
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $project->isArchived = $project->isArchived === 1 ? 0 : 1;
        $project->save();
        return redirect('projects')->with('status', 'Project Updated');
    }
    /**
     * Update the specified user.
     *
     * @param  Request  $request
     * @param  string  $id
     * @return Response
     */
    public function hatewords(Request $request, $id)
    {
        $project = Project::find($request->project_id);

        $this->authorize('update', $project);

        $project->hatewords = implode(',', explode('\r\n', $request->hatewords));
        $project->hatewordsActive = $request->filled('hatewordsActive') ? true : false;
        $project->save();
        return back()->with('status', 'Hate words updated');
    }
    private function calculateCredits($amount)
    {


        $PLATINUM = config('app.PLATINUM'); //0.4
        $DIAMOND = config('app.DIAMOND'); //0.6
        $GOLD = config('app.GOLD'); //0.8
        $RUBY = config('app.RUBY'); //1.0
        $SILVER = config('app.SILVER'); //1.2
        $BRONZE = config('app.BRONZE'); //1.4
        $GEM = config('app.GEM'); //1.6

        if ($amount >=  $PLATINUM) { //amount category
            return $amount / 0.4;           //pick the project's rate for this bracket. so rate is in project. while Category is in Global
        } elseif ($amount >= $DIAMOND) {
            return $amount / 0.6;
        } elseif ($amount >=  $GOLD) {
            return  $amount / 8.0;
        } elseif ($amount >=  $RUBY) {
            return  $amount / 1.0;
        } elseif ($amount >= $SILVER) {
            return  $amount / 1.2;
        } elseif ($amount >= $BRONZE) {
            return  $amount / 1.4;
        } else {
            return  $amount / 1.6;
        }
    }

    public function balAlert(Request $request)
    {
        $project = Project::find($request->project_id);

        $this->authorize('update', $project);

        $request->validate([
            'project_id' => "required",
            'min_balance' => "required",
            'min_balance_tel' => 'required',
            'min_balance_email' => 'required'
        ]);
        $project->min_balance = $request->min_balance;
        $project->min_balance_tel = $request->min_balance_tel;
        $project->min_balance_email = $request->min_balance_email;
        $project->save();


        Log::info('User updated min_balance projects dashboard' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User updated min_balance" . Auth::id(),
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        //copy the log code/ add log and talklog classes to class, editcode to reflect resource

        $request->session()->flash('message', 'Min Balance updated');
        return back();
    }

    public function budget(Request $request)
    {
        $project = Project::find($request->project_id);

        $this->authorize('update', $project);

        $request->validate([
            'project_id' => "required",
            'max_credit' => "required",
        ]);
        $project->max_credit = $request->max_credit;
        $project->save();


        Log::info('User updated monthly budget for project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User updated min_balance" . Auth::id(),
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        //copy the log code/ add log and talklog classes to class, editcode to reflect resource

        $request->session()->flash('message', 'Monthly Budget updated');
        return back();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {

        $this->authorize('create');
        return view('projects.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $this->authorize('create');
        if (Project::where('name', 'like', $request->name . "%")->exists()) {
            return back()->withErrors('That project name is already in use. Select a new name to make your project unique');
        }
        $free_trial_amount = config('app.free_trial_amount'); //20

        $amount = $free_trial_amount; //kes
        $credits = $this->calculateCredits($amount); //new project reward

        $manage_p_project = Project::where('name', 'primary_project')->first();
        //dd(User::find(Auth::id())->projects()->count() >= 2);
        if ($manage_p_project->credits < $credits) {
            Log::info('Fail: primary project has insufficient credits to issue new projects. ' . Auth::id());
            return back()->withErrors('Project could not be started successfully. Please contact support.');
        }
        if (User::find(Auth::id())->projects()->count() >= 2) {
            Log::info('More than 2 projects. No issue of free credits. ' . Auth::id());
            $amount = 0; //kes
            $credits = 0; //new project reward
        }

        $manage_p_project->credits -=  $credits;
        $manage_p_project->save();

        $project = new Project();
        $project->fill($request->all());
        $project->user_id = Auth::id();
        $project->code = uniqid('Tzpr');
        $project->credits = $credits;
        $project->code = uniqid('Tzpr');
        $project->isActive = true;
        $project->isArchived = false;
        $project->save();

        if ($amount != 0) {
            $payment = new payment();
            //$payment->transaction_id =  $transaction_id;
            $payment->txRef =  uniqid('P2O'); //unique internal ID
            $payment->project_id =  $project->id;
            $payment->resp_respmsg =  'Primary to Other';
            $payment->amount  =  $amount;
            $payment->credits  =  $credits;
            $payment->currency  =  'kes';
            $payment->detail  =  'Primary to Other';
            $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'trial' . '%')->exists() ?
                Paymentmethod::where('name', 'like', 'trial' . '%')->value('id') :
                Paymentmethod::insertGetId(['name' => 'trial', 'detail' => 'trial', 'created_by' => Auth::id(),]);
            $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'completed' . '%')->exists() ?
                Paymentstatus::where('name', 'like', 'completed' . '%')->value('id') :
                Paymentstatus::insertGetId(['name' => 'completed', 'detail' => 'completed', 'created_by' => Auth::id(),]);
            $payment->save();
        }
        Log::info('User created a new project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User " . Auth::id() . " created a new project:" . $project->name,
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        $request->session()->flash('message', 'Success. Project Created.');
        return redirect('projects');
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function show(Project $project)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function edit(Project $project)
    {

        $this->authorize('update', $project);
        $campaigns = Campaign::where('project_id', $project->id)->get();
        return view('projects.new-edit', compact('project','campaigns'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Project $project)
    {
$request->validate([
    'account_type' => 'required|string',
    'details' => 'required|regex:/\d+\.\d{1,2},\d+\.\d{1,2},\d+\.\d{1,2}/',
]);

        if ($project->name == "primary_project") {
            return response()->json(['status' => 'error'], 401);
        }
        if (!User::find(Auth::id())->isGlobalAdmin) {
            Log::info('Unauthorized');
            return response()->json(['status' => 'error'], 401);
        }
       // $this->authorize('update', $project);
        //$project->update(['details' => $request->details, 'account_type' => $request->account_type]);
        $project = Project::where('id', $request->project_id)->first();
        $project->details =  $request->details;
        $project->account_type = $request->account_type;
        $project->save();
        Log::info($project);

        Log::info('User updated a project' . Auth::id());
        return response()->json(['status' => 'success','details' => $project->details, 'accountType'=>$project->account_type ], 200);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {

        $project = Project::find($request->project_id);

        $this->authorize('delete', $project);

        if ($project->name == "primary_project") {
            return back()->with('status', 'Action not Allowed');
        }
        $this->authorize('delete', $project);
        $project->delete();

        Log::info('User deleted a project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User " . Auth::id() . "deleted a project" . $project->name,
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        $request->session()->flash('message', 'Project Deleted');
        return redirect('projects');
    }
}
