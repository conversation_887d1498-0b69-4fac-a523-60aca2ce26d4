<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Keyword;
use App\Project;

class AdminkeywordController extends Controller
{
    //
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Keyword::class]);


        $keywords = Keyword::orderBy('created_at','desc')->get();
        return view('admin.keywords.index', compact('keywords'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Keyword::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Keyword::class]);

        $request->validate([
            'name' => 'required|unique:keywords',
          //  'project_id' => 'required',
           // 'keyword_id' => 'required'
        ]);

        $project_id = Project::where('name','like',$request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Keyword::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Keyword Created');
        return redirect('admin/keywords');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function show(Keyword $keyword)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $keyword = Keyword::find($id);
        $project = Project::find($keyword->project_id);

        $this->authorize('update', $keyword);

        return view('admin/keywords.edit', compact('project','keyword'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Keyword $keyword)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'keyword_id' => 'required'
        ]);


        $keyword = Keyword::find($request->keyword_id);
        $keyword->fill($request->except('keyword_id'));
        $keyword->save();

        $request->session()->flash('message','Keyword Updated');
        return redirect('admin/keywords');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $keyword = Keyword::find($id);
      $this->authorize('delete', $keyword);

      $keyword->delete();
      $request->session()->flash('message','Keyword: '.$keyword->name.' deleted');
      return redirect('admin/keywords');
    }
}
