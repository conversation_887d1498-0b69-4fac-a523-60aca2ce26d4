<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Shortcode;
use App\Project;

class AdminshortcodeController extends Controller
{
    //
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Shortcode::class]);


        $shortcodes = Shortcode::orderBy('created_at','desc')->get();
        return view('admin.shortcodes.index', compact('shortcodes'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Shortcode::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Shortcode::class]);

        $request->validate([
            'name' => 'required|unique:shortcodes',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);

        $project_id = Project::where('name','like',$request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Shortcode::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Shortcode Created');
        return redirect('admin/shortcodes');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function show(Shortcode $shortcode)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $shortcode = Shortcode::find($id);
        $project = Project::find($shortcode->project_id);

        $this->authorize('update', $shortcode);

        return view('admin/shortcodes.edit', compact('project','shortcode'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Shortcode $shortcode)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);


        $shortcode = Shortcode::find($request->shortcode_id);
        $shortcode->fill($request->except('shortcode_id'));
        $shortcode->save();

        $request->session()->flash('message','Shortcode Updated');
        return redirect('admin/shortcodes');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $shortcode = Shortcode::find($id);
      $this->authorize('delete', $shortcode);

      $shortcode->delete();
      $request->session()->flash('message','Shortcode: '.$shortcode->name.' deleted');
      return redirect('admin/shortcodes');
    }
}
