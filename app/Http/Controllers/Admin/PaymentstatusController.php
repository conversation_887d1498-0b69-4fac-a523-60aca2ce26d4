<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Paymentstatus;
use Illuminate\Http\Request;

class PaymentstatusController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $paymentstatuses = Paymentstatus::all();
        return view('admin.paymentstatuses',compact('paymentstatuses'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $request->validate([
            'name' => 'required|unique:alphanumerics',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);

        Paymentstatus::create($request->all());

        $request->session()->flash('message','Ready to go! Payment Status Created');
        return redirect('admin/paymentstatuses');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Paymentstatus  $paymentstatus
     * @return \Illuminate\Http\Response
     */
    public function show(Paymentstatus $paymentstatus)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Paymentstatus  $paymentstatus
     * @return \Illuminate\Http\Response
     */
    public function edit(Paymentstatus $paymentstatus)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Paymentstatus  $paymentstatus
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Paymentstatus $paymentstatus)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Paymentstatus  $paymentstatus
     * @return \Illuminate\Http\Response
     */
    public function destroy(Paymentstatus $paymentstatus)
    {
        //
    }
}
