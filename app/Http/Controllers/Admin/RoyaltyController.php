<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;

use App\Royalty;
use Illuminate\Http\Request;

class RoyaltyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $royalties = Royalty::all();
        return view('admin.royalties', compact('royalties'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $request->validate([
            'name' => 'required|unique:alphanumerics',
        ]);

        Royalty::create($request->all());

        $request->session()->flash('message','Ready to go! Royalty Created');
        return redirect('admin/plans');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Royalty  $royalty
     * @return \Illuminate\Http\Response
     */
    public function show(Royalty $royalty)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Royalty  $royalty
     * @return \Illuminate\Http\Response
     */
    public function edit(Royalty $royalty)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Royalty  $royalty
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Royalty $royalty)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Royalty  $royalty
     * @return \Illuminate\Http\Response
     */
    public function destroy(Royalty $royalty)
    {
        //
    }
}
