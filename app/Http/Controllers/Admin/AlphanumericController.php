<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Alphanumeric;

use App\Project;
class AlphanumericController extends Controller
{
    //
        //
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Alphanumeric::class]);


        $alphanumerics = Alphanumeric::orderBy('created_at','desc')->get();
        return view('admin.alphanumerics.index', compact('alphanumerics'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Alphanumeric::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Alphanumeric::class]);

        $request->validate([
            'name' => 'required',

          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);

        $project_id = Project::where('name', 'like', $request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Alphanumeric::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Alphanumeric Created');
        return redirect('admin/alphanumerics');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function show(Alphanumeric $alphanumeric)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $alphanumeric = Alphanumeric::find($id);
        $project = Project::find($alphanumeric->project_id);

        $this->authorize('update', $alphanumeric);

        return view('admin.alphanumerics.edit', compact('project','alphanumeric'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Alphanumeric $alphanumeric)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);


       $alphanumeric =  Alphanumeric::find($request->alphanumeric_id);

        $this->authorize('update', $alphanumeric);
        //add logic to deduct money + tax from payments and send email notification
        //if paid == yes ... deduct from payments
        $alphanumeric->update($request->except('alphanumeric_id'));
        $request->session()->flash('message','Alphanumeric Updated');
        return redirect('admin/alphanumerics');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Alphanumeric  $alphanumeric
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $alphanumeric = Alphanumeric::find($id);
      $this->authorize('delete', $alphanumeric);

      $alphanumeric->delete();
      $request->session()->flash('message','Alphanumeric: '.$alphanumeric->name.' deleted');
      return redirect('admin/alphanumerics');
    }
}
