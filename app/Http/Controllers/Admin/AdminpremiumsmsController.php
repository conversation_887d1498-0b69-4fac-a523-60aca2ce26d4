<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Premiumsms;
use App\Project;

class AdminpremiumsmsController extends Controller
{
    //
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Premiumsms::class]);
        $premiumsmses = Premiumsms::orderBy('created_at','desc')->get();
        return view('admin.premiumsms.index', compact('premiumsmses'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Premiumsms::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Premiumsms::class]);

        $request->validate([
            'name' => 'required|unique:premiumsmses',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);

        $project_id = Project::where('name','like',$request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Premiumsms::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Premiumsms Created');
        return redirect('admin/premiumsmses');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function show(Premiumsms $premiumsms)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $premiumsms = Premiumsms::find($id);
        $project = Project::find($premiumsms->project_id);

        $this->authorize('update', $premiumsms);

        return view('admin/premiumsmses.edit', compact('project','premiumsms'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Premiumsms $premiumsms)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);


        $premiumsms = Premiumsms::find($request->shortcode_id);
        $premiumsms->fill($request->except('shortcode_id'));
        $premiumsms->save();

        $request->session()->flash('message','Premiumsms Updated');
        return redirect('admin/premiumsmses');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $premiumsms = Premiumsms::find($id);
      $this->authorize('delete', $premiumsms);

      $premiumsms->delete();
      $request->session()->flash('message','Premiumsms: '.$premiumsms->name.' deleted');
      return redirect('admin/premiumsmses');
    }
}
