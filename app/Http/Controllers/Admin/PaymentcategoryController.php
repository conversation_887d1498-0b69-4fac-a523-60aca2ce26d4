<?php

namespace App\Http\Controllers\Admin;

use App\Paymentcategory;
use App\Http\Controllers\Controller;

use Illuminate\Http\Request;

class PaymentcategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $paymentcategories = Paymentcategory::all();
        return view('admin.paymentcategories',compact('paymentcategories'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $request->validate([
            'name' => 'required|unique:alphanumerics',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);

        Paymentcategory::create($request->all());

        $request->session()->flash('message','Ready to go! Payment Category Created');
        return redirect('admin/paymentcategories');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Paymentcategory  $paymentcategory
     * @return \Illuminate\Http\Response
     */
    public function show(Paymentcategory $paymentcategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Paymentcategory  $paymentcategory
     * @return \Illuminate\Http\Response
     */
    public function edit(Paymentcategory $paymentcategory)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Paymentcategory  $paymentcategory
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Paymentcategory $paymentcategory)
    {
        //
        $request->validate([
            'name' => 'required|unique:alphanumerics',
          //  'project_id' => 'required',
           // 'shortcode_id' => 'required'
        ]);

        $paymentcategory::update($request->all());

        $request->session()->flash('message','Ready to go! Payment Category updated');
        return redirect('admin/paymentcategories');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Paymentcategory  $paymentcategory
     * @return \Illuminate\Http\Response
     */
    public function destroy(Paymentcategory $paymentcategory)
    {
        //
    }
}
