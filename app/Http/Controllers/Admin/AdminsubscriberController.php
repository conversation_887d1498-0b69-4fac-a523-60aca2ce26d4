<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Subscriber;
use App\Project;

class AdminsubscriberController extends Controller
{
    //
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Subscriber::class]);


        $subscribers = Subscriber::orderBy('created_at','desc')->get();
        return view('admin.subscribers.index', compact('subscribers'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Subscriber::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Subscriber::class]);

        $request->validate([
            'name' => 'required|unique:subscribers',
          //  'project_id' => 'required',
           // 'subscriber_id' => 'required'
        ]);

        $project_id = Project::where('name','like',$request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Subscriber::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Subscriber Created');
        return redirect('admin/subscribers');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Subscriber  $subscriber
     * @return \Illuminate\Http\Response
     */
    public function show(Subscriber $subscriber)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Subscriber  $subscriber
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $subscriber = Subscriber::find($id);
        $project = Project::find($subscriber->project_id);

        $this->authorize('update', $subscriber);

        return view('admin.subscribers.edit', compact('project','subscriber'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Subscriber  $subscriber
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Subscriber $subscriber)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'subscriber_id' => 'required'
        ]);


        $subscriber = Subscriber::find($request->subscriber_id);
        $subscriber->fill($request->except('subscriber_id'));
        $subscriber->save();

        $request->session()->flash('message','Subscriber Updated');
        return redirect('admin/subscribers');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Subscriber  $subscriber
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $subscriber = Subscriber::find($id);
      $this->authorize('delete', $subscriber);

      $subscriber->delete();
      $request->session()->flash('message','Subscriber: '.$subscriber->name.' deleted');
      return redirect('admin/subscribers');
    }
}
