<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Project;
use App\Payment;
use App\Paymentstatus;
use Illuminate\Support\Facades\Auth;
use App\Paymentmethod;
use App\Enrollment;
use Illuminate\Support\Carbon;
use App\Plan;
use Illuminate\Support\Facades\DB;
use App\Traits\CalculateCredits;
use Illuminate\Support\Facades\Log;
use App\Jobs\PostLedgers;


class PaymentController extends Controller
{
    //
    use CalculateCredits;
    public function store(Request $request)
    {
        $request->validate([
            'package_id' => 'required',
            'units' => 'required',
            'amount' => 'required'
        ]);

        $credits = $request->units; // WE DO NOT DO $this->CalculateCredits($request->amount);
        $project = Project::where('name', 'primary_project')->first();
        $project->credits +=  $credits;
        $project->save();

        $payment = DB::table('payments')->where(['project_id'=>1,'telco'=> $request->telco,'status'=>'success','type'=> $request->type])->exists() ? 
        Payment::where(['project_id'=>1,'telco'=> $request->telco,'status'=>'success','type'=> $request->type])->first() : new Payment();
        $current_units = $payment->balance ? $payment->balance : 0 ;
        //$payment->transaction_id =  $transaction_id;
        $payment->txRef =  uniqid('P2O'); //unique internal ID
        $payment->project_id =  $project->id;
        $payment->resp_respmsg =  'Primary Reload';
        $payment->amount  =  $request->amount;//amount paid in total
        $payment->balance  =  $request->units + $current_units;
        $payment->credit = "cash";
        $payment->debit = "units";
        $payment->unit_cost =  $request->amount / $request->units;
        $payment->package_id = $request->package_id;
        $payment->expiry = $request->expiry;
        $payment->date = Carbon::now();
        $payment->currency  =  'kes';
        $payment->credits  =  $credits;//will be used to track original amount
        $payment->telco = $request->telco; //WHICH TELCO IS THIS RELOAD DFOR
        $payment->detail  =  'Primary Reload';
        $payment->status =  'success';
        $payment->type =  $request->type; 

        $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'primary' . '%')->exists() ?
            Paymentmethod::where('name', 'like', 'primary' . '%')->value('id') :
            Paymentmethod::insertGetId(['name' => 'primary', 'detail' => 'primary', 'created_by' => Auth::id(),]);
        $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'completed' . '%')->exists() ?
            Paymentstatus::where('name', 'like', 'completed' . '%')->value('id') :
            Paymentstatus::insertGetId(['name' => 'completed', 'detail' => 'completed', 'created_by' => Auth::id(),]);
        $payment->save();

        PostLedgers::dispatch("units", $project->name,"purchases","debit",$request->amount,16.00 , "inclusive");
        PostLedgers::dispatch("payable", $project->name,"purchases","credit",$request->amount,16.00 , "inclusive");
        Log::info("accounts dispatched \n");

        PostLedgers::dispatch("cash", $project->name,"purchases","credit",$request->amount,16.00 , "inclusive");
        PostLedgers::dispatch("payable", $project->name,"purchases","debit",$request->amount,16.00 , "inclusive");
        Log::info("accounts dispatched \n");
        return back()->with('status', 'Primary Account Succefully Reloaded with ' . $credits . ' credits');
    }

    public function index()
    {
        $payments = Payment::all();
        return view('admin.payments', compact('payments'));
    }

    public function manualpay(Request $request)
    {
        $request->validate([
            'project_name' => 'required',
            'project_code' => 'required'
        ]);
        $enrollment_plan_id = $request->filled('enrollment_plan_id') ? $request->enrollment_plan_id : null;
        $project = Project::where('code', 'like', $request->project_code)
            ->where('name', 'like', $request->project_name)
            ->first();
        if($request->filled('rate') ) {
            $payload =  explode(',' ,$request->rate);
            if(!isset( $payload [0]) || !isset( $payload [1]) || !isset( $payload [2]) ) {
                return back()->withErrors('Rate incorrect. Leave empty to apply normal platform rate or use the format 0.5,0.4,3  to apply safcom, airtel and telkom rates respectively');
            }
        }
        if (!$project) {
            return back()->withErrors('Confirm the project details then try again');
        }
        $amount = $request->filled('enrollment_plan_id') ? Plan::find($enrollment_plan_id)->price_monthly :  $request->amount;
        if ($request->filled('enrollment_plan_id')) {
            if ($request->amount !==  Plan::find($enrollment_plan_id)->price_monthly) {
                return back()->withErrors('When creating a manual subscription, ensure Configured Subscription amount is equal to the Entered amount');
            }
        }
        $payment = new payment();
        $code =  uniqid('tzmn');;
        $payment->txRef = $code; //unique internal ID
        $payment->name = $code;
        $payment->project_id =  $project->id;
        $payment->tx_customer_phone =   $request->mobile;
        $payment->tx_orderRef =   $payment->txRef;
        //$payment->respcode =  $result['ResponseCode'];
        $payment->resp_respmsg =  'manual payment registration'; 
        $payment->amount  =  $amount;
        $payment->currency  =  'kes';
        $payment->tx_status = "completed"; 
        $payment->unit_cost = 0;
        $payment->credit = "units";
        $payment->debit = "cash";
        $payment->balance = $amount;//$credits;
        $payment->credits = $amount;//$credits;
        //$payment->safaricom = $this->CalculateSafaricomCredits($amount);
        //$payment->airtel = $this->CalculateAirtelCredits($amount);
       // $payment->telkom = $this->CalculateTelkomCredits($amount);
       //replaced the above to capture rate if provided, else calculate as normal
       Log::info($request->rate);
       $payment->safaricom = $request->filled('rate') ? explode(',' ,$request->rate)[0] : ( $project->details ? explode(',' ,$project->details)[0] :$this->CalculateSafaricomCredits($amount));
       $payment->airtel = $request->filled('rate') ? explode(',' ,$request->rate)[1] : ( $project->details ? explode(',' ,$project->details)[1] :$this->CalculateAirtelCredits($amount));
       $payment->telkom = $request->filled('rate') ? explode(',' ,$request->rate)[2] : ( $project->details ? explode(',' ,$project->details)[2] :$this->CalculateTelkomCredits($amount));
       $payment->international = $this->CalculateInternationalCredits($amount);
       $payment->other = $this->CalculateOtherCredits($amount);
       $payment->status =  $request->status;//success, new or pending
        $payment->detail  =  is_null($enrollment_plan_id) ? 'manual recharge' : 'manual enrollment'; //enrollment
        $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'manual' . '%')->exists() ?
            Paymentmethod::where('name', 'like', 'manual' . '%')->value('id') :
            Paymentmethod::insertGetId(['name' => 'manual', 'detail' => 'manual', 'created_by' => Auth::id(),]);
        $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'new' . '%')->exists() ?
            Paymentstatus::where('name', 'like', 'new' . '%')->value('id') :
            Paymentstatus::insertGetId(['name' => 'new', 'detail' => 'new', 'created_by' => Auth::id(),]);
        $payment->save();
        /*
        //create enrollment if necessary
        if (!is_null($enrollment_plan_id) && $enrollment_plan_id > 0) {
            //if enrollment for this project exists, update it
            $enrollment = new Enrollment();
            $enrollment->user_id = Auth::id();
            $enrollment->project_id = $project->id;
            $enrollment->plan_id = $enrollment_plan_id;
            $enrollment->payment_id = $payment->id;
            $enrollment->start = Carbon::now();
            $enrollment->end = Carbon::now()->addDays(45); //take for 30days
            $enrollment->credits = Plan::find($enrollment_plan_id)->credits; //to award
            $enrollment->status = 'new';
            $enrollment->created_by = Auth::id();
            $enrollment->updated_by = Auth::id();
            $enrollment->isActive = false;
            $enrollment->save();

            DB::table('enrollment_payment')->insert([
                'enrollment_id' => $enrollment->id,
                'user_id' => Auth::id(),
                'payment_id' => $payment->id,
                'plan_id' => $enrollment_plan_id,
                'project_id' => $project->id,
                'amount' => $amount
            ]);
        }
*/
        //$credits =  is_null($enrollment_plan_id) ?  $this->CalculateCredits($amount) :     $enrollment->credits;
       // $credits =  $request->filled('credits') ? (int)$request->credits : ( is_null($enrollment_plan_id) ?  $this->CalculateCredits($amount) :     $enrollment->credits);
        $credits = $amount;

        $payment->credits = $credits;
        $payment->credit = "units";
        $payment->debit = "cash";
        $payment->unit_cost = $amount / ($credits > 0 ? $credits : 1 );
        $payment->package_id = null;
        $payment->date = Carbon::now();
        $payment->save();
        

        PostLedgers::dispatch($project->account_type == "postpay" ?  "receivable" : "advance payment", $project->name,"sales","credit",$payment->amount,16.00 , "inclusive");
        PostLedgers::dispatch("cash", $project->name,"sales","debit",$amount,16.00 , "inclusive");
        Log::info("accounts dispatched for posting \n");

        if ($request->status == 'success') {
           // $project->credits += $credits;
          //  $project->save();
/*
            $manage_p_project = Project::where('name', 'primary_project')->first();
            $manage_p_project->credits -=  $credits;
            $manage_p_project->save();
            */
        }

        return back()->with('status', 'Manual Payment Registered Successfully');
    }
}
