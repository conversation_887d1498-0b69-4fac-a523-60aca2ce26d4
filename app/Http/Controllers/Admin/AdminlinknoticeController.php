<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Linknotice;
use App\Project;

class AdminlinknoticeController extends Controller
{
       /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
       // $this->authorize('viewAny', [Linknotice::class]);


        $linknotices = Linknotice::orderBy('created_at','desc')->get();
        return view('admin.linknotices.index', compact('linknotices'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', [Linknotice::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
      //  $this->authorize('create', [Linknotice::class]);

        $request->validate([
            'name' => 'required|unique:linknotices',
          //  'project_id' => 'required',
           // 'linknotice_id' => 'required'
        ]);

        $project_id = Project::where('name','like',$request->project_name.'%')->first()->id;

        $request->merge(['project_id'=>$project_id]);
        Linknotice::create($request->except('project_name'));

        $request->session()->flash('message','Ready to go! Linknotice Created');
        return redirect('admin/linknotices');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function show(Linknotice $linknotice)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function edit( $id)
    {
        //

        $linknotice = Linknotice::find($id);
        $project = Project::find($linknotice->project_id);

        $this->authorize('update', $linknotice);

        return view('admin/linknotices.edit', compact('project','linknotice'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Linknotice $linknotice)
    {

        $request->validate([
            'name' => 'required',
          //  'project_id' => 'required',
           // 'linknotice_id' => 'required'
        ]);


        $linknotice = Linknotice::find($request->linknotice_id);
        $linknotice->fill($request->except('linknotice_id'));
        $linknotice->save();

        $request->session()->flash('message','Linknotice Updated');
        return redirect('admin/linknotices');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $linknotice = Linknotice::find($id);
      $this->authorize('delete', $linknotice);

      $linknotice->delete();
      $request->session()->flash('message','Linknotice: '.$linknotice->name.' deleted');
      return redirect('admin/linknotices');
    }
}
