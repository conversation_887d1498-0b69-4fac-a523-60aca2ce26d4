<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTopupRequest;
use App\Http\Requests\UpdateTopupRequest;
use App\Models\Topup;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Exception;
use App\Payment;
use App\Project;
use App\Jobs\ProcessTopup;

class TopupController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($project_id)
    {
        ini_set('memory_limit', '40960M'); // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode  //set_time_limit(800);//same as this
        ini_set('default_socket_timeout', '-1');
        $topups = DB::table('topups')->where('project_id', $project_id)->latest()->get();
        $project = DB::table('projects')->where('id', $project_id)->first();
        return view('topups.index', compact('topups', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }
    public function e164Formatter254($mobile)
    {
        $smsController =  new \App\Http\Controllers\SmsController;
        return $smsController->e164Formatter254($smsController->cleanNumber($mobile));
    }
    public function checkIfValidMobile($mobile)
    {
        Log::info("checking mobile:" . $mobile);
        return 12 == strlen($this->e164Formatter254($mobile));
    }

    public function checkIfNumericValue($value) 
    {
        return is_numeric($value);
    }
    public function getTelco($mobile)
    {
        $smsController =  new \App\Http\Controllers\SmsController;
        return  $smsController->checkTelco($mobile);
    }
    public function getFlexiProductCodeFromTelco($mobile)
    {
        $telco = $this->getTelco($mobile);
        if ($telco == "safaricom") {
            $code = "SF01";
        } elseif ($telco == "airtel") {
            $code = "AP01";
        } elseif ($telco == "telkom") {
            $code = "OP01";
        } elseif ($telco == "faiba4G") {
            $code = "112233";
        } else {
            $code = "unsupported";
        }
        return $code;
    }
    private function chargeProject($project_id, $total)
    {
        $payments = DB::table('payments')->where('project_id', $project_id)->where('balance', '>', 5)->where('status', 'success')->get();
        $airtime_value = $total; //its either safaricom, airtel or uknown.
        $primary_payment = DB::table('payments')->where(['project_id' => DB::table('projects')->where('name', 'primary_project')->value('id'), 'telco' => 'pesapoint', 'status' => 'success'])->where('balance', '>',  $airtime_value)->first();
        $primary_payment->balance -= $airtime_value; //deduct one csp unit
        try {
            $payment_data = ['payment_id'  => $primary_payment->id, 'usage' => $airtime_value];
            $data_string = json_encode($payment_data);
            $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
            fwrite($file_stream, $data_string . "\n");
            fclose($file_stream);
            Log::info('primary payments appended to file done');
        } catch (Exception $e) {
            echo $e->getMessage();
        }
        $units_needed = $total;
        $cash = 0;
        $total_units_paid = 0;
        foreach ($payments as $payment) {
            $units_available = $payment->balance / 1;
            $units_paid =  $units_needed >  $units_available ? $units_available :  $units_needed; //if needed exceeds available 
            $payment->balance = $payment->balance - $units_paid;
            try {
                $payment_data = ['payment_id'  => $payment->id, 'usage' => $units_paid];
                $data_string = json_encode($payment_data);
                $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
                fwrite($file_stream, $data_string . "\n");
                fclose($file_stream);
                Log::info("project payment records appended to file");
            } catch (Exception $e) {
                echo  $e->getMessage();
            }
            Log::info('available ' . $units_available . ' paid units ' . $units_paid . ' payment balance ' . $payment->balance . 'units needed' . $units_needed);
            $units_needed = $units_needed - $units_paid; //these will be forwarded to next payment on the loop
            $cash +=  ($units_paid * $payment->safaricom);
            $total_units_paid += $units_paid;
            if ($units_needed == 0) {
                return  [$total_units_paid, $cash, $primary_payment->unit_cost];
            }
        }
        return  [0, 0, 0];
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreTopupRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        ini_set('memory_limit', '4096M'); // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 360); //default=30.You can not change this setting with ini_set() when running in safe mode  //set_time_limit(800);//same as this
        ini_set('default_socket_timeout', '-1');

        Log::info($request->all());
        $request->validate(['recipients' => 'nullable|string', 'numbers' => 'nullable|string', 'airtimeValue' => 'nullable|string', 'mobile' => 'nullable|array', 'value' => 'nullable|array']);
        $project = $request->filled('project_id') ? Project::find($request->project_id) :
            Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["data" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
        $project_id = $project->id;
        $created_by = $project->user_id;
        if ($request->filled('project_id')) {
            $this->authorize('create', [Topup::class, $request->project_id]);
            $created_by = $request->user()->id;
        }
        //$numbers = [];
        //$airtimeValue = [] ;
        //add api. number and amount
        $recipients_array =  $request->filled('recipients') ?  json_decode($request->recipients, true) : null;
        $numbers = $recipients_array  ?  array_column($recipients_array, "number") : [];
        $airtimeValue = $recipients_array  ?  array_column($recipients_array, "amount") : [];
        Log::info($numbers);
        Log::info($airtimeValue);
        $redactCurrency = function ($element) {
            return preg_replace("/^[a-zA-Z]{3}\s{1}/", "", $element);
        };
        $airtimeValue = array_map($redactCurrency, $airtimeValue);
        Log::info($numbers);
        Log::info($airtimeValue);

        //add excel
        $request->filled('numbers') ? ($numbers[] = explode(',', $request->numbers)) : [];
        $request->filled('airtimeValue') ? ($airtimeValue[] = explode(',', $request->airtimeValue)) : [];

        //form input
        if ($request->filled('mobile') && $request->filled('value')) {
            $mobile = ($request->mobile);
            $value = ($request->value);
            $name = ($request->name);

            for ($i = 0; $i < count($mobile); $i++) {
                $numbers[] = $mobile[$i];
            }
            for ($i = 0; $i < count($value); $i++) {
                $airtimeValue[] = $value[$i];
            }
            for ($i = 0; $i < count($name); $i++) {
                $names[] = $name[$i];
            }
        }
        //WORK ON CURRENCY
        Log::info($numbers);
        Log::info($airtimeValue);

        if (count($numbers) > 1000) {
            return response()->json(["data" => ["message" => "Error. Exceeded maximum allowed recipients"]]); //ensure project is resolved
        }
        if (count($numbers) !== count($airtimeValue)) {
            return response()->json(["data" => ["message" => "Error. Ensure all numbers have matching airtime value."]]); //ensure project is resolved
        }

        $balance = Payment::where('project_id', $project_id)->where('balance', '>', 5)->where('status', 'like', 'success')->sum('balance');
        if ($balance < (50 + array_sum($airtimeValue)) || $balance < array_sum($airtimeValue)) {
            Log::info('insufficient funds');
            return response()->json(["data" => ["message" => "Error. Insufficient balance. Min project balance for Airtime is 50KES. Top up your project then try again."]]);
        }
        $totalAmount = array_sum($airtimeValue);
        //$flexitopupproduct = DB::table('topupproducts')->where('name', 'flexitopup')->first();
        $merged_response = [];

        $numbers = array_map(array($this, 'e164Formatter254'), $numbers);
        for ($i = 0; $i < count($numbers); $i++) {
            $telco = $this->getTelco($numbers[$i]);
            $request_id = rand(1000000, 9999999);
            $errorMessage = "none";
            $status = "accepted";
            if (!isset($numbers[$i]) || !isset($airtimeValue[$i])) {
                Log::info("no amount or numbe");
                $status = "failed";
                $errorMessage = "Non matching or missing number and airtimeValue pairs";
            }
            if (!$this->checkIfValidMobile($numbers[$i])) {
                Log::info("unknown telco");
                $status = "failed";
                $errorMessage = "Invalid recipient number";
            }
            if (!$this->checkIfNumericValue($airtimeValue[$i])) {
                Log::info("non numeric airtime");
                $status = "failed";
                $errorMessage = "Invalid airtimeValue. (3 digit currency code)(space)(Decimal value) eg. KES 200";
            }
            if ($airtimeValue[$i] > 5000) {
                Log::info("airtime more than 5000");
                $status = "failed";
                $errorMessage = "Invalid airtime Value. Maximum limit per recipient is KES 5000";
            }
            if ($telco == "unsupported") {
                $status = "failed";
                $errorMessage = "unsupported mobile network";
            }
            //asve contact if name and mobile
            if (isset($numbers[$i]) && isset($names[$i])) {
                try {
                    $properName = preg_replace("/\d/", '', $names[$i]);
                    if( !DB::table('contacts')->where(['mobile' => $numbers[$i], 'project_id' => $project_id ])->exists() ) {
                        $id = DB::table('contacts')->insertGetId(['fname' => $properName , 'mobile' => $numbers[$i], 'project_id' => $project_id, 'created_at' => now() ]) ;
                        Log::info("saved contact id" . $id);
                    } else {
                        Log::info("contact exist");
                    }
                } catch (Exception $e) {
                    Log::info("could not save contact");
                    var_dump($e);
                }

            }
            $merged_response[] =  [
                "number" => $numbers[$i],
                "amount" => "KES " . number_format($airtimeValue[$i], 2),
                "discount" => "KES 0.00",
                "status" => $status,
                "requestId" =>  $request_id,
                "errorMessage" =>  $errorMessage
            ];
            if ($status === "failed") continue;
            $top_up_data = [
                'project_id' => $project_id,
                'terminal_number' =>  config('app.airtime_terminal_number'),  //topupproduct
                'method_name' => 'TopupFlexi',
                'session_id' => '', //fromcallback
                'request_unique_id' =>  $request_id,
                'transaction_key' => config('app.airtime_transaction_key'), //, //topupproduct
                'referral_number' => $numbers[$i], //request
                'amount' => (int)$airtimeValue[$i], //request
                'from_ani' => config('app.airtime_from_ani'), //topupproduct
                'email' => config('app.airtime_email'), //topupproduct
                'response_code' => '', // //callback
                'response_description' => '',  //callback
                'confirmation_code' => '', //callback
                'audit_no' => '',
                'operator_request_id' => '',
                'provider_response' => '', //callback.   json
                'authorize_key' => '',
                'system_service_id' => '2', //topupproduct
                'system_service_name' => 'Topup',
                'product_id' => '',
                'product_name' => '',
                'product_description' => '',
                'product_type' => 'flexi',
                'product_code' => $this->getFlexiProductCodeFromTelco($numbers[$i]), //topupproduct
                'product_info' => '',
                'telco' =>  $telco,
                'batch_id' => '',
                'batch_name' => '',
                'batch_type' => '',
                'batch_description' => '',
                'image_url' => '',
                'country' => '',
                'field_info' => '',
                'notification_info' => '',
                'allow_minor_currency' => '',
                'surcharge_type' => '',
                'surcharge_value' => 0.00, //add discount field discount    explode(',', $project->details)[3]  get eg. 0.03  0.03 x 100 = 3kes discount
                'created_at' => now(),
                'created_by' => $created_by,
                'status' => 'pending',
            ];
            $this->chargeProject($project_id, (int)$airtimeValue[$i]); // 1- $project->airtime_discount * $airtimeValue[$i]
            ProcessTopup::dispatch($top_up_data);
        }
        return response()->json(["recipientsCount" => count($merged_response), "totalAmount" => "KES " . number_format($totalAmount, 2), "totalDiscount" => "KES 0.00", "responses" =>  $merged_response]);
    }
    /* {
    "success": true,
    "data": "{\"ResponseCode\":\"000\",\"OperatorRequestID\":\"3873045\",\"ConfirmationCode\":\"R221211.1542.250012\",\"ProviderResponse\":\"{\\\"Transaction Id\\\":\\\"R221211.1542.250012\\\",\\\"Transaction Status transaction fee\\\":\\\"200\\\"}\",\"AuditNo\":\"16512927\",\"ProductInfo\":\"Safaricom Prepaid\",\"ResponseDescription\":\"Successful\"}"
}  

 {"success":true,"data":"{\"ResponseCode\":\"000\",\"OperatorRequestID\":\"3873271\",\"ConfirmationCode\":\"R221212.0258.220002\",\"ProviderResponse\":\"{\\\"Transaction Id\\\":\\\"R221212.0258.220002\\\",\\\"Transaction Status transaction fee\\\":\\\"200\\\"}\",\"AuditNo\":\"16513188\",\"ProductInfo\":\"Safaricom Prepaid\",\"ResponseDescription\":\"Successful\"}"} 
 */


    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Http\Response
     */
    public function show(Topup $topup)
    {
        //
    }
    public function getBalance()
    {

        try {
            $base_uri = config('app.airtime_base_uri');
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('GET', '/balance', [
                'query' => [
                    "encryptionKey" => config('app.airtime_encryption_key'),
                    "terminalNUmber" => config('app.airtime_terminal_number'),
                ],
                [
                    'timeout' => 240, // Response timeout .....240
                    'connect_timeout' => 240, // Connection timeout ...240
                ]
            ]);

            Log::info('airtime balance request completed');
            $rawBody = $response->getBody();
            Log::info($rawBody);
            $body = json_decode($rawBody, true);
            Log::info($body);
            if ($body["ResponseCode"] == "000") {
                $bal =  number_format(((int)$body["Balance"] / 100), 2);
                return response()->json(["balance" => $bal]);
            } else {
                return response()->json(["balance" => "Loading"]);
            }
        } catch (Exception $e) {
            Log::info($e);
        }
        return response()->json(["balance" => "Loading"]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Http\Response
     */
    public function edit(Topup $topup)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateTopupRequest  $request
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateTopupRequest $request, Topup $topup)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Http\Response
     */
    public function destroy(Topup $topup)
    {
        //
    }
}
