<?php

namespace App\Http\Controllers;

use App\Enrollment;
use Illuminate\Http\Request;
use App\Project;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Plan;
class EnrollmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $project = Project::find($id);
        $enrollments = Enrollment::where('project_id',$project->id)->get();

        $this->authorize('viewAny', [Enrollment::class, $id]);
        return view('enrollments.index', compact('project','enrollments'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        //
        $project = Project::find($id);
        $this->authorize('create', [Enrollment::class, $id]);
        return view('enrollments.create', compact('project'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        //
        $this->authorize('create', [Enrollment::class, $id]);

        $enrollment = new Enrollment();
        $enrollment->user_id = Auth::id();
        $enrollment->project_id = $id;
        $enrollment->plan_id = $request->plan_id;
        $enrollment->start = Carbon::now();
        $enrollment->end = Carbon::now()->addDays(30);
        $enrollment->credits = Plan::find($request->plan_id)->credits;
        $enrollment->status = 'new';
        $enrollment->created_by = Auth::id();
        $enrollment->updated_by = Auth::id();
        $enrollment->isActive = false;
        $enrollment->save();


    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Enrollment  $enrollment
     * @return \Illuminate\Http\Response
     */
    public function show(Enrollment $enrollment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Enrollment  $enrollment
     * @return \Illuminate\Http\Response
     */
    public function edit(Enrollment $enrollment)
    {
        //
        $this->authorize('update', $enrollment);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Enrollment  $enrollment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Enrollment $enrollment)
    {
        //
        $this->authorize('update', $enrollment);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Enrollment  $enrollment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Enrollment $enrollment)
    {
        //
        $this->authorize('delete', $enrollment);
    }
}
