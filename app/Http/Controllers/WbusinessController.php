<?php

namespace App\Http\Controllers;

use App\Wbusiness;
use Illuminate\Http\Request;

class WbusinessController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Wbusiness  $wbusiness
     * @return \Illuminate\Http\Response
     */
    public function show(Wbusiness $wbusiness)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wbusiness  $wbusiness
     * @return \Illuminate\Http\Response
     */
    public function edit(Wbusiness $wbusiness)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wbusiness  $wbusiness
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wbusiness $wbusiness)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wbusiness  $wbusiness
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wbusiness $wbusiness)
    {
        //
    }
}
