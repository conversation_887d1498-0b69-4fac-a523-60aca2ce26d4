<?php

namespace App\Http\Controllers;

use App\Premium;
use Illuminate\Http\Request;
use App\Project;
use Illuminate\Support\Facades\Mail;
use App\Mail\AppNotification;

class PremiumController extends Controller
{

    // Upload files/images
    public function checkSaveFile($file)
    {
        $allowedfileExtension = ['jpg','jpeg', 'png', 'docx','doc','pdf'];
        $extension = strtolower($file->getClientOriginalExtension());
        //$name = $file->getClientOriginalName();
        $check = in_array($extension, $allowedfileExtension);
        if ($check) {
            //return $file->storeAs( 'documents', $name );
            return $file->store('public/premiumkycdocs');
            // $extension = $request->file('avatar')->extension();
        } else {
            return null; //redirect('consumables/create')->withErrors('Only  jpeg or png images are allowed');
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        $this->authorize('viewAny', [Premium::class, $id]);

        $project = Project::find($id);
        $premia = Premium::where('project_id', $id)->orderBy('created_at','desc')->get();
        return view('premia.index', compact('premia','project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        $this->authorize('create', [Premium::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request,$id)
    {
        $this->authorize('create', [Premium::class, $id]);
        $request->validate([
            'name' => 'required|unique:premia',
            'project_id' => 'required',
            'shortcode' => 'required',
            'detail' => 'required',
            'product' => 'required',
            'offer_type' => 'required',
            'rate' => 'required',
            //'company_name' => 'required',
            //'company_industry' => 'required',
            //'company_address' => 'required',
            //'company_email' => 'required',
            'application_letter' => 'nullable',
            //'company_cert' => 'required',
            //'company_kra' => 'required',
            //'company_representative_id' => 'required'
        ]);
        $application_letter = $request->file('application_letter');
        $company_cert = $request->file('company_cert');
        $company_kra = $request->file('company_kra');
        $company_representative_id = $request->file('company_representative_id');

        $request->merge(['code' => uniqid('sozprem'),
       // 'application_letter' =>  $this->checkSaveFile($application_letter),
        'company_cert' =>  $this->checkSaveFile($company_cert),
        'company_kra' =>  $this->checkSaveFile($company_kra),
        'company_representative_id' =>  $this->checkSaveFile($company_representative_id)
         ]);

        Premium::create($request->all());

        $project = Project::find($id)->name;
        //Mail user owning the project  ->send mail to default queue
        $emailTo = "<EMAIL>";
        $bcc = "<EMAIL>";
        $message = "Hi, someone just requested for a premium service for project: " . $project;
        $subject = "New Sender ID request";
        Mail::to($emailTo)->bcc($bcc)->queue(new AppNotification($message, $subject));


        $request->session()->flash('message','Ready to go! Premium Requested');
        return redirect('projects/'.$request->project_id.'/premia');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function show(Premium $premium)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function edit( $pid, $id)
    {
        //

        $project = Project::find($pid);
        $premium = Premium::find($id);
        $this->authorize('update', $premium);

        return view('premiums.edit', compact('project','premium'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $pid,$id)
    {
        $project = Project::find($request->project_id);
        $premium = Premium::find($request->premium_id);

        //$this->authorize('update', User::class, Premium::class);
        $this->authorize('update', $premium);

        $request->validate([
            'name' => 'required',
            'premium_id' => 'required',
            'project_id' => 'required',
            'shortcode' => 'required',
            'detail' => 'required',
            'product' => 'required',
            'offer_type' => 'required',
            'rate' => 'required',
            //'company_name' => 'required',
            //'company_industry' => 'required',
            //'company_address' => 'required',
            //'company_email' => 'required',
            'application_letter' => 'nullable',
            //'company_cert' => 'required',
           // 'company_kra' => 'required',
            //'company_representative_id' => 'required'
        ]);
        $application_letter = $request->file('application_letter');
        $company_cert = $request->file('company_cert');
        $company_kra = $request->file('company_kra');
        $company_representative_id = $request->file('company_representative_id');

        $request->merge(['application_letter' =>  $this->checkSaveFile($application_letter),
        'company_cert' =>  $this->checkSaveFile($company_cert),
        'company_kra' =>  $this->checkSaveFile($company_kra),
        'company_representative_id' =>  $this->checkSaveFile($company_representative_id)
         ]);

        $premium->update($request->except('premium_id'));
        $request->session()->flash('message','Premium Updated');
        return redirect('projects/'.$request->project_id.'/premiums');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Premium  $premium
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {
      $premium = Premium::find($id);
      $this->authorize('delete', $premium);

      $premium->delete();
      $request->session()->flash('message','Premium: '.$premium->name.' deleted');
      return redirect('projects/'.$pid.'/premiums');
    }
}
