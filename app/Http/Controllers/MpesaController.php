<?php

namespace App\Http\Controllers;

use App\Mpesa;
use Illuminate\Http\Request;
use App\Contact;
use App\ContactList;
use App\Project;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use App\Payment;
use App\Sms;
use App\User;

class MpesaController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request,$id)
    {
        $projects =  User::find($request->user()->id)->projects()->get();
        if ( $projects->where('id', $id)->count()  !==  1) {
            return back();
        }

        $this->authorize('viewAny', [Sms::class, $id]);
        $project = Project::find($id);
        $mpesas = Mpesa::where('project_id', $id)->latest()->get();
        return view('payconfirm.index', compact('mpesas','project'));

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Mpesa  $mpesa
     * @return \Illuminate\Http\Response
     */
    public function show(Mpesa $mpesa)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Mpesa  $mpesa
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $this->authorize('viewAny', [Sms::class, $id]);
        $project = Project::find($id);
        $mpesas = Mpesa::where('project_id', $id)->latest()->get();
        //$smses = SMS::where('project_id', $id)->orderBy('created_at','asc')->get();
       // return view('sms.index', compact('smses', 'project'));
        return view('payconfirm.index', compact('mpesas','project'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Mpesa  $mpesa
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Mpesa $mpesa)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Mpesa  $mpesa
     * @return \Illuminate\Http\Response
     */
    public function destroy(Mpesa $mpesa)
    {
        //
    }
}
