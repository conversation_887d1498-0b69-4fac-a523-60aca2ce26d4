<?php

namespace App\Http\Controllers;

use App\Securityquestion;
use Illuminate\Http\Request;

class SecurityquestionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Securityquestion  $securityquestion
     * @return \Illuminate\Http\Response
     */
    public function show(Securityquestion $securityquestion)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Securityquestion  $securityquestion
     * @return \Illuminate\Http\Response
     */
    public function edit(Securityquestion $securityquestion)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Securityquestion  $securityquestion
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Securityquestion $securityquestion)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Securityquestion  $securityquestion
     * @return \Illuminate\Http\Response
     */
    public function destroy(Securityquestion $securityquestion)
    {
        //
    }
}
