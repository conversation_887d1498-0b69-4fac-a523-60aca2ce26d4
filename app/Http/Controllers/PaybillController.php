<?php

namespace App\Http\Controllers;

use App\Paybill;
use Illuminate\Http\Request;
use App\Sms;
use App\Project;
use App\Mpesa;
use Illuminate\Support\Facades\Auth;
use App\User;

class PaybillController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Paybill  $paybill
     * @return \Illuminate\Http\Response
     */
    public function show(Paybill $paybill)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Paybill  $paybill
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, $id)
    {
        $projects =  User::find($request->user()->id)->projects()->get();
        if ( $projects->where('id', $id)->count()  !==  1) {
            return back();
        }
        $this->authorize('viewAny', [Sms::class, $id]);
        $project = Project::find($id);
        $paybill = Paybill::where('project_id', $id)->exists() ?  Paybill::where('project_id', $id)->first() : null;
        $mpesas = Mpesa::where('project_id', $id)->where('paybill_id', $paybill ? $paybill->id : null)->get();
        //$smses = SMS::where('project_id', $id)->orderBy('created_at','asc')->get();

        // return view('sms.index', compact('smses', 'project'));
        return view('payconfirm.create', compact('mpesas', 'paybill', 'project'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Paybill  $paybill
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $project = Project::find($id);
        $request->validate(['business_name'=> 'required', 'paybill_type'=> 'required','number'=>'required']);
        $this->authorize('update', [Project::class, $project]);

        $paybill = Paybill::where('project_id', $id)->exists() ?  Paybill::where('project_id', $id)->first() : new Paybill();
        $paybill->project_id = $id;
        $paybill->business_name = $request->business_name;
        $paybill->paybill_type = $request->paybill_type;
        $paybill->number = $request->number;
        $paybill->code = $project->code;
        $paybill->consumer_key = $request->consumer_key;
        $paybill->consumer_secret = $request->consumer_secret;
        $paybill->authorized_mobile = $request->authorized_mobile;
        $paybill->validate_url = "";//"https://sozuri.net/zuka/paybill/validate";
        $paybill->confirm_url = "";//"https://sozuri.net/zuka/paybill/confirm";
        $paybill->till_callback_url = "";//"https://sozuri.net/zuka/lipa/".$project->code."/handle";
        $paybill->webhook = $request->webhook;
        $paybill->sender_id = $request->sender_id;
        $paybill->numbers_to_notify = $request->numbers_to_notify;
        $paybill->template_1 = $request->template_1;
        $paybill->daily_summary = $request->daily_summary;
        $paybill->weekly_summary = $request->weekly_summary;
        $paybill->user_id = Auth::id();
        $paybill->save();
        $mpesas = Mpesa::where('project_id', $id)->where('paybill_id', $paybill ? $paybill->id : null)->get();
        //$smses = SMS::where('project_id', $id)->orderBy('created_at','asc')->get();

        // return view('sms.index', compact('smses', 'project'));
        $request->session()->flash('message', 'You have successfully modified Zuka Mpesa details');
        return redirect('/projects/' . $project->id . '/payconfirm/settings'); //->with(compact('mpesas','paybill','project'])
        //   return view('payconfirm.create', );    
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Paybill  $paybill
     * @return \Illuminate\Http\Response
     */
    public function destroy(Paybill $paybill)
    {
        //
    }
}
