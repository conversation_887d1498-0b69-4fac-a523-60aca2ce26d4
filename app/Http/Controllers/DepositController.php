<?php

namespace App\Http\Controllers;

use App\Deposit;
use App\Project;
use App\Setting;
use Auth;
use Illuminate\Http\Request;

class DepositController extends Controller
{
    public function getRate($deposit_amount, Setting $setting, Project $project)
    {
        if ($deposit_amount >= $setting->dec_6) {//amount category 
            return $project->rate_6;           //pick the project's rate for this bracket. so rate is in project. while Category is in Global
        } elseif ($deposit_amount >= $setting->dec_5) {
            return $project->rate_5;
        } elseif ($deposit_amount >= $setting->dec_4) {
            return  $project->rate_4;
        } elseif ($deposit_amount >= $setting->dec_3) {
            return  $project->rate_3;
        } elseif ($deposit_amount >= $setting->dec_2) {
            return  $project->rate_2;
        } else {
            return  $project->rate_1;
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $projects = Project::all();
        $deposits = Deposit::where('project_id', $id)->orderBy('created_at','desc')->get();
        return view('deposits.index', compact('deposits', 'projects'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Deposit::class);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        $this->authorize('create', Deposit::class);

        $project = Project::find($request->project_id);
        $setting = Setting::where('name', 'like', '%rateamount%')->first(); //must be 
        $deposit_amount = $request->amount;
        $rate = $this->getRate($deposit_amount, $setting, $project);

if($rate < 0.5){
    return back()->withErrors('The project has incorrectly set rates. Please contact support team via the "Help menu" <NAME_EMAIL>');
}
$credits = floor($deposit_amount / $rate);

        $deposit = new Deposit;
        $deposit->project_id = $request->project_id;
        $deposit->amount = $deposit_amount;
        $deposit->balance = $deposit_amount;
        $deposit->rate  = $rate;
        $deposit->deposit_credits = $credits;
        $deposit->from = $request->from;
        $deposit->status = "completed";
        $deposit->payment_method = $request->payment_method;
        $deposit->type = $request->type;
        $deposit->detail = $request->detail;
        $deposit->save();


        $project->increment('credits',  $credits);

        $request->session()->flash('message', 'Manual Top Up Created. It will be approved shortly! If delayed, talk to support via WezaTok');
        return redirect('deposits');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function show(Deposit $deposit)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function edit(Deposit $deposit)
    {
        $this->authorize('update', $deposit);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Deposit $deposit)
    {
        $this->authorize('update', $deposit);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Deposit  $deposit
     * @return \Illuminate\Http\Response
     */
    public function destroy(Deposit $deposit)
    {
        $this->authorize('delete', $deposit);
    }
}
