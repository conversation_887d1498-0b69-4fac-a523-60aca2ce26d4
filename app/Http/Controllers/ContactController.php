<?php

namespace App\Http\Controllers;

use App\Contact;
use Illuminate\Http\Request;
use App\Exclusion;
use Carbon\Carbon;
use App\Imports\ContactsImport;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ContactsExport;
use App\ContactList;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Project;
use Illuminate\Support\Facades\Log;
use App\Talklog;
use App\Policies\ContactPolicy;
use App\Jobs\UploadContacts;
use Validator;
class ContactController extends Controller
{
    public function export(Request $request)
    {
        //if($request->hasFile('tools'))
        return Excel::download(new ContactsExport, 'sample_contacts.xlsx');
        return back();
    }

    public function import(Request $request)
    {
        if (true) {
            // return back()->withErrors('Contacts could not be uploaded. Please try again later.');
        }
        ini_set( 'upload_max_size' , '256M' );
        ini_set( 'post_max_size', '256M');
        ini_set('memory_limit', '10000M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $request->validate(['recipient_list' => 'required', 'contactlists' => 'required', 'project_id' => 'required']);

        $project_id = $request->project_id;
        //dd($request->all());
        $this->authorize('create', [Contact::class, $project_id]);
        /*
        //the system will almost always create a single default
        $file_collection = (new ContactsImport)->toCollection($request->file('contacts_excel'), \Maatwebsite\Excel\Excel::XLSX); //remove type to test with other types
        $file_contacts = $file_collection->collapse()->toArray();
        dd( $file_collection);
*/
        $file_contacts = json_decode($request->recipient_list, true);
        Log::info(count( $file_contacts));
        if (count($file_contacts) > 1000000) {
            //return back()->withErrors('For optimal performance, upload a maximum of 1000,000  contacts. In addition, take a coffee after your first upload before uploading a second batch to allow your Project to queue appropriately.');
        }
        //Log::info($file_contacts);
        //we need to add the project_id column, so for now lets split up the array
        $numbers = array_column($file_contacts, 'mobile');
        $emails = array_column($file_contacts, 'email');
        $cities = array_column($file_contacts, 'city');
        $fnames = array_column($file_contacts, 'fname');
        $mnames = array_column($file_contacts, 'mname');
        $lnames = array_column($file_contacts, 'lname');
        $types = array_column($file_contacts, 'type');
        $jobs = array_column($file_contacts, 'job');
        $companies = array_column($file_contacts, 'company');
        $isStars = array_column($file_contacts, 'isStar');
        $isSpams = array_column($file_contacts, 'isSpam');
        $isHiddens = array_column($file_contacts, 'isHidden');
        $details = array_column($file_contacts, 'detail');
        $project_ids = array_fill(0, count($file_contacts), $project_id);
        $tags = array_fill(0, count($file_contacts), $request->contactlists);

        //time to check if number is valid
        $cleanNumber = function ($phone) {
            $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
            return $clean_number;
        };
        $e164Formatter254 = function ($phone) {
            $forced_numbber = substr($phone, -9);
            return '254' . $forced_numbber;
        };
        $clean_contacts = array_map($cleanNumber, $numbers);
        $formatted_recipients = array_map($e164Formatter254, $clean_contacts);

        $numbers = $formatted_recipients;
        $contacts_results = array_map(function ($number, $email, $city, $fname, $mname, $lname, $type, $job, $company, $isStar, $isSpam, $isHidden, $detail, $project_id, $tag) {
            return array_combine(
                ['mobile', 'email', 'city', 'fname', 'mname', 'lname', 'type', 'job', 'company', 'isStar', 'isSpam', 'isHidden', 'detail', 'project_id', 'tag'],
                [$number, $email, $city, $fname, $mname, $lname, $type, $job, $company, $isStar, $isSpam, $isHidden, $detail, $project_id, $tag]
            );
        }, $numbers, $emails, $cities, $fnames, $mnames, $lnames, $types, $jobs, $companies, $isStars, $isSpams, $isHiddens, $details, $project_ids, $tags);
$chunks = array_chunk($contacts_results, 4000);
        //foreach ($contacts_results as $contacts_result) {
            //if ($contacts_result['mobile'] == null) {
            //    continue;
           // }
            foreach ($chunks as $chunk) {

            DB::table('contacts')->insert($chunk);
            /*
            $contact_id =  DB::table('contacts')->where(['project_id' => $project_id, 'mobile' => $contacts_result['mobile']])->exists() ?
                DB::table('contacts')->where(['project_id' => $project_id, 'mobile' => $contacts_result['mobile']])->first()->id :
                DB::table('contacts')->insertGetId($contacts_result);
            DB::table('contact_contact_list')->insertOrIgnore(
                ['contact_id' =>  $contact_id, 'contact_list_id' => $contactlists, 'project_id' => $project_id]
            );*/
            Log::info('contact imported');
            
        }



        /*
        //chunk and queue
        $split_array = array_chunk($contacts_result, 500);
        foreach ($split_array as $contacts_results) {
            UploadContacts::dispatch($contacts_results, $request->contactlists, $project_id)->onQueue('low')->delay(5);
        }
*/

        /*old
       //DB::table('contacts')->insert($contacts_results);
       foreach($contacts_results as $contacts_result){
           if(Contact::where(['project_id'=>$project_id,'mobile'=> $contacts_result['mobile']])->exists()){
            $contact = Contact::where(['project_id'=>$project_id,'mobile'=> $contacts_result['mobile']])->get()->first();
            $contact->contactLists()->sync($request->contactlists);
             continue; }
        $contact = new Contact();
        $contact->fill($contacts_result);
        $contact->save();
        $contact->contactLists()->sync($request->contactlists);
       }*/
        $count = count($numbers);
        $request->session()->flash('message', 'All good! ' . $count . ' Contacts queued for import. Check back after a few hours');
        return response()->json(['status' => 'success', 'message' => $count .' Contacts queued for import']);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        $this->authorize('viewAny', [Contact::class, $id]);
        $project = Project::find($id);
        return view('contacts.log', compact('project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        //
        $this->authorize('create', [Contact::class, $id]);
        $project = Project::find($id);
        return view('contacts.create', compact('project'));
    }
    /**
     * Show the form for uploading a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function upload($id)
    {
        $this->authorize('create', [Contact::class, $id]);

        $project = Project::find($id);

        return view('contacts.upload', compact('project'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        $this->authorize('create', [Contact::class, $id]);
        //check if mobile format correct
        if (!preg_match("/^[+]?[0-9]{7,13}$/", $request->mobile)) {
            //return back()->withErrors('Number has wrong format. Please use the international format..  +254725164XXX  and avoid spaces');
        }

        $project_id = $request->project_id;
        $contact_lists = $request->contact_lists;
        $mobile =  '254' . substr($request->mobile, -9);
        if (strlen($mobile) !== 12) {
            return response()->json(['status' => 'success', 'message' => 'Number has wrong format. Please use the international format..  254725164XXX  and avoid spaces']);
        }


        $contact = new Contact();
        $request->merge(['project_id' => $project_id, 'mobile' => $mobile]);
        $contact->fill($request->except('contact_lists'));

        $contact->save();

        //$contact->contactLists()->sync($request->contact_lists);
        
        Log::info('User created a contact' . Auth::id());

        // $request->session()->flash('message', 'Contact created');
        return response()->json(['status' => 'success', 'message' => 'Contact created']);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function show(Contact $contact)
    {
        $this->authorize('update', $contact);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function edit($pid, Contact $contact)
    {
        $this->authorize('update', $contact);

        $project = Project::find($pid);
        return view('contacts.edit', compact('contact', 'project'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,  $contact)
    {
        //
        $contact = Contact::find($request->contact_id);
        $this->authorize('update', $contact);
        $mobile =  '254' . substr($request->mobile, -9);
        if (strlen($mobile) !== 12) {
            return back()->withErrors('Number has wrong format. Please use the international format..  254725164XXX  and avoid spaces');
        }

        $request->merge([
            'isStar' => $request->filled('isStar') ? "1" : "0",
            'isSpam' => $request->filled('isSpam') ? "1" : "0",
            'isHidden' => $request->filled('isHidden') ? "1" : "0",
            'mobile' => $mobile
        ]);
        $contact->update($request->except('contact_id'));
        $request->session()->flash('message', 'Contact details for ' . $contact->mobile . ' Updated');
        $project = Project::find($contact->project_id);
        return redirect('/projects/' . $project->id . '/contacts');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Contact  $contact
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $project_id, $contact_id)
    {
        Log::info($request->user()->id);
        $contact = Contact::find($contact_id);
        $this->authorize('delete', $contact);
        $contact->contactLists()->detach();
        $contact->delete();
        $request->session()->flash('message', 'Contact deleted');
        return response()->json(['status' => 'success', 'message' => 'Contact deleted successfully']);
        //send a success message
    }



     // Method to get all contacts
     public function apiGetAll(Request $request)
     {
        $project = Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["messageData" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
        $contactListId = null;
        if($request->group != "" && ContactList::where(['name' => $request->group, 'project_id' => $project->id] )->exists()) {
            $contactList =  ContactList::where(['name' => $request->group, 'project_id' => $project->id] )->first();
            $contactListId =  $contactList->id;
             $contacts = Contact::where('project_id', $project->id)->where('tag', $contactListId)->select('id','fname','mname','lname','mobile','email','city','company','created_at')->paginate(1000);
        } else {
            $contacts = Contact::where('project_id', $project->id)->select('id','fname','mname','lname','mobile','email','city','company','created_at')->paginate(1000);
        }
         return response()->json(['group' => $contactList->name ?? '', 'contacts' => $contacts, ]);
     }
 
     // Method to get a single contact by ID
     public function apiGet(Request $request, $id)
     {
        $project = Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["messageData" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
        $contact = Contact::where(['project_id' => $project->id, 'id'=> $id])->select('id','fname','mname','lname','mobile','email','city','company','created_at')->first() ??
        Contact::where(['project_id' => $project->id, 'mobile'=> $id])->select('id','fname','mname','lname','mobile','email','city','company','created_at')->first() ?? null;
   
                if (!$contact) {
             return response()->json(['error' => 'Contact not found'], 404);
         }
         return response()->json(['contact' => $contact]);
     }
 
     // Method to create a new contact
     public function apiCreate(Request $request)
     {
        $project = Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["messageData" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
         $validator = Validator::make($request->all(), [
             'contacts.*.fname' => 'required|string',
             'contacts.*.mname' => 'nullable|string',
             'contacts.*.lname' => 'nullable|string',
             'contacts.*.mobile' => 'required|string',
             'contacts.*.email' => 'nullable|email',
             // Add other validation rules as needed
         ]);
 
         if ($validator->fails()) {
             return response()->json(['errors' => $validator->errors()], 422);
         }
 
         $contactsData = $request->input('contacts');
         if(count($contactsData) > 1000) {
             return response()->json(['error' => 'You can only create 1000 contacts at a time'], 400);
         }
         $contactListId = ContactList::where(['name' => $request->group, 'project_id' => $project->id] )->exists() ? 
         ContactList::where(['name' => $request->group, 'project_id' => $project->id] )->first()->id :
         ContactList::insertGetId(['name' => $request->group?? 'default', 'project_id' => $project->id]);

         foreach ($contactsData as $data) {
             $contact = new Contact();
             //$contact->fill($data);
             //'mobile', 'email', 'city', 'fname', 'mname', 'lname', 'type', 'job', 'company', 'isStar', 'isSpam', 'isHidden', 'detail', 'project_id', 'tag'
                $contact->project_id = $project->id;
                $contact->mobile = $data['mobile'];
                $contact->email = $data['email'] ?? null;
                $contact->city = $data['city'] ?? null;
                $contact->fname = $data['fname'] ?? null;
                $contact->mname = $data['mname'] ?? null;
                $contact->lname = $data['lname'] ?? null;
               // $contact->type = $data['type'];
                $contact->job = $data['job'] ?? null;
                $contact->company = $data['company'] ?? null;
                $contact->tag = $contactListId;
                $contact->save();
         }
 
         return response()->json(['message' => 'Contacts created successfully'], 201);
     }
 
     // Method to update a contact by ID
     public function apiPut(Request $request, $id)
     {
        $project = Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["messageData" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
         $validator = Validator::make($request->all(), [
             'contact.fname' => 'required|string',
             'contact.mname' => 'nullable|string',
             'contact.lname' => 'nullable|string',
             'contact.mobile' => 'required|string',
             'contact.email' => 'nullable|email',
         ]);
 
         if ($validator->fails()) {
             return response()->json(['errors' => $validator->errors()], 422);
         }
 
         $data = $request->input('contact');
         $contactListId = ContactList::where(['name' => $request->group, 'project_id' => $project->id] )->exists() ?
         ContactList::where(['name' => $request->group, 'project_id' => $project->id] )->first()->id :
         null;

        


         try {
            if($contactListId) {
                $contact = Contact::where(['project_id' => $project->id, 'id'=> $id, 'tag' => $contactListId ])->first() ??
                Contact::where(['project_id' => $project->id, 'mobile'=> $id, 'tag' => $contactListId ])->first() ?? null;
            } else {
                $contact = Contact::where(['project_id' => $project->id, 'id'=> $id])->first() ??
                Contact::where(['project_id' => $project->id, 'mobile'=> $id])->first() ?? null;
            }

             
             if($contact === null) {
                return response()->json(['error' => 'Contact not found'], 404);
             }

             $contact->mobile = $data['mobile'] ?? null;
             $contact->email = $data['email'] ?? null;
             $contact->city = $data['city'] ?? null;
             $contact->fname = $data['fname'] ?? null;
             $contact->mname = $data['mname'] ?? null;
             $contact->lname = $data['lname'] ?? null;
             $contact->job = $data['job'] ?? null;
             $contact->company = $data['company'] ?? null;
             $contact->tag = $contactListId;
             $contact->save();
         } catch(Exception $e) {
             return response()->json(['error' => 'Contact not updated'], 404);
         }
        
         return response()->json(['message' => 'Contact updated successfully']);}
     // Method to delete a contact by ID
     public function apiDelete(Request $request, $id)
     {
        $project = Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["messageData" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
        $contact = Contact::where(['project_id' => $project->id, 'id'=> $id])->first() ??
        $contact = Contact::where(['project_id' => $project->id, 'mobile'=> $id])->first() ?? null;
        if($contact === null) {
           return response()->json(['error' => 'Contact not found'], 404);
        }
         $contact->delete();
         return response()->json(['message' => 'Contact deleted successfully']);
     }
}
