<?php

namespace App\Http\Controllers;

use App\Wprofilepic;
use Illuminate\Http\Request;

class WprofilepicController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Wprofilepic  $wprofilepic
     * @return \Illuminate\Http\Response
     */
    public function show(Wprofilepic $wprofilepic)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wprofilepic  $wprofilepic
     * @return \Illuminate\Http\Response
     */
    public function edit(Wprofilepic $wprofilepic)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wprofilepic  $wprofilepic
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wprofilepic $wprofilepic)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wprofilepic  $wprofilepic
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wprofilepic $wprofilepic)
    {
        //
    }
}
