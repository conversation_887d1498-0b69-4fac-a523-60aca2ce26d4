<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ManagecsvController extends Controller
{
    //
    public function checkSaveFile($file, $name)
    {
        $allowedfileExtension = ['xlsx','pdf','docx'];
        //$filename = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $check = in_array($extension, $allowedfileExtension);
        if ($check && $name === "sendername") {
            return $file->storeAs('samples', $name . '.docx');
        }elseif($check ) {
                return $file->storeAs('samples', $name . '.xlsx');
        } else {
           // return back()->withErrors('Only CSV file format is allowed');
           return false;
            // Sorry Only Upload png , jpg , doc
        }
    }
    public function uploadSampleHsm(Request $request)
    {
        $request->validate([]);
        $file = $request->file('hsmfile');
        $file = $request->hasFile('hsmfile') ? $this->checkSaveFile($file, 'hsm') : false;
        if (!$file) {
            return redirect('admin')->with('error', 'could not upload file try again later');
        }
        return redirect('admin')->with('status', 'file uploaded');
    }
    public function uploadSampleAirtime(Request $request)
    {
        $request->validate([]);
        $file = $request->file('airtimefile');
        $file = $request->hasFile('airtimefile') ? $this->checkSaveFile($file, 'airtime') : false;
        if (!$file) {
            return redirect('admin')->with('error', 'could not upload file try again later');
        }
        return redirect('admin')->with('status', 'file uploaded');
    }
    public function uploadSampleContact(Request $request)
    {
        $request->validate([]);
        $file = $request->file('contactfile');
        $file = $request->hasFile('contactfile') ? $this->checkSaveFile($file, 'contact') : false;
        if (!$file) {
            return redirect('admin')->with('error', 'could not upload file try again later');
        }
        return redirect('admin')->with('status', 'Contact file uploaded');
    }

    public function uploadSampleContacts(Request $request)
    {
        $request->validate([]);
        $file = $request->file('contactsfile');
        $request->hasFile('contactsfile') ? $this->checkSaveFile($file, 'contacts') : false;
        if (!$file) {
            return redirect('admin')->with('error', 'could not upload file try again later');
        }
        return redirect('admin')->with('status', 'Contacts for upload file uploaded');
    }
    public function uploadSampleSenderApplication(Request $request)
    {
        $request->validate([]);
        $file = $request->file('senderfile');
        $request->hasFile('senderfile') ? $this->checkSaveFile($file, 'sendername') : false;
        if (!$file) {
            return redirect('admin')->with('error', 'could not upload file try again later');
        }
        return redirect('admin')->with('status', 'Sendername file uploaded');
    }
    
    public function downloadSampleHsm($file)
    {
        return Storage::download('samples/' . $file.'.xlsx', 'SampleCustomSmsExcel.xlsx');
    }
    public function downloadSampleAirtime($file)
    {
        return Storage::download('samples/' . $file.'.xlsx', 'SampleExcelForSendingAirtime.xlsx');
    }
    public function downloadSampleContact($file)
    {
        return Storage::download('samples/' . $file . '.xlsx', 'SampleContactListForSendingSMS.xlsx');
    }
    public function downloadSampleContacts($file)
    {
        return Storage::download('samples/' . $file . '.xlsx', 'SampleForUploadingBulkContacts.xlsx');
    }
    public function downloadSampleSenderApplication($file)
    {
       // return Storage::download('samples/' . $file . '.pdf', 'SenderNameApplicationForm.pdf');
        return Storage::download('samples/' . $file . '.docx', 'SenderNameApplicationForm.docx');

    }
}
