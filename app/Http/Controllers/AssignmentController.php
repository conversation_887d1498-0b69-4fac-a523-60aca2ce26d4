<?php

namespace App\Http\Controllers;

use App\Assignment;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class AssignmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        //
        $this->authorize('viewAny', [Assignment::class, $id]);
    }
    public function assign(Request $request, $id)
    {
        //
        $this->authorize('create', [Assignment::class, $id]);


        $assignments = Assignment::where('mobile_number', $request->customermobile)->get();
        foreach ($assignments as $assignment) {
            $assignment->isCurrent = false;
            $assignment->save();
        }


        $assignment = new Assignment();
        $assignment->channel = "from";
        $assignment->mobile_number = $request->customermobile;
        $assignment->project_id = $request->project_id;
        $assignment->assigned_at = Carbon::now();
        $assignment->assigned_by = Auth::id();
        $assignment->assigned_to = $request->user_id;
        $assignment->crm_status = "progress";
        $assignment->isCurrent = true;
        $assignment->updated_by = Auth::id();
        $assignment->created_by = Auth::id();
        $assignment->updated_by = Auth::id();
        $assignment->created_at = Carbon::now();
        $assignment->save();

        //


        return back()->with('status', 'Assigned successfully');
    }
    public function close(Request $request, $id)
    {
        //
        $this->authorize('create', [Assignment::class, $id]);
        if( Assignment::where('mobile_number', $request->customermobile)->where('isCurrent', 1)->count() == 0 ){
        return back()->withErrors('The connversation is Unassigned. Assign to an agent before closing');
    }

        $assignment = Assignment::where('mobile_number', $request->customermobile)->where('isCurrent', 1)->first();


            $assignment->isCurrent = false;
            $assignment->closed_at = Carbon::now();
           // $assignment->closed_by = Auth::id();
            $assignment->closed_reason =  $request->reason;
            $assignment->crm_status = "closed";
            $assignment->save();

        return back()->with('status', 'Engagement Closed successfully as '.$assignment->closed_reason);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, $id)
    {
        //
        $this->authorize('create', [Assignment::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
        $this->authorize('create', [Assignment::class, $id]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Assignment  $assignment
     * @return \Illuminate\Http\Response
     */
    public function show(Assignment $assignment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Assignment  $assignment
     * @return \Illuminate\Http\Response
     */
    public function edit(Assignment $assignment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Assignment  $assignment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Assignment $assignment)
    {
        //
        $this->authorize('update', $assignment);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Assignment  $assignment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Assignment $assignment)
    {
        //
        $this->authorize('delete', $assignment);
    }
}
