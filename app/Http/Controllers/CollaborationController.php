<?php

namespace App\Http\Controllers;

use App\Collaboration;
use Illuminate\Http\Request;
use App\Mail\Collabo;
use App\Mail\CollaboAccepted;
use App\Mail\NewRegistration;
use App\Project;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\User;
use Illuminate\Support\Facades\Log;
 

class CollaborationController extends Controller
{

    public function send(Request $request)
    {
        //

    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $this->authorize('viewAny', [Collaboration::class, $id]);

        $project = Project::find($id);
        $collaborations = Collaboration::where('project_id', $id)->get();
        //this id is in use
        return view('collaborations.index', compact('project', 'collaborations'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        //
        $this->authorize('create', [Collaboration::class, $id]);

        //crate a code, save it, email it,
        $validatedData = $request->validate([
            'project_id' => 'required|numeric',
            'role' => 'required|string',

            'email' => 'required|string|email|max:255',
        ]);

        $code = random_bytes(10);
        $code = uniqid("TZ");

        if(Auth::user()->email == $request->email ) {
            return back()->withErrors('You are the owner of the project. A collaborator MUST be ANOTHER person who you want to work on the same project with.');
            }
            if(Collaboration::where('project_id', $request->project_id)->where( 'email', 'like', $request->email .'%')->exists()) {
                return back()->withErrors('The user is already an existing collaborator for this project.');
                }
        $collaboration = new Collaboration();
        $collaboration->fill($request->all());
        $collaboration->collabo_code = $code;
        $collaboration->role = $request->role;
        $collaboration->email = $request->email;
        $collaboration->isActive = false;
        $collaboration->status = false;
        $collaboration->created_by = Auth::id();
        $collaboration->save();

        $to = [
            [
                'email' =>  $request->email,
                'name' => "Invitation to Collaborate",
            ]
        ];
        $bcc = [
            [
                'email' => "<EMAIL>",
                'name' => "Collaboration Invitation",
            ]
        ];
        Mail::to($to)->bcc($bcc)->send(new Collabo($collaboration));
        return back()->with('status', 'Invitation Successful');
    }

    public function processInvitation(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            //  'fname' => 'required|string|max:255',
            // 'lname' => 'required|string|max:255',
            'mobile' => 'required|regex:/^[+]?[0-9]{7,13}$/',
            'email' => 'required|string|email|max:255',
            //'password' => 'required|string|min:6|confirmed',
           // 'password' => 'string|min:6',
        ]);

        $code = $request->collabo_code;
        $email = $request->email;
        $collaboration = Collaboration::where('collabo_code', $code)->where('email',$email)->exists() ?
         Collaboration::where('collabo_code', $code)->where('email',$email)->first() : null;
        if (!$collaboration) {
            return  back()->with('status', 'Not a valid Collaboration(n)');
        }
        if ($collaboration->email != $request->email) {
            return  back()->with('status', 'Not a valid Collaboration(e)');
        }

        $verifyNumber = function ($phone) {  return preg_match("/^[+]?[0-9]{4,13}$/", $phone);
        };
        //double cleaning in addition to validation
        $cleanNumber = function ($phone) {
            $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
            return $clean_number;
        };
        $user_count = User::where('email', $request->email)->count();
        if ($user_count == 0) {
            $referral_code = uniqid('RecTz');
            $user =  User::create([
                'name' => $request->name,
                'mobile' => $cleanNumber($request->mobile),
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'isClerk' =>  1,
                'referral_code' =>  $referral_code,
                'source' =>  "collaboration",
            ]);
            $to = [ [ 'email' =>$request->email, 'name' => $request->name ] ];
            $bcc = [ [ 'email' => "<EMAIL>", 'name' => "New Registration", ] ];
            //Mail::to($to)->bcc($bcc)->queue(new NewRegistration($user));
            Mail::to($to)->bcc($bcc)->send(new NewRegistration($user));

        } else {
            $user = User::where('email', $request->email)->first();
        }

        $collaboration->status = true;
        $collaboration->isActive = true;
        $collaboration->user_id = $user->id;
        $collaboration->save();


        $to = [['email' =>  $collaboration->email,'name' => "Collaboration request Accepted"]];
        $cc = [['email' =>  Project::find($collaboration->project_id)->user()->first()->email,'name' => "Collaboration request Accepted"]];//check here
        $bcc = [['email' => "<EMAIL>",'name' => "Collaboration request Accepted"]];
        //Mail::to($to)->cc($cc)->bcc($bcc)->queue(new CollaboAccepted($collaboration));
        Mail::to($to)->cc($cc)->bcc($bcc)->send(new CollaboAccepted($collaboration));

        Auth::login($user, true);
        return redirect('dashboard');
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\Collaboration  $collaboration
     * @return \Illuminate\Http\Response
     */
    public function show(Collaboration $collaboration)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Collaboration  $collaboration
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request, Collaboration $collaboration)
    {
        //
        $this->authorize('update', $collaboration);

    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Collaboration  $collaboration
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $collaboration_id)
    {
        //
        $request->validate([
            'role' => 'string'
        ]);
        $collaboration = Collaboration::find($request->collaboration_id);
        if(!$collaboration) {
            Log::info('Collaboration doesnt exist');
            return back()->with('status', 'Collaborator has already removed.');
        }
       // $this->authorize('update', $collaboration);
       if($request->role === "remove") {
            $collaboration->delete();
            Log::info('Collaboration deleted', ['collaboration_id' => $collaboration->id]);
            return back()->with('status', 'Collaborator removed.');
        }
        $collaboration->role = $request->role;
        $collaboration->save();
        return back()->with('status', 'Collaborator updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Collaboration  $collaboration
     * @return \Illuminate\Http\Response
     */
    public function destroy(Collaboration $collaboration)
    {
        //
        $this->authorize('delete', $collaboration);
    }
}
