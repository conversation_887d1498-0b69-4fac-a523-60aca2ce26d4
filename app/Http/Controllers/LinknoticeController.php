<?php

namespace App\Http\Controllers;

use App\Linknotice;
use App\Project;
use Illuminate\Http\Request;

class LinknoticeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $this->authorize('viewAny', [Linknotice::class, $id]);

        $project = Project::find($id);
        $linknotices = Linknotice::where('project_id', $id)->latest()->get();
        return view('linknotices.index', compact('linknotices', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function show(Linknotice $linknotice)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function edit(Linknotice $linknotice)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Linknotice $linknotice)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Linknotice  $linknotice
     * @return \Illuminate\Http\Response
     */
    public function destroy(Linknotice $linknotice)
    {
        //
    }
}
