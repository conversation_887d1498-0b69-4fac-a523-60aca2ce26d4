<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLogDownloadRequest;
use App\Http\Requests\UpdateLogDownloadRequest;
use App\Models\LogDownload;

class LogDownloadController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreLogDownloadRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreLogDownloadRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\LogDownload  $logDownload
     * @return \Illuminate\Http\Response
     */
    public function show(LogDownload $logDownload)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\LogDownload  $logDownload
     * @return \Illuminate\Http\Response
     */
    public function edit(LogDownload $logDownload)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateLogDownloadRequest  $request
     * @param  \App\Models\LogDownload  $logDownload
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateLogDownloadRequest $request, LogDownload $logDownload)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\LogDownload  $logDownload
     * @return \Illuminate\Http\Response
     */
    public function destroy(LogDownload $logDownload)
    {
        //
    }
}
