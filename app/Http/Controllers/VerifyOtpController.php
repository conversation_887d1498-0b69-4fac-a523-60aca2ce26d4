<?php

namespace App\Http\Controllers;

use App\User;
use App\Project;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

use Illuminate\Http\Request;
use App\Mail\Otp;
use Illuminate\Support\Facades\Log;

class VerifyOtpController extends Controller
{
    //
    public function generateOtp(Request $request) 
    {
        Log::info($request->send);
        $code = rand(0, 99999);
        $user = $request->user();
        Log::info($user);
        if (!Auth::check()) {
            Log::error('User not authenticated');
            return redirect('/login');
        }
        $request->user()->fill([
            'otp' => Hash::make($code)
        ])->save();

        //save and send to subscriber
        if ($request->send == "email") {
            $to = [
                [
                    'email' =>  Auth::user()->email,
                    'name' =>  Auth::user()->name,
                ]
            ];
            $bcc = [
                [
                    'email' => "<EMAIL>",
                    'name' => "CTO",
                ]
            ];
            Log::info($to);
            Log::info($bcc);
            Log::info(Auth::user()->email);
            try {
                Mail::to($to)->bcc($bcc)->send(new Otp($code, $user->name));
                Log::error('OTP email sent: ');

            } catch (\Exception $e) {
                Log::error('Error sending OTP email: ' . $e->getMessage());
            }
            return redirect('/verifyOtp');
        } elseif ($request->send == "sms") {
            $primary_project = Project::where('name', 'Devs')->first();
            if ($primary_project == null) {
                return redirect('/verifyOtp');
            }
            Log::info($primary_project );
            $curl = curl_init();
            $to = Auth::user()->mobile;
            $url = "https://sozuri.net/api/v1/messaging";
            $data = [
                "project" => "Devs",
                'from' => 'Sozuri',
                'to' => $to,
                "campaign" => "OTP",
                "channel" => "sms",
                'message' => 'Your Sozuri verification code is: ' . $code,
                "type" => "promotional"
            ];
            Log::info($data);
            $curl = curl_init($url . '?' . http_build_query($data));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, [
                "accept: application/json",
                "authorization: Bearer " . $primary_project->api_token,
                "content-type: application/json"
            ]);

            $response = curl_exec($curl);
            Log::info($response);
            curl_close($curl);
            //$err = curl_error($curl);
            if ($response) {
                //echo "cURL Error #:" . $err;
                //dd($err);
                // return back()->withSuccess('SMS Sent' . $response);
                return redirect('/verifyOtp');
            }
        }

        return redirect('/generateOtp');
    }
    public function validateOtp(Request $request)
    {
        $otp_to_verify = $request->otp;
        Log::info($otp_to_verify);
        if (Hash::check($otp_to_verify, $request->user()->otp)) {
            $request->user()->fill([
                'otp' => null,
                'otp2' => null,
                'otpVerified' => true
            ])->save();

            $user = User::find($request->user()->id);
            // $user->otp = null;
            // $user->otp2 = null;
            //$user->otpVerified = true;
            // $user->save();

            return redirect('/dashboard')->with('status', 'Account Verified');
        }
        //$request->session()->flas('status','')
        return redirect('/verifyOtp')->withErrors('Wrong OTP');
    }
}
