<?php

namespace App\Http\Controllers;

use App\Smscallback;
use Illuminate\Http\Request;
use App\Project;

class SmscallbackController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $this->authorize('viewAny', [Smscallback::class, $id]);
        $project = Project::find($id);
        $smscallbacks = Smscallback::where('project_id', $id)->orderBy('created_at','desc')->get();
        return view('smscallbacks.index', compact('smscallbacks','project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        //
        $this->authorize('create', [Smscallback::class, $id]);

    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request,$id)
    {
        $this->authorize('create', [Smscallback::class, $id]);

        $request->validate([
            'project_id' => 'required',
           // 'smscallback_id' => 'required'
        ]);
        $smscallback = new Smscallback();

        if($request->filled('secure')){ $request->merge(['secure' => 1]);
        }else{  $request->merge(['secure' => 0]) ;
        }
        $smscallback->fill($request->all());
        $smscallback->save();
        $request->session()->flash('message', 'Ready to go! Smscallback Created');
        return redirect('projects/'.$id.'/messages/smscallbacks');

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Smscallback  $smscallback
     * @return \Illuminate\Http\Response
     */
    public function show(Smscallback $smscallback)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Smscallback  $smscallback
     * @return \Illuminate\Http\Response
     */
    public function edit( $pid, $id)
    {
        $project = Project::find($pid);
        $smscallback = Smscallback::find($id);
        $this->authorize('update', $smscallback);

        return view('smscallbacks.edit', compact('project','smscallback'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Smscallback  $smscallback
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $pid, $id)
    {
        $project = Project::find($request->project_id);
        $smscallback = Smscallback::find($request->smscallback_id);
        $this->authorize('update', $smscallback);

        //$this->authorize('update', User::class, Smscallback::class);
        $request->validate([
            'project_id' => 'required',
            'smscallback_id' => 'required'
        ]);
        if($request->filled('secure')){ $request->merge(['secure' => 1]);
        }else{  $request->merge(['secure' => 0]) ;
        }

              $smscallback->update($request->except('smscallback_id'));
        $request->session()->flash('message','Smscallback Updated');
        return redirect('projects/'.$request->project_id.'/messages/smscallbacks');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Smscallback  $smscallback
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {

      //  $this->authorize('delete', Smscallback::class);
      $smscallback = Smscallback::find($id);
      $this->authorize('delete', $smscallback);

      $smscallback->delete();
      $request->session()->flash('message','Smscallback: '.$smscallback->name.' deleted');
      return redirect('projects/'.$pid.'/messages/smscallbacks');
    }
}
