<?php

namespace App\Http\Controllers;

use App\Keyword;
use App\Project;
use App\Sms;
use Illuminate\Http\Request;

class KeywordController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $this->authorize('viewAny', [Keyword::class, $id]);

        $project = Project::find($id);
        $keywords = Keyword::where('project_id', $id)->latest()->get();
        return view('keywords.index', compact('keywords', 'project'));
    }
    public function sms($id) {
               //
               $this->authorize('viewAny', [Keyword::class, $id]);

               $project = Project::find($id);
               $keyword = Keyword::find($id);

               $this->authorize('viewAny', [Sms::class, $id]);
               $smses = SMS::where('project_id',$id)->whereNotNull('keyword')->latest()->get();

               return view('sms.index', compact('smses', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        //
        $this->authorize('create', [Keyword::class, $id]);

        $request->validate([
            'name' => 'required',
            'project_id' => 'required',
            'detail' => 'required',
            'company_name' => 'required'
        ]);
        $project = Project::find($id);

        Keyword::create($request->all());
        $request->session()->flash('message','Ready to go! Keyword Requested');

        $keywords = Keyword::where('project_id', $id)->latest()->get();
        return view('keywords.index', compact('keywords', 'project'));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function show(Keyword $keyword)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function edit(Keyword $keyword)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Keyword $keyword)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Keyword  $keyword
     * @return \Illuminate\Http\Response
     */
    public function destroy(Keyword $keyword)
    {
        //
    }
}
