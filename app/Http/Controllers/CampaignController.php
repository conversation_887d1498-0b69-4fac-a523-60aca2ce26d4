<?php

namespace App\Http\Controllers;

use App\Campaign;
use Illuminate\Http\Request;
use Auth;
use App\Project;
use Illuminate\Support\Facades\DB;
use App\Models\InteractiveSms;
class CampaignController extends Controller
{
    public function campaignReport($pid) {
        $this->authorize('viewAny', [Campaign::class, $pid]);
        ini_set('memory_limit', '30000M');
        ini_set('max_execution_time', 360);
        ini_set('default_socket_timeout', '-1');

        $project = Project::find($pid);
        //$interactiveSms = InteractiveSms::where('project_id', $pid)->get();
        //                <td>{{\App\Campaign::where('id', $sms->campaign_id)->value('name')}}</td>
        return view('campaigns.report', compact('project'));
    }

    public function getCampaignCosts(Request $request, $pid)
    {
        //
        $this->authorize('viewAny', [Campaign::class, $pid]);
        $count = 1;
        $response = "<p>";
        foreach (Campaign::where('project_id', $pid)->limit(10)->get() as $campaign) {
            $clicks = DB::table('sms')->select('campaign_id', 'price', 'clicked_at')->where('campaign_id', $campaign->id)->get();
            $response .= $count . ". Name: " . $campaign->name . ", Total SMS:" . $clicks->count() . ", Clicked: " . $clicks->whereNotNull('clicked_at')->count() . ", Cost: " . $clicks->sum('price') . "<br/>";
            $count++;
        }
        $response .= "</p>";

        return response()->json([ 'status' => 'success', 'clicks' =>  $response ]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        //
       // $this->authorize('viewAny', [Campaign::class, $id]);

        $project = Project::find($id);
        $campaigns = Campaign::where('project_id', $id)->orderBy('created_at', 'desc')->get();
        return view('campaigns.index', compact('campaigns', 'project'));

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        $this->authorize('create', [Contact::class, $id]);
        $campaigns = ""; //Campaign::all();
        return view('campaigns.create', compact('campaigns'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        $request->validate([
            'project_id' => 'required'
        ]);
        //$org_id = $request->user()->project_id;
        //$request->merge(['project_id'=> $org_id]);
        $this->authorize('create', [Campaign::class, $id]);
        Campaign::create([  "project_id" => $id,
        "name" => $request->name,
        "goal" => $request->goal,
        "start" => $request->start,
        "end" => $request->end,
        "sponsor" =>  $request->sponsor,
        "demographics" =>  $request->demographics,
        "budget" =>  $request->budget,
        "target_sms" =>  $request->target_sms,
        "detail" => $request->detail]);
        $request->session()->flash('message', 'Ready to go! Campaign Created');

        return redirect('projects/' . $id . '/campaigns');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Campaign  $campaign
     * @return \Illuminate\Http\Response
     */
    public function show(Campaign $campaign)
    {
        //

    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Campaign  $campaign
     * @return \Illuminate\Http\Response
     */
    //public function edit(Campaign $campaign,$id)
    public function edit($pid, $id)
    {
        $project = Project::find($pid);
        $campaign = Campaign::find($id);
        $this->authorize('update', $campaign);


        return view('campaigns.edit', compact('project', 'campaign'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Campaign  $campaign
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $pid, $id)
    {
        $project = Project::find($pid);
        $campaign = Campaign::find($id);
        $campaign = Campaign::find($request->campaign_id);

        $this->authorize('update', $campaign);

        $request->validate([
            'project_id' => 'required',
            'campaign_id' => 'required'
        ]);

        $this->authorize('update', $campaign);
        //$campaign->update($request->all());
        $campaign->update([
            "name" => $request->name,
            "goal" => $request->goal,
            "start" => $request->start,
            "end" => $request->end,
            "sponsor" =>  $request->sponsor,
            "detail" => $request->detail
        ]);
        $request->session()->flash('message', 'Campaign Updated');
        return redirect('projects/' . $request->project_id . '/campaigns');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Campaign  $campaign
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $pid, $id)
    {
        //
        $campaign = Campaign::find($id);

        $this->authorize('delete', $campaign);

        //$this->authorize('delete', Campaign::class);
        $campaign->delete();
        $request->session()->flash('message', 'Campaign: ' . $campaign->name . ' deleted');
        return redirect('projects/' . $pid . '/campaigns');
    }
}
