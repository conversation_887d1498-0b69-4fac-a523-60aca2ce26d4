<?php

namespace App\Http\Controllers;

use App\Shortcode;
use Illuminate\Http\Request;
use App\Project;
use Illuminate\Support\Facades\Mail;
use App\Mail\AppNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShortcodeController extends Controller
{
    // Upload files/images
    public function checkSaveFile($file)
    { if(!$file) {
        return null;
    
    }else{
        $allowedfileExtension = ['jpg', 'jpeg', 'png', 'docx', 'doc', 'pdf'];
        $extension = strtolower($file->getClientOriginalExtension());
        //$name = $file->getClientOriginalName();
        // $extension = $request->file('avatar')->extension();
        $check = in_array($extension, $allowedfileExtension);
        if ($check) {
            //return $file->storeAs( 'documents', $name );
            return $file->store('public/shortcodekycdocs');
    }

}
}


public function chat(Request $request, $id)
{

    $project = Project::find($id);
    $shortcode = Shortcode::where(['project_id' => $id, 'status' => 'active'])->exists() ?  Shortcode::where(['project_id' => $id, 'status' => 'active'])->first()->name : "";
    //$messages = DB::table('sms_monthly_copy')->where(['project_id' => $id, ])->where('to', $shortcode->name)->orWhere('from', $shortcode->name)->latest()->get();
    return view('whatsapp.new-chat', compact('project', 'shortcode'));
}

public function fetchChatMessages(Request $request, $projectId, $customerNumber)
{
   // $customerNumber = $request->customerNumber;
   // $projectId = auth()->user()->project->id; // Adjust this according to how you get the project_id

    // Fetch messages from WhatsApp and SMS tables for the given customer number
    $wappMessages = DB::table('wappmessages')
        ->where('project_id', $projectId)
        ->where(function ($query) use ($customerNumber) {
            $query->where('to', $customerNumber)
                  ->orWhere('from', $customerNumber);
        })
        ->select('id', 'message', 'created_at', 'type', 'data', 'channel', 'from', 'to', 'message_id')
        ->get();

    $smsMessages = DB::table('interactive_sms')
        ->where('project_id', $projectId)
        ->where(function ($query) use ($customerNumber) {
            $query->where('to', $customerNumber)
                  ->orWhere('from', $customerNumber);
        })
        ->select('id', 'message', 'created_at', 'type', 'data', 'channel', 'from', 'to', 'message_id')
        ->get();

    // Combine both message sets, order by created_at
    $allMessages = collect($wappMessages)
        ->merge($smsMessages)
        ->sortBy('created_at');

    // Render the HTML for the messages
    return view('whatsapp.partials.chat-messages', compact('allMessages', 'customerNumber'))->render();
}



    
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        $this->authorize('viewAny', [Shortcode::class, $id]);

        $project = Project::find($id);
        $shortcodes = Shortcode::where('project_id', $id)->orderBy('created_at', 'desc')->get();
        return view('shortcodes.index', compact('shortcodes', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        $this->authorize('create', [Shortcode::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        $this->authorize('create', [Shortcode::class, $id]);
        $request->validate([
            'name' => 'required',
            'project_id' => 'required',
            //'shortcode_id' => 'required',
            'detail' => 'required',
            //'company_name' => 'required',
            //'company_industry' => 'required',
            //'company_address' => 'required',
            //'company_email' => 'required',
            'application_letter' => 'nullable',
            //'company_cert' => 'required',
            //'company_kra' => 'required',
            //'company_representative_id' => 'required'
        ]);
        $application_letter = $request->file('application_letter');
        $company_cert = $request->file('company_cert');
        $company_kra = $request->file('company_kra');
        $company_representative_id = $request->file('company_representative_id');


        $shortcode = new Shortcode();
        $shortcode->project_id =  $id;
        $shortcode->code =  uniqid('sozprem');
        //$shortcode->application_letter =  $this->checkSaveFile($application_letter);
        $shortcode->company_cert =  $this->checkSaveFile($company_cert);
        $shortcode->company_kra =  $this->checkSaveFile($company_kra);
        $shortcode->company_representative_id =  $this->checkSaveFile($company_representative_id);
        $shortcode->name =  $request->name;
        $shortcode->detail =  $request->detail;
        $shortcode->type =  $request->type;
        $shortcode->telco =  $request->telco;
        $shortcode->company_name =  $request->company_name;
        $shortcode->company_industry =  $request->company_industry;
        $shortcode->company_address =  $request->company_address;
        $shortcode->company_email =  $request->company_email;
        $shortcode->save();

        $project = Project::find($id)->name;
        //Mail user owning the project  ->send mail to default queue
        $emailTo = "<EMAIL>";
        $bcc = "<EMAIL>";
        $message = "Hi, someone just requested for a shortcode for project: " . $project;
        $subject = "New Sender ID request";
        Mail::to($emailTo)->bcc($bcc)->queue(new AppNotification($message, $subject));


        //Shortcode::create($request->all());
        $request->session()->flash('message', 'Ready to go! Shortcode Requested');
        return redirect('projects/' . $request->project_id . '/shortcodes');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function show(Shortcode $shortcode)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function edit($pid, $id)
    {
        //

        $project = Project::find($pid);
        $shortcode = Shortcode::find($id);
        $this->authorize('update', $shortcode);

        return view('shortcodes.edit', compact('project', 'shortcode'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $pid, $id)
    {
        return;
        $project = Project::find($request->project_id);
        $shortcode = Shortcode::find($request->shortcode_id);

        $this->authorize('update', $shortcode);

        //$this->authorize('update', User::class, Shortcode::class);

        $request->validate([
            'name' => 'required',
            'project_id' => 'required',
            'shortcode_id' => 'required',
            'detail' => 'required',
            //'company_name' => 'required',
            //'company_industry' => 'required',
            //'company_address' => 'required',
            //'company_email' => 'required',
            'application_letter' => 'nullable',
            //'company_cert' => 'required',
            // 'company_kra' => 'required',
            //'company_representative_id' => 'required'
        ]);
        $application_letter = $request->file('application_letter');
        $company_cert = $request->file('company_cert');
        $company_kra = $request->file('company_kra');
        $company_representative_id = $request->file('company_representative_id');

        $request->merge([
            //'application_letter' =>  $this->checkSaveFile($application_letter),
            'company_cert' =>  $this->checkSaveFile($company_cert),
            'company_kra' =>  $this->checkSaveFile($company_kra),
            'company_representative_id' =>  $this->checkSaveFile($company_representative_id)
        ]);


        $this->authorize('update', $shortcode);
        $shortcode->update($request->except('shortcode_id'));
        $request->session()->flash('message', 'Shortcode Updated');
        return redirect('projects/' . $request->project_id . '/shortcodes');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Shortcode  $shortcode
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $pid, $id)
    {
        $shortcode = Shortcode::find($id);
        $this->authorize('delete', $shortcode);

        $shortcode->delete();
        $request->session()->flash('message', 'Shortcode: ' . $shortcode->name . ' deleted');
        return redirect('projects/' . $pid . '/shortcodes');
    }
}
