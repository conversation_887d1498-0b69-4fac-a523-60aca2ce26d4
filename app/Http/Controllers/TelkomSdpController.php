<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use App\Subscriber;
use App\Premium;
use App\Premiumsms;
use App\Linknotice;
use App\Jobs\ProcessSendpremium;
use App\Traits\Atsendsms;
use App\Sms;
use Illuminate\Support\Carbon;
use App\Jobs\ProcessSendbulk;
use App\Project;
use App\Jobs\ProcessSdp;
use App\Shortcode;
use Illuminate\Support\Facades\DB;

class TelkomSdpController extends Controller
{
    public function bulkdlr(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('telkombulkdlr: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        //Log::info($data);
        $stringBody = (string) $x; // Explicitly cast the body to a string // +OK|8248779844099083167|Success|20201207-154901
        //Log::info('stringbody' . $stringBody);
        $code = http_response_code(); // get current response code
//200/201 – OK
//400 – Bad Request
//500 – Internal Server Error


        if ($code == '200' || $code == '201') {
            $codes = 'success';
        } elseif ($code == '400') {
            $codes = 'Bad Request';
        } elseif($code == '500') {
            $codes = 'Internal Server Error';
        }else{
             $codes = 'deliveryImpossible';
        }
        //original bulk sms
        $sms = Sms::where(['trace_id' => '200'])->exists() ? Sms::where(['trace_id' => '200'])->first() : null;
        $sms !== null ? $sms->update(['status' => $codes, 
        'detail' => $codes, 
        'description' => $codes, 
        'status_code' => $code]) : '';
        $project = $sms !== null ? Project::find($sms->project_id) : null;
        $callback = $project !== null && $project->smscallbacks()->count() > 0 ? $project->smscallbacks()->first()->deliveryCallbackUrl : null;
        /*  //retry if not successful
    $package_id = config('app.package_id'); //$AT_FROM; //env('AT_FROM');

    // if failed, ie.  NETWORK FAILURE or DELIVERY IMPOSSIBLE  retry
    if ( $deliveryStatus === 1001 ||  $deliveryStatus === 1 ||  $deliveryStatus === 5 || $deliveryStatus === 11 || $deliveryStatus === 13 || 
        $deliveryStatus === 31 || $deliveryStatus === 32 ||  $deliveryStatus === 34 || $deliveryStatus === 99) {
        $token = $this->getToken();
        //populate with previous sms details and add the retry key
        $raw_smses = [
            'message_id' => $sms->message_id, 'credits' => $sms->credits, 'to' => $sms->to, 'status' => $sms->status,
            'status_code' => $sms->status_code,
            'bulk_id' => $sms->bulk_id,
            'message_part' => $sms->message_part,
            'project_id' => $sms->project_id,
            'message' => $sms->message, 'from' => $sms->from,
            'created_at' => $sms->created_at,
            'send_at' => $sms->send_at, 'campaign_id' => $sms->campaign_id, 'telco' => $sms->telco, 'retry' => 1
        ];
        $sms_payload = $raw_smses;
        //if telco is not unknown and is safaricom
        if ($raw_smses['telco'] == 'safaricom' && $raw_smses['status'] == 'accepted') {
            ProcessSendbulk::dispatch($raw_smses, $token, $package_id)->onQueue('high'); //no delay
        }
    } elseif ($deliveryStatus === 6 ||  $deliveryStatus === 27) {//ABSENT SUBSCRIBER
        //just continue
    }
*/
        if ($callback != null && $sms !== null) {
            $base_uri = $callback; //config('app.at_base_uri'); //'http://164.132.96.155'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', '', [
                'headers' => [
                    'Content-Type'     => 'application/json',
                    'Accept'     => 'application/json',
                ],
                'json' => ['project' => $project->name, 'message_id' =>  $sms->message_id, 'channel' => 'sms', 'status' =>  $codes, 'network' =>  'safaricom', 'type' => 'Bulk Delivery', 'timestamp' => time()] //add auth key from smscallbacks etc
            ]);
            $code = $response->getStatusCode();
            $reason = $response->getReasonPhrase();
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string
            //update sms
            $sms->link_notify_response_from_customer_body = $stringBody;
            $sms->save();

           // Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('stringbody from customer airtel bulk dlr' . $stringBody);
            $data = json_decode($stringBody, true);
           // Log::info($data);
        }
        return response()->json([
            'status' => 'Successs',
            "responseTimeStamp" => date('Ymdhi'),
            "operation" => "BULK_NOTIFICATION"
        ]);
    }
}
