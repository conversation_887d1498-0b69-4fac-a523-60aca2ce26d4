<?php

namespace App\Http\Controllers;

use App\Wappmessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Project;
use App\Waccount;
use GuzzleHttp\Client;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Wnumber;
use App\Wwebhook;
use Exception;
use Illuminate\Support\Facades\Auth;
use App\Services\AutomationService;

class WappmessageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        //$this->authorize('viewAny', [Sms::class, $id]);
        if(!Auth::user()->isGlobalAdmin) {
            return back();
        }
        $project = Project::find($id);
        $whatsappmessages = Wappmessage::where('project_id', $id)->latest()->get();
        Log::info($whatsappmessages->count() . "whatsapp messages for project " .$project->name);

        if(DB::table('sandbox_numbers')->where('project_id', $id)->exists()){
        $request->session()->flash('message', 'Are you enjoying testing ? To add your real business number, go to: Whatsapp > Whatsapp Settings and fill in Whatsap Account form. We will initiate your setup ASAP.');
        }else {
        $request->session()->flash('message', 'You can test Whatsapp Business API by adding Your Phone Number to Sozuri Whatsapp Sandbox. Go to this menu: Whatsapp > Whatsapp Settings  to add your number and begin testing.');

        }

        return view('whatsapp.chat', compact('project', 'whatsappmessages'));
    }
    

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function send(Request $request)  //from whatsapp business number
    {
        Log::info('sending whatsapp');
        Log::info($request->all());
        $request->validate([
            'from' => 'required',
            'to' => 'required',
            'type' => 'required|string',
            'channel' => 'required'
        ]);
        $api_request = $request->filled('project_id') ? false : true;
        $project = $request->filled('project_id') ? DB::table('projects')->where('id', $request->project_id)->first() :
            DB::table('projects')->where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["MessageData" => ["Error" => "Unknown project."]]); //ens
        }
        $project_id =  $project->id;
        Log::info('whatsapp project ' . $project->name);
        //select campaign
        if ($request->filled('campaign')) {
            $campaign_id = DB::table('campaigns')->where('name', 'like', $request->campaign)->where('project_id', $project->id)->exists() ?
                DB::table('campaigns')->where('name', 'like', $request->campaign)->where('project_id', $project->id)->first()->id :
                DB::table('campaigns')->insertGetId(['name' => $request->campaign, 'goal' => $request->campaign, 'project_id' => $project->id]);
        } else {
            $campaign_id = DB::table('campaigns')->where('project_id', $project->id)->exists() ? DB::table('campaigns')->where('project_id', $project->id)->first()->id : null;
        }
        Log::info('whatsapp campaign ' . $campaign_id);
        $payments = DB::table('payments')->where('project_id', $project->id)->where('balance', '>', 5)->where('status', 'like', 'success')->get();
        if ($payments->sum('balance') <  10) {
            Log::info('insufficient funds ' . $project_id);
            return response()->json(["MessageData" => ["Error" => "Insufficient balance. Top up and try again."]]);
        }
        //make type promotional by default
        $type =   strtolower($request->type);
        //$businessNumber = $this->e164Formatter254($this->cleanNumber($businessNumber));
        //$to = $this->e164Formatter254($this->cleanNumber($request->to));
        $businessNumber = $this->cleanNumber($request->from);
        $to = $this->cleanNumber($request->to);
        $w_number = Wnumber::where('number', $businessNumber)->exists() ? Wnumber::where('number',  $businessNumber)->first() : null;
        if ($w_number == null) {
            return response()->json(["error" => "Whatsapp Number could not be found"]); //ensure project is resolved
        }
        Log::info('outbound whatsapp number: ' . $w_number->number);

        //is parent number sandbox, if yes, source must be among sandbox_numbers
        $is_w_number_sandbox = $w_number->number_type == "sandbox"  ? true : false;
        if ($is_w_number_sandbox) {
            Log::info('is sandbox');
            $sandbox_customer = DB::table('sandbox_numbers')->where('number', $to)->exists() ? DB::table('sandbox_numbers')->where('number', $to)->first()  : false;
            if (!$sandbox_customer) {
                Log::info('missing sandbox customer');
                return response()->json([
                    "message" => "The given data was invalid",
                    "errors" => [
                        "to" => [$businessNumber . " is a Sandbox Whatsapp Business number which can only send messages to sandbox customer numbers. Add " . $to . " as a sandbox number from your Sozuri project menu: Whatsapp > settings , to be able to use it for testing."]
                    ]
                ]);
            }
        }
        $project_id = $is_w_number_sandbox ? $sandbox_customer->project_id : $w_number->project_id;
        $w_account = Waccount::where('id', $w_number->waccount_id)->exists() ?
            Waccount::where('id', $w_number->waccount_id)->first() : null;
        if ($w_account == null) {
            Log::info('missing waccount');
            return response()->json(["error" => "Whatsapp Account could not be found"]); //ensure project is resolved
        }
        Log::info('outbound whatsapp account ' . $w_number->number);

        $contextMessageId = null;
        if ($request->filled('context')) {
            $request->validate([
                'context.message_id' => 'required|string',
            ]);
            $message_id = $request->input('context.message_id');
            if (Wappmessage::where('message_id', $message_id)->exists()) {
                $contextMessageId =  $message_id;
            } else {
                return response()->json([
                    "message" => "Error",
                    "errors" => [
                        "message_id" => ["The provided message_id does not exist."]
                    ]
                ]);
            }
        }
        if ($type == "text") {
            $request->validate([
                'text.preview_url' => 'nullable|boolean',
                'text.body' => 'required|string',
            ]);
            $content =  [
                "preview_url" => $request->text["preview_url"] ?? false,
                "body" => $request->text["body"]
            ];
        } elseif ($type == "reaction") {
            $message_id = $request->input('reaction.message_id');
            if (Wappmessage::where('message_id', $message_id)->exists()) {
                // The message_id exists in the table, proceed with validation
                $request->validate([
                    'reaction.message_id' => 'required|string',
                    'reaction.emoji' => 'required|string',
                ]);
            } else {
                return response()->json([
                    "message" => "Error",
                    "errors" => [
                        "message_id" => ["The provided message_id does not exist."]
                    ]
                ]);
            }
            $content =  [
                "message_id" => $request->reaction["message_id"],
                "emoji" => $request->reaction["emoji"]
            ];
        } elseif ($type == "audio" || $type == "document" || $type == "image" || $type == "sticker" || $type == "video") {
            $request->validate([
                "{$type}.link" => 'required|url'
            ]);
            $content = [
                "link" => $request->{$type}["link"]  //or "id"
            ];
        } elseif ($type == "location") {
            $request->validate([
                'location.longitude' => 'required|numeric',
                'location.latitude' => 'required|numeric',
                'location.name' => 'required|string',
                'location.address' => 'required|string',
            ]);
            $content =  $request->location;
        } elseif ($type == "contacts") {
            $request->validate([
                'contacts' => 'required|array',
                'contacts.*.addresses' => 'required|array',
                'contacts.*.addresses.*.street' => 'nullable|string',
                'contacts.*.addresses.*.city' => 'nullable|string',
                'contacts.*.addresses.*.state' => 'nullable|string',
                'contacts.*.addresses.*.zip' => 'nullable|string',
                'contacts.*.addresses.*.country' => 'nullable|string',
                'contacts.*.addresses.*.country_code' => 'nullable|string',
                'contacts.*.addresses.*.type' => 'nullable|string|in:HOME,WORK',
                'contacts.*.birthday' => 'nullable|string', // You might want to define a more specific date format validation rule here.
                'contacts.*.emails' => 'required|array',
                'contacts.*.emails.*.email' => 'nullable|string|email',
                'contacts.*.emails.*.type' => 'nullable|string|in:HOME,WORK',
                'contacts.*.name' => 'required|array',
                'contacts.*.name.formatted_name' => 'nullable|string',
                'contacts.*.name.first_name' => 'nullable|string',
                'contacts.*.name.last_name' => 'nullable|string',
                'contacts.*.name.middle_name' => 'nullable|string',
                'contacts.*.name.suffix' => 'nullable|string',
                'contacts.*.name.prefix' => 'nullable|string',
                'contacts.*.org' => 'required|array',
                'contacts.*.org.company' => 'nullable|string',
                'contacts.*.org.department' => 'nullable|string',
                'contacts.*.org.title' => 'nullable|string',
                'contacts.*.phones' => 'required|array',
                'contacts.*.phones.*.phone' => 'nullable|string',
                'contacts.*.phones.*.type' => 'nullable|string|in:HOME,WORK',
                'contacts.*.phones.*.wa_id' => 'nullable|string', // You might want to define a more specific validation rule for WhatsApp IDs.
                'contacts.*.urls' => 'required|array',
                'contacts.*.urls.*.url' => 'nullable|string|url',
                'contacts.*.urls.*.type' => 'nullable|string|in:HOME,WORK',
            ]);
            $content =  $request->contacts;
        } elseif ($type == "interactive") {
            $request->validate([
                'interactive' => 'required|array',
                'interactive.type' => 'required|string|in:list,button',
                'interactive.header' => 'nullable|array',
                'interactive.header.type' => 'nullable|string|in:text',
                'interactive.header.text' => 'nullable|string',
                'interactive.body' => 'nullable|array',
                'interactive.body.text' => 'nullable|string',
                'interactive.footer' => 'nullable|array',
                'interactive.footer.text' => 'nullable|string',
                'interactive.action' => 'required|array',
                'interactive.action.button' => 'nullable|string',
                'interactive.action.sections' => 'nullable|array',
                'interactive.action.sections.*' => 'nullable|array',
                'interactive.action.sections.*.title' => 'nullable|string',
                'interactive.action.sections.*.rows' => 'nullable|array',
                'interactive.action.sections.*.rows.*' => 'nullable|array',
                'interactive.action.sections.*.rows.*.id' => 'nullable|string',
                'interactive.action.sections.*.rows.*.title' => 'nullable|string',
                'interactive.action.sections.*.rows.*.description' => 'nullable|string',
                'interactive.action.buttons' => 'nullable|array',
                'interactive.action.buttons.*' => 'nullable|array',
                'interactive.action.buttons.*.type' => 'nullable|string|in:reply',
                'interactive.action.buttons.*.reply' => 'nullable|array',
                'interactive.action.buttons.*.reply.id' => 'nullable|string',
                'interactive.action.buttons.*.reply.title' => 'nullable|string',
            ]);
            $content =  $request->interactive;
        }
        Log::info($content);
        $base_uri = config('app.whatsapp_base_uri');
        $token = $w_number->uid;
        try {
            $message_id = $this->sendMessage($token, $base_uri, $businessNumber, $to, $type, $content, $contextMessageId, $w_number->code);
            $status = "sent";
            $description = "submitted";
        } catch (Exception $e) {
            Log::info($e->getMessage());
            $status = "sent";
            $description = "submission failure14";
            $message_id = bin2hex(random_bytes(20));
        } 
            //create the message
            try {
                $message_id = bin2hex(random_bytes(20));

                $wmessage = new Wappmessage();
                $wmessage->project_id =  $project_id;
                $wmessage->uuid =  bin2hex(random_bytes(20)); //internal
                $wmessage->message_id =  $message_id; //whatsapp 
                $wmessage->waccount_id = $w_account->id;
                $wmessage->wnumber_id = $w_number->id;
                $wmessage->context_message_id = $contextMessageId;
                $wmessage->channel = "whatsapp";
                $wmessage->type = $type;
                $wmessage->data = json_encode($content);
                $wmessage->cost = 0;
                $wmessage->refund = 0;
                $wmessage->source = $businessNumber;
                $wmessage->destination = $to;
                $wmessage->from = $businessNumber;
                $wmessage->to = $to;
                $wmessage->content_type = $type;
                $wmessage->sent_at = now();
                //$wmessage->delivered_at = ;
                $wmessage->status = $status;
                $wmessage->direction = "outbound";
                $wmessage->redact = "";
                $wmessage->whatsapp_fee = 0.00;
                $wmessage->platform_fee = 0.00;
                $wmessage->provider_fee = 0.5;
                $wmessage->status_code = "";
                $wmessage->description = $description;
                $wmessage->details = "";
                $wmessage->events_url = null;
                $wmessage->rejected_reason = null;
                $wmessage->active = null;
                $wmessage->archived = null;
                $wmessage->save();
                Log::info('message saved');
            } catch (Exception $e) {
                Log::info($e->getMessage());
                return response()->json([
                    "message" => "Error",
                    "errors" => [
                        "message" => ["General failure. Contact <EMAIL> for assistance"]
                    ]
                ]);
            }
            Log::info("whatsapp send outbound message saved");
            $response =  [[
                'message_id' => $wmessage->message_id,
                'to' =>  $wmessage->to,
                'status' =>   $wmessage->status
                //'cost' =>  "n"
            ]];
            return response()->json(["messageData"  => ["messages" => 1], "recipients" => $response]);
        
    }
    function sendMessage($token, $base_uri, $businessNumber, $to, $type, $content, $contextMessageId = null, $code)
    {
        if ($contextMessageId) {
            $data = [
                "messaging_product" => 'whatsapp',
                "recipient_type" => 'individual', //whatsapp business number
                "to" => $to, //recipient or customer +254
                "type" =>  $type,
                $type => $content,
                "context" => ["message_id" => $contextMessageId]
            ];
        } else {
            $data = [
                "messaging_product" => 'whatsapp',
                "recipient_type" => 'individual', //whatsapp business number
                "to" => $to, //recipient or customer +254
                "type" =>  $type,
                $type => $content
            ];
        }
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', $code . '/messages', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $token,
            ],
            'json' => $data
        ]);
        //Log::info($response);
        
           /*{} "messaging_product": "whatsapp", 
            "contacts": [
              {
                "input": "<INPUT>",// customer phone number that the message was sent to. 
                "wa_id": "<WA_ID>" //WhatsApp ID of the customer who the message was sent to. Th
              }
            ],
            "messages": [
              {
                "id": "<ID>"//WhatsApp message ID. You can use the ID listed after "wamid." to track your message status.
              }
            ]
          }*/
        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('wapp send message response: ' .  $stringBody);
        $data = json_decode($stringBody, true);
        return $data["messages"][0]["id"];
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Wappmessage  $wmessage
     * @return \Illuminate\Http\Response
     */
    public function show(Wappmessage $wmessage)
    {
        //
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wappmessage  $wmessage
     * @return \Illuminate\Http\Response
     */
    public function edit(Wappmessage $wmessage)
    {
        //
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wappmessage  $wmessage
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wappmessage $wmessage)
    {
        //
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wappmessage  $wmessage
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wappmessage $wmessage)
    {
        //
    }
    private function cleanNumber($phone)
    {
        //return  preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
        return  preg_replace("/[^0-9]/", "", $phone);

    }
    private function e164Formatter254($phone)
    {
        $forced_numbber = substr($phone, -9);
        return '254' . $forced_numbber;
    }
    private function e164Formatter($phone)
    {
        return substr($phone, -12);
    }
    public function createSandboxCustomerNumber(Request $request)
    {
        $request->validate([
            'customer_number' => 'required',
            'project_id' => 'required',
            'sandbox_business_number_id' => 'required'
        ]);
        $customer_number = $this->cleanNumber($request->customer_number);
        $project_id = $request->project_id;
        $sandbox_business_number_id = $request->sandbox_business_number_id;
        $parent_wnumber = Wnumber::where('id', $sandbox_business_number_id)->exists() ?  Wnumber::where('id', $sandbox_business_number_id)->first() : null;
        Log::info('parent wnumber id : ' . $parent_wnumber->id);
        if (DB::table('sandbox_numbers')->where('number', $customer_number)->exists()) {
            DB::table('sandbox_numbers')->where('number', $customer_number)->delete();
            $message= "This number was already attached to another project\'s sandbox. It has been reassociated";
        }
        try {
            DB::table('sandbox_numbers')->insert([
                'project_id' => $project_id,
                'uid' => null,
                'number' => $customer_number,
                'code' => uniqid('sandbx'),
                'wnumber_id' => $parent_wnumber->id,
                'waccount_id' => $parent_wnumber->waccount_id,
                'wwebhook_id' => $parent_wnumber->webhook_id,
                'status' => 'new',
                'number_type' => "sandbox_new",
                'country' => 'KE',
                'service' => 'whatsapp',
                'rented_at' => null,
                'rental_rate' => null,
                'setup_rate' => null,
                'sozuri_rate' => null,
                'inbound_message_rate' => null,
                'platform_auto_recharge' => null,
                'sozuri_auto_recharge' => null,
                'platform_credit_balance' => null,
                'sozuri_credit_balance' => null,
                'active' => false,
                'archived' => false,
                'created_by' => Auth::user()->id,
                'updated_by' => Auth::user()->id,
            ]);
        } catch (Exception $e) {
            Log::info('sanbox error' . $e->getMessage());
            return back()->withMessage('Sorry. Sandbox number Could not be added. Try again later.');
        }
        return back()->withMessage('Successfully added sandbox number.'.$message);
    } //
    public function receive(Request $request) //to business number
    {
        $jsonObject = file_get_contents('php://input');
        Log::info($request->all());
        if ($request->has('hub_mode') && $request->input('hub_mode') === 'subscribe') {
            if ($request->input('hub_verify_token') === config('app.WHATSAPP_VERIFY_TOKEN')) {
                return $request->input('hub_challenge');
            } else {
                return "unauthorized action";
            }
        } //verify

        Log::info("inbound whatsapp messages" . $jsonObject);
        $data = json_decode($jsonObject, true);
        $decodedObject = json_decode($jsonObject, true);
        // Extracting the values
        $objectType = $decodedObject['object'];
        $whatsappAccountId = $decodedObject['entry'][0]['id'];
        $metadata = $decodedObject['entry'][0]['changes'][0]['value']['metadata'];
        $displayPhoneNumber = $metadata['display_phone_number'];
        $phoneNumberId = $metadata['phone_number_id'];

        $isStatusNotification = isset($decodedObject['entry'][0]['changes'][0]['value']['statuses']) ? true : false;
        $isMessageNotification = isset($decodedObject['entry'][0]['changes'][0]['value']['messages']) ? true : false;

        if ($isMessageNotification) {
            Log::info("message notification\n");
            $contacts = $decodedObject['entry'][0]['changes'][0]['value']['contacts'];
            $contactName = $contacts[0]['profile']['name'];
            $contactPhoneNumber = $contacts[0]['wa_id'];
            $messages = $decodedObject['entry'][0]['changes'][0]['value']['messages'];
            try {
                $processedStatus = $this->processReceivedMessage($messages, $displayPhoneNumber);
            } catch (Exception $e) {
                Log::info($e->getMessage());
                $processedStatus = "error";
            }
        } elseif ($isStatusNotification) {
            Log::info("status notification\n");
            $statuses = $decodedObject['entry'][0]['changes'][0]['value']['statuses'];
            try {
                $processedStatus = $this->processReceivedStatus($statuses, $displayPhoneNumber);
            } catch (Exception $e) {
                Log::info($e->getMessage());
                $processedStatus = "failed";
            }
        } else {
            $processedStatus = "unknown error";
        }
        return $processedStatus;
    }
    public function processReceivedStatus($statuses, $displayPhoneNumber)
    {

        $id = $statuses[0]['id'];

        $status = $statuses[0]['status'];
        $timestamp = $statuses[0]['timestamp'];
        $recipientId = $statuses[0]['recipient_id'];
        if (!Wappmessage::where('message_id', $id)->exists()) {
            Log::info("message for the status notificication not found");
            return "message not found";
        }
        $wmessage =  Wappmessage::where('message_id', $id)->first();

        if ($status == "failed") {
            /*
                {"object":"whatsapp_business_account","entry":[{"id":"***************","changes":[{"value":{"messaging_product":"whatsapp","metadata":{"display_phone_number":"************","phone_number_id":"***************"},"statuses":[{"id":"wamid.HBgMMjU0NzI1MTY0MjkzFQIAERgSMUI1NDg5ODNBMzhDN0Q3RERFAA==","status":"failed","timestamp":"**********","recipient_id":"************","errors":[{"code":131047,"title":"Re-engagement message","message":"Re-engagement message","error_data":{"details":"Message failed to send because more than 24 hours have passed since the customer last replied to this number."},"href":"https:\/\/developers.facebook.com\/docs\/whatsapp\/cloud-api\/support\/error-codes\/"}]}]},"field":"messages"}]}]}
            */
            Wappmessage::where('message_id', $id)->update(["status" => $status]);
            Log::info('updated failed status notification');
            $webhook_callback = Wwebhook::where('project_id', $wmessage->project_id)->value('url');
            $error = $statuses[0]['errors'][0]['title'] ?? "unknown";
            $data =   [
                'project' => Project::where('id', $wmessage->project_id)->value('name'),
                'channel' => "whatsapp",
                'phone_number' => $displayPhoneNumber,
                'statuses' => [
                    'recipient' => $recipientId,
                    'message_id' => $wmessage->message_id,
                    'status' => $status,
                    "type" => "status",
                    "error" => $error
                ]
            ];
            $this->sendCallback($webhook_callback, $data);
        }


        $conversationId = $statuses[0]['conversation']['id'];
        $expirationTimestamp = $statuses[0]['conversation']['expiration_timestamp'];
        $originType = $statuses[0]['conversation']['origin']['type']; //user_initiated

        $billable = $statuses[0]['pricing']['billable'];
        $pricingModel = $statuses[0]['pricing']['pricing_model']; // CBP
        $category = $statuses[0]['pricing']['category']; //user_initiated

        $wmessage = Wappmessage::where('message_id', $id)->exists() ? Wappmessage::where('message_id', $id)->first() : null;
        if ($wmessage) {
            Log::info('message for this status notification exists');
            $wmessage->status = $status; //sent, delivered,
            $wmessage->conversation_id = $conversationId;
            $wmessage->timestamp = $timestamp;
            $wmessage->expiration_timestamp = $expirationTimestamp;
            $wmessage->conversation_origin = $originType;
            $wmessage->provider_pricing_model = $pricingModel;
            $wmessage->billable = $billable;
            $wmessage->pricing_category = $category;
            $wmessage->whatsapp_fee = 0.00;
            $wmessage->platform_fee = 0.00;
            $wmessage->provider_fee = 0.5;
            $wmessage->status_code = "";
            $wmessage->description = $status;
            $wmessage->save();
            Log::info('whatsapp inbound receive status saved');
            //shoot callback to webhook
            $data =   [
                'project' => Project::where('id', $wmessage->project_id)->value('name'),
                'channel' => "whatsapp",
                'phone_number' => $displayPhoneNumber,
                'statuses' => [
                    'recipient' => $recipientId,
                    'message_id' => $wmessage->message_id,
                    'status' => $status,
                    "type" => "status",
                    "conversation_id" => $conversationId,
                    'expiration_timestamp' => $expirationTimestamp,
                    "category" => $category
                ]
            ];
            $webhook_callback = Wwebhook::where('project_id', $wmessage->project_id)->value('url');
            $this->sendCallback($webhook_callback, $data);
            return "success";
        }

        Log::info('message for this status notification was not found');
    }

    public function processReceivedMessage($messages, $displayPhoneNumber)
    {
        $messageFrom = $messages[0]['from'];
        $messageId = $messages[0]['id'];
        $messageTimestamp = $messages[0]['timestamp'];
        //$messageBody = $messages[0]['text']['body'];
        $messageType = $messages[0]['type'];
        $messageContent = $messages[0][$messageType];
        $destination = $this->cleanNumber($displayPhoneNumber);
        $source = $this->cleanNumber($messageFrom);
        $w_number = Wnumber::where('number', $destination)->exists() ? Wnumber::where('number', $destination)->first() : null;
        Log::info($w_number);
        Log::info($source);

        Log::info('inbound from whatsapp. business destination number: ' . $destination);
        if ($w_number == null) {
            Log::info('whatsapp business number not found');
            return response()->json(["error" => "Whatsapp Number could not be found"]); //ensure project is resolved
        }
        //is parent number sandbox, if yes, source must be among sandbox_numbers
        $is_w_number_sandbox = $w_number->number_type == "sandbox" ? true : false;
        if ($is_w_number_sandbox) {
            $sandbox_customer = DB::table('sandbox_numbers')->where('number', $source)->exists() ? DB::table('sandbox_numbers')->where('number', $source)->first() : false;
            if (!$sandbox_customer) {
                Log::info('Add your number as a sandbox number ');
                return response()->json(["error" => "This number requires a sandbox number. Add your number as a sandbox number from your Sozuri project."]); //ensure project is resolved
            }
        }
        $project_id = $is_w_number_sandbox ? $sandbox_customer->project_id : $w_number->project_id;
        $project = Project::find($project_id);
        $w_account = Waccount::where('id', $w_number->waccount_id)->exists() ?
            Waccount::where('id', $w_number->waccount_id)->first() : null;
        if ($w_account == null) {
            return response()->json(["error" => "Whatsapp Account could not be found"]); //ensure project is resolved
        }
        Log::info('inbound whatsapp account business number ' . $w_number->number);
        $contextMessageId = $messages[0]["context"]["id"] ?? null; //$messages[0]["context"]["id"]
        $wmessage = new Wappmessage();
        $wmessage->project_id = $project_id;
        $wmessage->message_id = bin2hex(random_bytes(20)); //internal
        $wmessage->uuid = $messageId; //whatsapp 
        $wmessage->waccount_id = $w_account->id;
        $wmessage->wnumber_id = $w_number->id;
        $wmessage->context_message_id = $contextMessageId;
        $wmessage->channel = "whatsapp";
        $wmessage->type = $messageType;
        $wmessage->data = json_encode($messageContent);
        $wmessage->cost = 0;
        $wmessage->refund = 0;
        $wmessage->source = $messageFrom;
        $wmessage->destination = $displayPhoneNumber;
        $wmessage->from = $messageFrom;
        $wmessage->to = $displayPhoneNumber;
        $wmessage->content_type = $messageType;
        $wmessage->sent_at = now();
        //$wmessage->timestamp = $messageTimestamp;
        $wmessage->status = ""; //$status;
        $wmessage->direction = "inbound";
        $wmessage->redact = "";
        $wmessage->whatsapp_fee = 0.00;
        $wmessage->platform_fee = 0.00;
        $wmessage->provider_fee = 0.5;
        $wmessage->status_code = "";
        $wmessage->description = ""; //$description;
        $wmessage->details = "";
        $wmessage->events_url = null;
        $wmessage->rejected_reason = null;
        $wmessage->active = null;
        $wmessage->archived = null;
        $wmessage->save();
        Log::info('whatsapp inbound receive message saved'); //run this using a service and raw sql
        //shoot callback to webhook
        if ($contextMessageId) {
            $data = [
                [
                    'project' => Project::where('id', $project_id)->value('name'),
                    'channel' => "whatsapp",
                    'to' => $displayPhoneNumber,
                    'messages' => [
                        "context" => ["message_id" => $contextMessageId, "from" => $wmessage->from],
                        'from' => $wmessage->from,
                        'message_id' => $wmessage->message_id,
                        'timestamp' => $messageTimestamp,
                        "type" => $messageType,
                        "{$messageType}" => $messageContent,
                    ]
                ]
            ];
        } else {
            $data = [
                [
                    'project' => Project::where('id', $project_id)->value('name'),
                    'channel' => "whatsapp",
                    'to' => $displayPhoneNumber,
                    'messages' => [
                        'from' => $wmessage->from,
                        'message_id' => $wmessage->message_id,
                        'timestamp' => $messageTimestamp,
                        "type" => $messageType,
                        "{$messageType}" => $messageContent,
                    ]
                ]
            ];
        }
        $webhook_callback = Wwebhook::where('project_id', $project_id)->value('url');
        $this->sendCallback($webhook_callback, $data);

        AutomationService::createContact($project_id, $wmessage->from);
        // Check if the JSON contains a "body" field, otherwise ignore automation.since message is media. maybe check captions etc
        $data = json_decode($wmessage->data, true);
        if (isset($data['body'])) {
            $bodyValue = $data['body'];
            echo "Body: $bodyValue\n";
            AutomationService::automate($project, $bodyValue, $wmessage->from, "whatsapp");
        } else {
            echo "No body field found.\n";
        }
        return "success";
    }


    public function getMessages() {
        $messages = Wappmessage::latest()->get();
        return view('admin.whatsapp-messages', compact('messages'));
    }
    public function getProjectMessages($projectId)
    {
        $this->authorize('viewAny', [Sms::class, $projectId]);

        $project = Project::find($projectId);
        Log::info('whatsapp project ' . $project->name);
        $messages = Wappmessage::where('project_id', $projectId)->latest()->get();
        Log::info($messages->count() . "whatsapp messages for project ".$project->name);
        return view('whatsapp.messages', compact('project', 'messages'));
    }
    public function sendCallback($webhook_callback, $data)
    {
        $callback_array = explode(',', $webhook_callback);
        foreach ($callback_array as $webhook) {
            if ($webhook == null) {
                continue;
            }
            try {
                $time_start = microtime(true);
                Log::info("whatsapp webhook being sent for inbound message" . $webhook);
                $base_uri = $webhook;
                $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
                $response = $client->request(
                    'POST',
                    '',
                    [
                        'headers' => [
                            'Content-Type'     => 'application/json',
                            'Accept'     => 'application/json',
                        ],
                        'json' => $data,
                        'timeout' => 5, // Set timeout to 10 seconds
                    ]
                );
                $body = $response->getBody();
                $stringBody = (string) $body;
                Log::info('inbound whatsapp webhook request completed in:' . (string)(microtime(true) - $time_start) . "\n" . $stringBody);
            } catch (Exception $e) {
                Log::info("sender error " . $e->getMessage());
            }
        }
        return true;
    }
}
