<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePromptRequest;
use App\Http\Requests\UpdatePromptRequest;
use App\Models\Prompt;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Mail\AppNotification;
use Illuminate\Support\Facades\Mail;

class PromptController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    public function whatsappWebhook(Request $request, $pid)
    {
        Log::info($request->all());
        $json_str = file_get_contents('php://input');
        Log::info('whatsapp incoming: ' . $json_str);
        if ($request->filled('hub_mode')) {
            $hub_challenge = $request->hub_challenge;
            $hub_verify_token = $request->hub_verify_token; //"spicywaba"
            if ($hub_verify_token == "spicywaba") {
                Log::info("successful verification");
                return $hub_challenge;
            } else {
                Log::error("failed verification");
                return "err";
            }
        }
        //extract whatsapp message
        $json_obj = json_decode($json_str);
        var_dump($json_obj);
        $display_phone_number = $json_obj->entry[0]->changes[0]->value->metadata->display_phone_number;
        $phone_number_id = $json_obj->entry[0]->changes[0]->value->metadata->phone_number_id;
        $name = $json_obj->entry[0]->changes[0]->value->contacts[0]->profile->name;
        $wa_id = $json_obj->entry[0]->changes[0]->value->contacts[0]->wa_id;
        $from = $json_obj->entry[0]->changes[0]->value->messages[0]->from;
        $id = $json_obj->entry[0]->changes[0]->value->messages[0]->id;
        $timestamp = $json_obj->entry[0]->changes[0]->value->messages[0]->timestamp;
        $text = $json_obj->entry[0]->changes[0]->value->messages[0]->text->body;

        //send AI request
        $myRequest = new Request();
        $myRequest->setMethod('POST');
        $myRequest->request->add(['prompt' => $text, 'aiEmail' => $name.':'.$from, 'chatbuddy' => true]);
        $aiResponse = $this->generate($myRequest, 1);
        $jsonResponse = $aiResponse->getContent();

        Log::info($jsonResponse);
        $response_obj = json_decode($jsonResponse);
        var_dump($response_obj);
        $status = $response_obj->status;
        $response_text = $response_obj->response;

        //send response to whatsapp number
        $whatsappReplyResponse = $this->sendWhatsappMessage($from, $response_text, $id);
        //save to db
        exit;
    }

    private function sendWhatsappMessage(string $to, string $message, string $messageId = null)
    {
$message = str_replace("\n\n", "", $message);


        $curl = curl_init();
        $data = '{"messaging_product": "whatsapp", "to": "'.$to.'", "type": "text", "text": {"preview_url": false, "body": "'.$message.'"}}';
        Log::info($data);
        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://graph.facebook.com/v16.0/116754754716556/messages',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          //CURLOPT_POSTFIELDS =>'{"messaging_product": "whatsapp", "to": "254725164293", "type": "template", "template": { "name": "hello_world", "language": { "code": "en_US" } }}',
          CURLOPT_POSTFIELDS =>$data,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json',
            'Authorization: Bearer EAAQxKdGZCESsBAKLOVSu5wvEOEcghO8gBn3ZCydqZCbP5Y52dIQl94cZAEyREUZCzg5sE2ZArq0qkP8m8kZASW9f9M5LnmvsxU6yQyCNwXhE0IAba1tpR2ZBuoyYZB74SI30vSqIVbQgNZB5A2uwqzdOZCy5jSxNiUq1wpJefUAxTLjwxTVcdZB2P59UjwNO30vosLzPsI7Sge6ToLXcdZBZACb9uhOH264n8AOjgZD'
          ),
        ));
      
        $response = curl_exec($curl);
        
        curl_close($curl);
        echo $response;
        Log::info($response);
/*
        $client = new Client();
        $headers = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer EAAQxKdGZCESsBAKLOVSu5wvEOEcghO8gBn3ZCydqZCbP5Y52dIQl94cZAEyREUZCzg5sE2ZArq0qkP8m8kZASW9f9M5LnmvsxU6yQyCNwXhE0IAba1tpR2ZBuoyYZB74SI30vSqIVbQgNZB5A2uwqzdOZCy5jSxNiUq1wpJefUAxTLjwxTVcdZB2P59UjwNO30vosLzPsI7Sge6ToLXcdZBZACb9uhOH264n8AOjgZD'
        ];
   
        $body = '{"messaging_product": "whatsapp",
                "to": "' . $to . '",
                "type": "text",
                "context": {
                    "message_id": "' . $messageId . '"
                },
                "text": {
                    "preview_url": False,
                    "body": "' . $message . '"
                }
            }';
            Log::info($body);

        $request =  new \GuzzleHttp\Psr7\Request('POST', 'https://graph.facebook.com/v16.0/116754754716556/messages', $headers, $body);
        $res = $client->sendAsync($request)->wait();
        
        $responseBody = $res->getBody();



        Log::info($responseBody);
        */
        return  $response;
        
        /*sending
             /*
            $body = '{
            "messaging_product": "whatsapp",
            "to": "'. $to.'",
            "type": "template",
            "template": {
                "name": "hello_world",
                "language": {
                "code": "en_US"
                }
            }
            }';
                {
                "messaging_product": "whatsapp",
                "recipient_type": "individual",
                "to": "PHONE_NUMBER",
                "type": "text",
                "text": { // the text object
                    "preview_url": false,
                    "body": "MESSAGE_CONTENT"
                    }
                }'

                    expect  
            {
            "messaging_product": "whatsapp",
            "contacts": [
                {
                "input": "16505076520",
                "wa_id": "16505076520"
                }
            ],
            "messages": [
                {
                "id": "wamid.HBgLMTY1MDUwNzY1MjAVAgARGBI5QTNDQTVCM0Q0Q0Q2RTY3RTcA"
                }
            ]
            }
            */
    }


    public function generate(Request $request, $pid)
    {
        Log::info($request->all());
        $x = file_get_contents('php://input');
        Log::info('whatsapp incoming: ' . $x);
        if ($request->filled('hub_mode')) {
            $hub_challenge = $request->hub_challenge;
            $hub_verify_token = $request->hub_verify_token; //"spicywaba"
            if ($hub_verify_token == "spicywaba") {
                Log::info("successful verification");
                return $hub_challenge;
            } else {
                Log::error("failed verification");
                return "err";
            }
        }
        $request->validate(['prompt' => 'required', 'textLength' => 'nullable|numeric']);
        if ($pid != 1) {
            $this->authorize('viewAny', [Sms::class, $pid]);
        } else {
            $emailTo = "<EMAIL>";
            $cc = "<EMAIL>";
            $bcc = "<EMAIL>";
            $subject = $request-> filled('chatbuddy') ? "New Whatsapp Chatbuddy request: ". date('h:m  m-Y') : "New anonymous Sozu AI bot request from Login page: " . date('h:m  m-Y');
            $message = "Prompt: " . $request->prompt . "\n <br> Email: " . $request->aiEmail;
            Mail::to($emailTo)->send(new AppNotification($message, $subject));
            Log::info('ai email sent: ' . $subject . $message);
        }
        Log::info($request->aiEmail);
        $prompt = $request->prompt;

$model = config('app.openai_model'); // Ensure this is set to "gpt-4o-mini" or the desired model
$openai_base_uri = config('app.openai_base_uri');
$openai_key = config('app.openai_key');
$openai_org = config('app.openai_org');

// Create a new Guzzle client instance
$client = new Client(['base_uri' => $openai_base_uri, 'verify' => false]);

// Define the request payload
$payload = [
    'model' => $model,
    'messages' => [
        [
            'role' => 'system',
            'content' => 'You are a helpful assistant.'
        ],
        [
            'role' => 'user',
            'content' => $prompt
        ]
    ],
    'temperature' => 0.2,
    'max_tokens' => 200
];

try {
    // Send the POST request
    $response = $client->request('POST', '/v1/chat/completions', [
        'headers' => [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => 'Bearer ' . $openai_key,
            'OpenAI-Organization' => $openai_org
        ],
        'json' => $payload,
        'timeout' => 240, // Response timeout
        'connect_timeout' => 240 // Connection timeout
    ]);

    // Get the response details
    $code = $response->getStatusCode(); // 200
    $reason = $response->getReasonPhrase(); // OK
    $body = $response->getBody()->getContents(); // Get the response body


} catch (\Exception $e) {
    // Handle any exceptions that may occur
    Log::error("Error: " . $e->getMessage());
}

        Log::info($body);
        $data = json_decode($body, true);

    // Extract parameters
    $choices = $data['choices'];
    $created = $data['created'];
    $id = $data['id'];
    $model = $data['model'];
    $object = $data['object'];
    $usage = $data['usage'];

    // Handle choices
    $choice = $choices[0]; // Assuming you want the first choice
    $finish_reason = $choice['finish_reason'];
    $index = $choice['index'];
    $message = $choice['message'];
    $message_content = $message['content'];
    $message_role = $message['role'];
    $logprobs = $choice['logprobs'];

    // Usage parameters
    $completion_tokens = $usage['completion_tokens'];
    $prompt_tokens = $usage['prompt_tokens'];
    $total_tokens = $usage['total_tokens'];


        DB::table('prompts')->insert([
            "project_id" =>  $pid,
            "model" =>  $model,
            "prompt" =>  $prompt,
            "temperature" =>  0,
            "max_tokens" =>  200,
            "top_p" =>  1,
            "frequency_penalty" =>  0.0,
            "presence_penalty" =>  0.0,
            "stop" => null, //["\n"],
            "open_id" => $id,//isset($body["id"]) ?? null,
            "object" => $object,//$body["object"],
            "created" => $created ,//$body["created"],
            "model" => $model,//$body["model"],
            "text" =>  $message_content ,//$text = $body["choices"][0]["text"],
            "index" => $index,//$body["choices"][0]["index"],
            "logprobs" => $logprobs,//$body["choices"][0]["logprobs"],
            "finish_reason" => $finish_reason ,//$body["choices"][0]["finish_reason"],
            "prompt_tokens" => $prompt_tokens,//$body["usage"]["prompt_tokens"],
            "completion_tokens" => $completion_tokens,//$body["usage"]["completion_tokens"],
            "total_tokens" => $total_tokens,//$body["usage"]["total_tokens"],
            "created_at" => now()
        ]);
        return response()->json(['status' => 'success', 'response' => $message_content]);
    }

    public function summarize()
    {
        //
    }
    public function shortenChars()
    {
        //
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StorePromptRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StorePromptRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Prompt  $prompt
     * @return \Illuminate\Http\Response
     */
    public function show(Prompt $prompt)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Prompt  $prompt
     * @return \Illuminate\Http\Response
     */
    public function edit(Prompt $prompt)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdatePromptRequest  $request
     * @param  \App\Models\Prompt  $prompt
     * @return \Illuminate\Http\Response
     */
    public function update(UpdatePromptRequest $request, Prompt $prompt)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Prompt  $prompt
     * @return \Illuminate\Http\Response
     */
    public function destroy(Prompt $prompt)
    {
        //
    }
}
