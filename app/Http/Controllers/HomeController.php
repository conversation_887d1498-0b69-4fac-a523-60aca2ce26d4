<?php

namespace App\Http\Controllers;
use App\Project;
use Illuminate\Support\Facades\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Talklog;
class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //return view('home');
        $projects = Project::where('user_id','=',Auth::id())->get();
        $smss = DB::table('sms')->get()->toArray();
        $sms = array_column($smss, 'id');
        $time = array_column($smss, 'created_at');

        $sms = json_encode($sms,JSON_NUMERIC_CHECK);
        $time = json_encode($time,JSON_NUMERIC_CHECK);

        Log::info('User accessed projects dashboard' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User accessed dashboard" . Auth::id(),
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMehod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);


//copy the log code/ add log and talklog classes to class, editcode to reflect resource

        $request->session()->flash('status', 'Your Projects');

        return view('projects.index',compact('sms','time','projects'))->with('status','talkzuri is performing early tests before beta.
        Data you commit is considered demo and may be altered.');

    }
}
