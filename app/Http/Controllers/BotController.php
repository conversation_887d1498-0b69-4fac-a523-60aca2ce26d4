<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Exception;
use Illuminate\Support\Facades\Log;

class BotController extends Controller
{

    /**
     * This script is responsible for:
     * 1. receiving a whatsapp message
     * 2. processing the message to find possible interpretetions and responses
     * 3. sending a reply response back to sender
     */
    function index()
    {
        $x = file_get_contents('php://input');
        Log::info('bot food incoming ' . $x);
        $data = json_decode($x, true);
        $recipients = $data['recipients'][0];
        //authenticate the request
        $bot = new BotController();
        $bot->processMessage($recipients);
        Log::info('bot response sent incoming');
    }

    public function processMessage($data)
    {
        $raw_message = strtolower($data['message']);
        $to = $data['from']; //from becomes to
        $from = "+{{DB::table('wnumbers')->where('number_type','sandbox')->value('number')}}"; //always its this since we are only replying. to 
        $project = "Devs"; //we know this
        if ($data['description'] != "customer message") {
            Log::info('not customer message');
            return;
        }
        //format number
        // $destination = $this->e164Formatter254($this->cleanNumber($data['destination']));

        //check register
        $register   = '1';
        $find_register = strpos($raw_message, $register);
        //check near location
        $place  = '2';
        $find_place = strpos($raw_message, $place);

        //check Deposit
        $deposit  = '3';
        $find_deposit = strpos($raw_message, $deposit);

        //check withdraw
        $withdraw  = '4';
        $find_withdraw = strpos($raw_message, $withdraw);

        //check statement
        $statement  = '5';
        $find_statement = strpos($raw_message, $statement);

        //check loan
        $loan  = '6';
        $find_loan = strpos($raw_message, $loan);

        //block 
        $block  = '7';
        $find_block = strpos($raw_message, $block);

        //agent 
        $agent  = '8';
        $find_agent = strpos($raw_message, $agent);

        //check balance
        $balance   = 'bal';
        $find_balance = strpos($raw_message, $balance);

        //send money
        $send   = 'send';
        $find_send = strpos($raw_message, $send);

        $statement   = 'statement';
        $find_statement = strpos($raw_message, $statement);

        $location  = 'location';
        $find_location = strpos($raw_message, $location);
        // Note our use of ===.  Simply == would not work as expected
        // because the position of 'a' was the 0th (first) character.
        if ($find_balance !== false) {
            Log::info('matched balane');
            $reply_message =  "One moment.. I'm fetching your balance.";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
            sleep(5);

            $reply_message =  "Total Available balance for XXXX1208 is Ksh. 159,552.63. Actual Bal: Ksh. 197,223.54";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
            //balance

            sleep(60);
            $reply_message =  "Tip.\n You can send the word 'balance' to get your account balance, 'statement' to get a mini statement,  or 'send' to send money";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } elseif ($find_register !== false) {
            $reply_message =  "Its simple to get started in three steps \n -Take a clear of your  National ID . \n -Take a clear selfie picture of yourself holding your ID. \n -Reply with the two clear pictures and you're set!";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } elseif ($find_place !== false) {
            $reply_message =  "One moment.. I'm fetching the locations.";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);

            sleep(3);
            $contentType = "text";
            $reply_message =  "Here is the nearest Fintech Bank branch to your location. \n -Enterprise ABC Place.";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);

            sleep(2);
            $contentType = "location";
            $longitude = "36.8088";
            $latitude = "-1.3086";
            $label = "Fintech Ltd. Payments provider";
            $address = "Issah, Haji Complex, Nairobi";
            $reply_message =  "location";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType, $longitude, $latitude, $label, $address);


            sleep(6);
            $contentType = "media";
            $mediaUrl = "https://les-articleimg.s3.amazonaws.com/17397_AdobeStock_111553575.jpeg";
            $mediaCaption = "We cant wait to meet you!";
            $reply_message =  "media";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType, $longitude, $latitude, $label, $address, $mediaUrl, $mediaCaption);
        } elseif ($find_send !== false) {
            $reply_message =  "Reply with \n 1. To send to another Fintech Account. \n 2. To send to MPESA \n 3. Send to PESALINK \n 4. Logout";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //statement
        elseif ($find_statement !== false) {
            $reply_message =  "Here is a mini statement for your account XXX139: \n Ksh 5.000 debited for CRD-OOO-XXX on 25/01/2022. \n -Ksh 1500.00 sent to Shirleen Kiragu-Mpes ********* on 26/01/2022. \n -Ksh 1500.00 sent to Johnstone Pesalink 846678 on 21/02/2022.";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //location
        elseif ($find_deposit !== false) {
            $reply_message =  "To deposit to your Fintech Account. Go to \nMpesa > \nLipa na Mpesa > \nPaybill > \nEnter Business Number:  4039323. \nEnter Account number: $to";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //deposit

        elseif ($find_withdraw !== false) {
            $reply_message =  "To withdraw from your Fintech Account. Choose account to withdraw from\n 10. FC4039323 \n 11. RT09876";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //withdraw

        elseif ($find_statement !== false) {
            $reply_message =  "Select the period of statement below:\n\n 12. 1 Week \n 13. 1 Month \n 14. 6 Months \n 15. 1 Year";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //statement

        elseif ($find_loan !== false) {
            $reply_message =  "We have a variety of Loans. Please select an option below.\n \n 16. Business Loan \n 17. School Loan \n 18. Personal Loan \n 19. LPO Loan \n 20. Asset Finance";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //loans

        elseif ($find_block !== false) {
            $reply_message =  "Please reply with an option to confirm or cancel Blocking of ATM card associated with Account FC786023 and Mobile: $to \n \n 21. Confirm \n 22. Cancel \nYou will need to visit a brach physically to request a new ATM card.";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //block
        elseif ($find_agent !== false) {
            $reply_message =  "Thank you for choosing Fintech &#128525;. An agent will respond to you shortly.";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        } //agent

        elseif ($find_location !== false) {
            $reply_message =  "One moment... I'm fetching the locations.";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);

            sleep(3);
            $contentType = "text";
            $reply_message =  "Here is the nearest Fintech Bank branch to your location. \n -Enterprise ABC Place.";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);

            sleep(2);
            $contentType = "location";
            $longitude = "36.8088";
            $latitude = "-1.3086";
            $label = "Fintech Ltd. Payments provider";
            $address = "Issah, Haji Complex, Nairobi";
            $reply_message =  "location";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType, $longitude, $latitude, $label, $address);

            sleep(6);
            $contentType = "media";
            $mediaUrl = "https://les-articleimg.s3.amazonaws.com/17397_AdobeStock_111553575.jpeg";
            $mediaCaption = "We cant wait to meet you!";
            $reply_message =  "media";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType, $longitude, $latitude, $label, $address, $mediaUrl, $mediaCaption);
        }
        //new message
        else {
            $reply_message =  "Hi, My name is Fini.\nWelcome to Fintech Banking. I found that you are new.\n\nPlease select from the below list by typing the option number (eg. 1 ) to :\n ``` 1. Register for Fintech Banking.\n 2. Find the nearest Fintech branch \n 3. Deposit Funds \n 4. Withdraw request \n 5. Get Statement \n 6. Loans \n 7. Block ATM card \n 8. Contact Customer Service```";
            $token = "zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ";
            $contentType = "text";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
            sleep(60);
            $reply_message =  "Tip.\n You can send the word 'balance' to get your account balance, 'statement' to get a mini statement,  or 'send' to send money";
            $this->sendMessage($from, $to, $reply_message, $project, $token, $contentType);
        }
    }
    /**
     * send message  // "https://image.shutterstock.com/image-illustration/beautiful-aurora-universe-milky-way-260nw-**********.jpg",      /"Ohw, isnt it Beautiful",
     */
    public function sendMessage($from, $to, $message, $project, $token, $contentType, $longitude = null, $latitude = null, $label = null, $address = null, $mediaUrl = null, $mediaCaption = null)
    {
        //https://sozuri.net/api/v1/whatsapp?project=Devs&from=+{{DB::table('wnumbers')->where('number_type','sandbox')->value('number')}}&campaign=Whatsapp test&channel=whatsapp&message=Hey&type=text&to=+************&apiKey=
        $time_start = microtime(true);
        $client = new Client(['base_uri' =>  'https://sozuri.net/api/', 'verify' => false]);
        try {

            $response = $client->request('POST', 'v1/whatsapp', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept'     => 'application/json',
                    'authorization'     => 'Bearer ' . $token,
                ],
                'json' => [
                    'project' =>  $project, 'from' =>  $from, 'to' => $to, 'campaign' => 'Alerts', 'channel' => 'whatsapp', 'message' =>  $message, 'contentType' => $contentType,
                    "type" => "conversation",
                    "longitude" => $longitude,
                    "latitude" => $latitude,
                    "label" => $label,
                    "address" => $address,
                    "mediaUrl" => $mediaUrl,
                    "mediaCaption" => $mediaCaption

                ]
            ]);
            $body = $response->getBody();
            $stringBody = (string) $body;
            echo date('Y-m-d H:i:s') . ' request completed in:' . (string)(microtime(true) - $time_start) . "\n" . $stringBody;
        } catch (Exception $e) {
            echo "sender error" . $e->getMessage() . "\n";
        }
    }
}
