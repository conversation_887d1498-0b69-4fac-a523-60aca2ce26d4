<?php

namespace App\Http\Controllers;

use App\Crud;
use Illuminate\Http\Request;
use Faker\Generator;
use Illuminate\Http\Response;

class CrudController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        return response(Crud::all()->jsonSerialize(), Response::HTTP_OK);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Generator $faker)
    {
        //
        $crud = new Crud();
        $crud->name = $faker->lexify('????????');
        $crud->color = $faker->boolean ? 'red' : 'green';
        $crud->save();

      
        return response($crud->jsonSerialize(), Response::HTTP_CREATED);

       // {"name":"bxfvsfwi","color":"green","updated_at":"2020-04-16 10:08:38",
       //     "created_at":"2020-04-16 10:08:38","id":1}
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Crud  $crud
     * @return \Illuminate\Http\Response
     */
    public function show(Crud $crud)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Crud  $crud
     * @return \Illuminate\Http\Response
     */
    public function edit(Crud $crud)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Crud  $crud
     * @return \Illuminate\Http\Response
     */
   // public function update(Request $request, Crud $crud)
    public function update(Request $request, $id)

    {
        //
        $crud = Crud::findOrFail($id);
        $crud->color = $request->color;
        $crud->save();

  return response(null, Response::HTTP_OK);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Crud  $crud
     * @return \Illuminate\Http\Response
     */
    //public function destroy(Crud $crud)
    public function destroy($id)

    {
        //
        Crud::destroy($id);

        return response(null, Response::HTTP_OK);
    }
}
