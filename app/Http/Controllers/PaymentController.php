<?php

namespace App\Http\Controllers;

use App\Payment;
use Illuminate\Http\Request;
use App\Traits\Ravepay;
use App\Traits\Flutterwavepay;
use App\Project;
use App\Paymentmethod;
use App\Paymentstatus;
use App\Transaction;
use App\Transactiontype;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use App\Plan;
use App\Enrollment;
use Illuminate\Support\Facades\DB;
use App\Traits\CalculateCredits;
use Illuminate\Support\Facades\Mail;
use App\Mail\PaymentCompleted;
use App\Jobs\PostLedgers;

class PaymentController extends Controller
{

    use Ravepay;
    use CalculateCredits;
    use Flutterwavepay;
    public function e164Formatter254($phone)
    {
        $forced_numbber = substr($phone, -9);
        return '254' . $forced_numbber;
    }
    public function enroll($pid, $plan_id)
    {
        // $this->authorize('create', Payment::class,)
        $this->authorize('create', [Payment::class, $pid]);
        $project = Project::find($pid);
        $plan = Plan::find($plan_id);
        return view('payments.enroll', compact('plan', 'project'));
    }

    public function getCredentials()
    {
        $secret = config('app.TALKZURI_Mbesha_s');
        $key = config('app.TALKZURI_Mbesha_k');
        $crede = base64_encode($key . ':' . $secret);
        $url = config('app.url_for_oauth_generation');
        Log::info($secret . $key . $url );
        //get credentials from oauth
        // $url = 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Basic ' . $crede)); //setting a custom header
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $curl_response = curl_exec($curl);
        Log::info($curl_response);
        $tokenresult =  json_decode($curl_response, true);
        $access_token = $tokenresult['access_token'];
        Log::info($access_token);
        return $access_token;
    }

    public function lipanampesaonline($mobile, $enrollment_plan_id = null, $amount, $project_id)
    {
        //  dd(config('app.PESA_URL'));
        $timestamp = date('YmdHis');
        $project = Project::find($project_id);
        $passkey = "a5bbdcfaf53218ce55a1e364779c7117e1a0a46f14667ec30327be0bb1629380"; //'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919';
        // $passkey = config('app.passkey');
        $mobile = $this->e164Formatter254($mobile);
        $pass = base64_encode('4029323' . $passkey . $timestamp);
        $tzcode = 'TzReload' . $project->code;
        $url = config('app.process_stkpush_url');
        //$BusinessShortCode =  config('app.BusinessShortCode'); 'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header
        $curl_post_data = array(
            'BusinessShortCode' => '4029323',
            'Password' => $pass, //'Safcom145!', //the password used for encrypting the request sent: A base64 encoded string. (The base64 string is a combination of Short-code+Passkey+Timestamp)
            'Timestamp' =>  $timestamp,
            'TransactionType' => 'CustomerPayBillOnline',
            'Amount' => $amount,
            'PartyA' => $mobile, //use shortcode 1..initiator name and security credentials...174379
            'PartyB' => '4029323',  //u
            'PhoneNumber' =>  $mobile,
            'CallBackURL' => config('app.TALKZURI_Mpesa_callback_URL'), //  'https://sozuri.net/lipanampesa/handle',
            'AccountReference' => $tzcode,
            'TransactionDesc' => 'Talkzuri Recharge'
        );

        $data_string = json_encode($curl_post_data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        //array of result
        Log::info($curl_response);
        $result = json_decode($curl_response, true);
        //dd( $result );
        $err = curl_error($curl);

        if ($err) {
            return back()->withErrors('An error was encountered. Try again later');
            // die('Curl returned error: ' . $err);
        }
        if (!empty($result['errorCode'])) {
            if ($result['errorCode'] == "************") {
                //return "A current transaction is still being processed. Please check your mobile Phone Mpesa Menu to Confirm the transaction.";
                return  $result['errorMessage'];
            } elseif ($result['errorCode'] == "400.002.02") {
                return "You have entered an Invalid amount. Check if you have exceeded your personal Mpesa daily transaction and try again.";
            } else {
                return 'Oops, An error was encountered' . $result['errorMessage'];
            }
        }
        if ($result['ResponseCode'] === "0") {
            $payment = new payment();
            $payment->txRef =  $result['MerchantRequestID']; //unique internal ID
            // $payment->transaction_id =  $transaction_id;
            $payment->project_id =  $project_id;
            $payment->tx_customer_phone =  $mobile;
            $payment->tx_orderRef =   $result['CheckoutRequestID'];
            $payment->respcode =  $result['ResponseCode'];
            $payment->resp_respmsg =  $result['CustomerMessage'];
            $payment->amount  =  $amount;
            $payment->balance  = $project->account_type == "postpay" ? 0 : $amount;
            $payment->safaricom = $project->details ? explode(',', $project->details)[0] : $this->CalculateSafaricomCredits($payment->amount);
            $payment->airtel = $project->details ? explode(',', $project->details)[1] : $this->CalculateAirtelCredits($payment->amount);
            $payment->telkom = $project->details ? explode(',', $project->details)[2] : $this->CalculateTelkomCredits($payment->amount);
            $payment->international = $this->CalculateInternationalCredits($payment->amount);
            $payment->other = $this->CalculateOtherCredits($payment->amount);
            $payment->currency  =  'kes';
            $payment->tx_status = "new";
            $payment->status = "pend";
            $payment->credit = "units";
            $payment->debit = "cash";
            $payment->unit_cost = 0;
            $payment->detail  =  empty($enrollment_plan_id) ? 'recharge' : 'enrollment'; //enrollment

            $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'lipanampesa' . '%')->exists() ?
                Paymentmethod::where('name', 'like', 'lipanampesa' . '%')->value('id') :
                Paymentmethod::insertGetId(['name' => 'lipanampesa', 'detail' => 'lipanampesa', 'created_by' => Auth::id(),]);
            $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'new' . '%')->exists() ?
                Paymentstatus::where('name', 'like', 'new' . '%')->value('id') :
                Paymentstatus::insertGetId(['name' => 'new', 'detail' => 'new', 'created_by' => Auth::id(),]);
            $payment->save();

            //create enrollment if necessary
            if (!is_null($enrollment_plan_id) && $enrollment_plan_id > 0) {
                //if enrollment for this project exists, update it
                $enrollment = new Enrollment();
                $enrollment->user_id = Auth::id();
                $enrollment->project_id = $project_id;
                $enrollment->plan_id = $enrollment_plan_id;
                $enrollment->payment_id = $payment->id;
                $enrollment->start = Carbon::now();
                $enrollment->end = Carbon::now()->addDays(45); //take for 30days
                $enrollment->credits = Plan::find($enrollment_plan_id)->credits; //to award
                $enrollment->status = 'new';
                $enrollment->created_by = Auth::id();
                $enrollment->updated_by = Auth::id();
                $enrollment->isActive = false;
                $enrollment->save();

                DB::table('enrollment_payment')->insert([
                    'enrollment_id' => $enrollment->id,
                    'user_id' => Auth::id(),
                    'payment_id' => $payment->id,
                    'plan_id' => $enrollment_plan_id,
                    'amount' => $amount,
                    'project_id' => $project_id
                ]);
            }
            return "Success";
        }
    }

    public function startPay(Request $request, $id)
    {
        $project = Project::find($id);
        Log::info($request->all());
        if ($request->payby == "cards") {
            //make corresponding pyment and refer rti thr sae
            $this->initiatePay($request->email, $request->enrollment_plan_id, $request->phone, (int)$request->amount, $request->currency, $id);
        } elseif ($request->payby == "mpesa") {
            $payment_message = $this->lipanampesaonline($request->phone, $request->enrollment_plan_id, (int)$request->amount, $id);

            if ($payment_message != "Success") {
                return back()->withErrors('Oops ' . $payment_message);
            }
            if ($payment_message == "Success") {
                //$request->session()->flash('Mpesa transaction Initiated.Please check your mobile phone Mpesa Menu to confirm Transaction');
                return redirect('/projects/' . $id . '/edit')->with('status', 'Please check your mobile phone Mpesa Menu to confirm Transaction.
             Your balance will be automatically updated');
            }
        } elseif ($request->payby == "card") {
            $this->initiateFlutterPay($request->email, Auth::user()->name, $request->enrollment_plan_id, $request->phone, (int)$request->amount, $request->currency, $id);
        }
    }


    public function handlePay(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('incoming rave callback' . $x);
        $signature = $request->header('verif-hash') ?  $request->header('verif-hash') : '';
        $request = $request->all();
        $successfullpayment = $this->handleCallback($request,   $signature);
        if (!$successfullpayment) {
            //$request->session()->flash('message', 'You have Cancelled the Transaction.');
            return redirect('/projects')->withErrors('You have Cancelled the Transaction.');
        }
        //you only create an enrollment using a payment.
        $credits = Enrollment::where('payment_id', $successfullpayment->id)->exists() ? Enrollment::where('payment_id', $successfullpayment->id)->value('credits') :
            $this->CalculateCredits($successfullpayment->amount);
        //if success, obviously the db is updated, now offer value
        //update tx
        $successfullpayment->credits = $credits;
        $successfullpayment->credit = "units";
        $successfullpayment->debit = "cash";
        $successfullpayment->unit_cost = $successfullpayment->amount / $credits;
        $successfullpayment->package_id = null;
        $successfullpayment->date = Carbon::now();
        $successfullpayment->save();
        $project = Project::find($successfullpayment->project_id);
        $project->credits += $credits;
        $project->save();

        $manage_p_project = Project::where('name', 'primary_project')->first();
        $manage_p_project->credits -=  $credits;
        $manage_p_project->save();

        //$request->session()->flash('status', 'All good! Recharge for '.$successfullpayment->amount.' successful!');
        return redirect('/dashboard')->with('message', 'Payment successful. Your project credits have been updated.');
    }

    public function sozuripaybill()
    {
        $x = file_get_contents('php://input');
        Log::info($x);
        $x = file_get_contents('php://input');
        Log::info("paybill callback");
        $to_write = json_decode($x, true);
        $project = Project::where('name', 'like', $to_write['BillRefNumber'])->first();

        if ($project != null) {

            if (Payment::where('tx_id', $to_write['TransID'])->exists()) {
                Log::info('payment exists');
                return response()->json(['status' => 'failed', 'description' => 'Payment exists']);
                exit;
            }

            $payment = new Payment();
            $payment->project_id =  $project ? $project->id : null;

            $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'done' . '%')->exists() ?
                Paymentstatus::where('name', 'like', 'done' . '%')->value('id') :
                Paymentstatus::insertGetId(['name' => 'done', 'detail' => 'done']);
            $payment->name =  $to_write['BillRefNumber'];
            $payment->tx_id =  $to_write['TransID'];
            $payment->amount  = $to_write['TransAmount'];
            $payment->balance  = $project->account_type == "postpay" ? 0 : $to_write['TransAmount'];
            $payment->credits =  $to_write['TransAmount'];
            $payment->safaricom = $project->details ? explode(',', $project->details)[0] : $this->CalculateSafaricomCredits($payment->amount);
            $payment->airtel = $project->details ? explode(',', $project->details)[1] : $this->CalculateAirtelCredits($payment->amount);
            $payment->telkom = $project->details ? explode(',', $project->details)[2] : $this->CalculateTelkomCredits($payment->amount);
            $payment->international = $this->CalculateInternationalCredits($payment->amount);
            $payment->other = $this->CalculateOtherCredits($payment->amount);
            $payment->credit = "units";
            $payment->debit = "cash";
            $payment->status = "success";
            $payment->tx_status = "completed";
            $payment->unit_cost = 0;
            $payment->package_id = null;
            $payment->date = Carbon::now();
            $payment->updated_at = Carbon::now();
            $payment->body =  json_encode($to_write);
            $payment->save();
            $project = Project::find($payment->project_id);
            // $project->save();

            PostLedgers::dispatch($project->account_type == "postpay" ?  "receivable" : "advance payment", $project->name, "sales", "credit", $payment->amount, 16.00, "inclusive");
            PostLedgers::dispatch("cash", $project->name, "sales", "debit", $payment->amount, 16.00, "inclusive");
            Log::info("accounts dispatched for posting \n");

            return response()->json([
                'status' => 'Success from sozuri billing'
            ]);
        }
        return response()->json([
            'status' => 'Absent project from sozuri billing'
        ]);
    }

    public function handleFlutterwavePay(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('incoming flutterwave callback' . $x);
        $signature = $request->header('verif-hash') ?  $request->header('verif-hash') : '';
        $request = $request->all();
        Log::info($request);

        $body = $request;
        //resp  {"cancelled":true}
        //if (array_key_exists('cancelled', $body) && $body['cancelled'] == true) {
        //    return back();  //return a value that the main function can parse
        // }

        $status = $body['status'];
        $txRef =  $body['tx_ref'];
        //$transaction_id =  $body['transaction_id'] ;
        // Store the same signature on your server as an env variable and check against what was sent in the headers
        $local_signature = getenv('SECRET_HASH');
        // confirm the event's signature
        if ($signature !== $local_signature) {
            //  exit();
        }
        http_response_code(200); // PHP 5.4 or greater
        $payment = Payment::where('txRef', '=', $txRef)->where('tx_status', '!=', 'completed')->exists() ?
            Payment::where('txRef', '=', $txRef)->where('tx_status', '!=', 'completed')->first() : null;
        //if payment ref doesnt exist, skip
        if (!$payment) {
            return redirect('/dashboard')->withErrors('Your Sozuri payment could not be completed. Please try again.');
        }
        if ($status == 'successful' && $payment) {

            if (Enrollment::where('payment_id', $payment->id)->exists()) {
                $enrollment = Enrollment::where('payment_id', $payment->id)->first();
                $enrollment->status = 'completed';
                $enrollment->isActive = true;
                $enrollment->save();
            }
            //you only create an enrollment using a payment.
            $credits = $payment->amount;
            // $credits =  Enrollment::where('payment_id', $payment->id)->exists() ? Enrollment::where('payment_id', $payment->id)->value('credits') : $this->CalculateSafaricomCredits($payment->amount);
            //if success, the db is updated, now offer value
            $payment->amount = $payment->amount; //$credits;
            $payment->balance = $payment->amount; //$credits;
            $payment->credits = $payment->amount; //$credits;
            $payment->safaricom = $this->CalculateSafaricomCredits($payment->amount);
            $payment->airtel = $this->CalculateAirtelCredits($payment->amount);
            $payment->telkom = $this->CalculateTelkomCredits($payment->amount);
            $payment->international = $this->CalculateInternationalCredits($payment->amount);
            $payment->other = $this->CalculateOtherCredits($payment->amount);
            $payment->credit = "units";
            $payment->debit = "cash";
            $payment->status = "success";
            $payment->unit_cost = $payment->amount / $credits;
            $payment->package_id = null;
            $payment->date = Carbon::now();
            $payment->save();

            $project = Project::find($payment->project_id);
            $project->credits += ($credits > 1 ?  $credits : 0);
            $project->save();

            PostLedgers::dispatch($project->account_type == "postpay" ?  "receivable" : "advance payment", $project->name, "sales", "credit", $payment->amount, 16.00, "inclusive");
            PostLedgers::dispatch("cash", $project->name, "sales", "debit", $payment->amount, 16.00, "inclusive");
            Log::info("accounts dispatched for posting \n");

            //$manage_p_project = Project::where('name','primary_project')->first();
            //$manage_p_project->credits -= $credits > 1 ?  $credits : 0;
            //$manage_p_project->save();
            $to = [['email' => $project->user()->first()->email, 'name' => $project->user()->first()->name]];
            $bcc = [['email' => "<EMAIL>", 'name' => "New Successful Payment",]];
            Mail::to($to)->bcc($bcc)->send(new Paymentcompleted($payment));
            //$request->session()->flash('status', 'All good! Recharge for '.$successfullpayment->amount.' successful!');
            return redirect('/dashboard')->with('message', 'Payment successful. Your project credits have been updated.');
        } elseif ($status == 'cancelled' && $payment) {
            $payment->status = "cancelled";
            $payment->save();
            return redirect('/dashboard')->withErrors('Payment Failed. You have cancelled your transaction.');
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        $this->authorize('viewAny', [Payment::class, $id]);
        $project = Project::find($id);
        $payments = Payment::where('project_id', $id)->get();

        $projects_sms = DB::table('sms_copy')
        ->where('project_id', $id)
        ->whereYear('created_at', date('Y'))
        ->select(
            DB::raw("
            project_id,
            sum(price) as price,
            sum(message_part) as sms, 
            YEAR(created_at) year, 
            MONTH(created_at) month
            ")
        )
            ->groupBy('project_id', 'year', 'month')
            ->get();


        return view('payments.index', compact('project', 'payments', 'projects_sms'));
    }
 
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        //
        $this->authorize('create', [Payment::class, $id]);
        $project = Project::find($id);
        $payments = Payment::all();
        return view('payments.create', compact('project'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id)
    {
        $this->authorize('create', [Payment::class, $id]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Payment  $payment
     * @return \Illuminate\Http\Response
     */
    public function show(Payment $payment)
    {
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Payment  $payment
     * @return \Illuminate\Http\Response
     */
    public function edit(Payment $payment)
    {
        $this->authorize('update', $payment);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Payment  $payment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Payment $payment)
    {
        //
        $this->authorize('update', $payment);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Payment  $payment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Payment $payment)
    {
        //
        $this->authorize('delete', $payment);
    }
}
