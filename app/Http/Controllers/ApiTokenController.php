<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;
use App\Project;
use App\Collaboration;
use App\User;
use Illuminate\Support\Facades\Auth;
class ApiTokenController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
       // $this->authorize('viewAny', [ApiTokenController::class, $id]);

        $project = Project::find($id);

        $collabo_projects_ids = Collaboration::where('user_id', Auth::id())->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find(Auth::id())->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $project->id)->count()  ===  1) {
       }else{
        return back()->withErrors("Unauthorized");;
       }

        return view('projects.api', compact('project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        //
       // $token = $request->user()->api_token;
       $project = Project::find($id);
       $collabo_projects_ids = Collaboration::where('user_id', Auth::id())->where('isActive','=',1)->pluck('project_id')->toArray();
       $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
       $own_projects = User::find(Auth::id())->projects()->get();
       $projects = $collabo_projects->merge( $own_projects );
       //if both are empty
       if ( $projects->where('id', $project->id)->count()  ===  1) {
      }else{
       return back()->withErrors("Unauthorized");;
      }


        $token = $project->api_token;
        if(!$token) {
            return back()->withErrors("You have not yet generated an api key for '". $project->name  ."'. click 'Generate New API key' button first");
        }
        //$request->user()->forceFill(['api_token' => hash('sha256',$token)])->save();

        return back()->withGeneratedtoken('Your API key is : ' . $token .  '      .Anyone with this key can consume your Accounts API.');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
        $project = Project::find($id);

        $collabo_projects_ids = Collaboration::where('user_id', Auth::id())->where('isActive','=',1)->where('role','admin')->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find(Auth::id())->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );

        if ( $projects->where('id', $project->id)->count()  !==  1) {
        return back()->withErrors("Unauthorized");
       }

        $token = Str::random(60);
        //$request->user()->forceFill(['api_token' => hash('sha256',$token)])->save();
        //$request->user()->forceFill(['api_token' => $token])->save();
        Project::find($id)->forceFill(['api_token' => $token])->save();

        return back()->withGeneratedtoken('Credentials updated! Your new API key is : ' . $token . '  Keep your credentials safe');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
