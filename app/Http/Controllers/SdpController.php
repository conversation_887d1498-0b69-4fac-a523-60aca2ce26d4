<?php

namespace App\Http\Controllers;

use App\Services\AutomationService;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use App\Subscriber;
use App\Premium;
use App\Premiumsms;
use App\Linknotice;
use App\Jobs\ProcessSendpremium;
use App\Traits\Atsendsms;
use App\Sms;
use Illuminate\Support\Carbon;
use App\Jobs\ProcessSendbulk;
use App\Project;
use App\Jobs\ProcessSdp;
use App\Shortcode;
use Illuminate\Support\Facades\DB;
use App\Jobs\ProcessSendsafaricombulk;
use App\Jobs\ProcessSendairtelbulk;
use App\Jobs\ProcessSendtelkombulk;
use App\Jobs\ProcessAirtelBulkDlr;
use App\Payment;
use Exception;
use Throwable;
use App\Contact;
use App\ContactList;
use App\Models\Automation;

class SdpController extends Controller
{
    private function getToken()
    {
        $base_uri = config('app.sdp_base_uri');
        $sdp_username = config('app.sdp_username');
        $sdp_password = config('app.sdp_password');
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'auth/login', [
            'headers' => [
                'Content-Type'     => 'application/json',
                'Accept'     => 'application/json',
                'X-Requested-With'     => 'XMLHttpRequest',
            ],
            'json' => ['username' => $sdp_username, 'password' =>  $sdp_password]
        ]);
        $code = $response->getStatusCode();
        $reason = $response->getReasonPhrase();
        $body = $response->getBody();
        Log::info($body);
        $stringBody = (string) $body;
        Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('stringbody' . $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $token =  $data['token'];
        $msg =  $data['msg'];
        $refreshToken =  $data['refreshToken'];
        return $token;
    }
    public function bulkdlr(Request $request)
    {
        //
    }
    //offercode, msisdn,
    public function subscribe(Request $request)
    {
        if ($request->filled('project_id')) {
            $this->authorize('create', [Premiumsms::class, $request->project_id]);
        }
        $project = $request->filled('project_id') ? Project::find($request->project_id) :
            Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        $request->validate(['number' => 'required', 'network' => 'required', 'type' => 'required', 'project_id' => 'nullable', 'keyword' => 'required']);
        $cleanNumber = function ($phone) {
            $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
            return $clean_number;
        };
        $e164Formatter254 = function ($phone) {
            $forced_numbber = substr($phone, -9);
            return '254' . $forced_numbber;
        };
        $verifyNumber = function ($phone) {
            return preg_match("/^[+]?[0-9]{7,13}$/", $phone) ? 'valid' : 'invalid';
        };

        $premium = $project ? (Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('status', 'active')->exists() ?
            Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('status', 'active')->first() : null) : null;

        if ($premium === null || $project === null) {
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                return response()->json(['status' => 'error', 'description' => 'Unknown, Expired, or Inactive Premium Service']);
            } else {
                return back()->withErrors('Unknown, Expired, or Inactive Premium Service');
            }
        }
        $campaign_id = $request->has('campaign_id') ? $request->campaign_id : null;
        $number = $request->number;
        $clean_number = array_unique(array_filter(array_map($cleanNumber, [$number])));
        $formatted_number = array_map($e164Formatter254, $clean_number);
        $num = $formatted_number[0];

        $status_code = $this->subscribestatusGenerator($num, $project->id, $premium->id);

        $tz_id = strtoupper(uniqid('subscribe') . time());
        $status =  $status_code['status'];
        $description = $status_code['description'];

        $OfferCode = $premium->offer_code;
        $msisdn = $num; //25478998xxx
        $Language = "1";
        $CpId = $premium->cp_id;
        $rate = $premium->rate;
        $type = $premium->type;
        $project_id = $project->id;
        $created_at = Carbon::now();
        $send_at =  $request->send_at;
        $campaignId = $campaign_id;

        if ($status !== "accepted") { // our response before shooting to sdp
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                return response()->json(['status' =>  $status, 'description' => $description]);
            } else {
                return back()->withErrors($description);
            }
        } else {
            // $subscriber = Subscriber::create(['msisdn' => $msisdn, 'created_at' => Carbon::now(), 'project_id' => $project->id, 'premium_id' => $premium->id, 'status' => 'inactive', 'type' => $type]);
            DB::table('subscribers')->insertOrIgnore(['msisdn' => $msisdn, 'created_at' => Carbon::now(), 'project_id' => $project->id, 'premium_id' => $premium->id, 'status' => 'inactive', 'type' => $type]);

            $subscriber = Subscriber::where(['msisdn' => $msisdn, 'project_id' => $project->id, 'premium_id' => $premium->id, 'type' => $type]);
            //  $raw_subscribe_request = [$tz_id, $number, $status, $description, $OfferCode, $msisdn, $Language, $CpId, $rate, $type, $project_id, $created_at, $send_at, $campaignId];
            //  ProcessSdp::dispatch($raw_subscribe_request)->delay(Carbon::parse($request->send_at));
            $timestamp = time();
            $uniqueid = uniqid('SOZSUB');
            $token = $this->gettoken();
            $base_uri = 'https://dsvc.safaricom.com:9480/api/'; //config('app.at_base_uri'); //'http://**************' 
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', 'public/SDP/activate', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Authorization' => 'Bearer ' . $token,
                    'SourceAddress' => '************'
                ],
                'json' => [
                    "requestId" => $uniqueid,
                    "requestTimeStamp" => date('Ymdhi'), //date('Y-m-d H:i:s'),
                    "channel" => 'APIGW',
                    "operation" => 'ACTIVATE', //SUBSCRIPTION',
                    "requestParam" => [
                        "data" => [
                            ["name" => "OfferCode", "value" => $premium->offer_code], //  $OfferCode      '001022429302'  F/OR SUBSCRIPTION  001029900700 FOR ONDEMAND
                            ["name" => "Msisdn", "value" => $msisdn], // $msisdn
                            ["name" => "Language", "value" =>  $Language],  // $Language
                            ["name" => "CpId", "value" => '224'], // $CpId
                        ]
                    ]
                ]
            ]);
            $code = $response->getStatusCode(); // 200
            $reason = $response->getReasonPhrase(); // OK
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string
            Log::info('subscribe --code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('stringbody for subscribe request' . $stringBody);
            $data = json_decode($stringBody, true);
            //Log::info($data);
            //$subscriber = Subscriber::where(['msisdn' => $msisdn, 'project_id' => $project_id, 'premium_id' => $premium->id])->first();
            $subscriber->last_message = $uniqueid; //Unique client TransactionID used for tracking
            $subscriber->activate_body = $stringBody;
            $subscriber->provider_status =  $data['responseParam']['status'];
            $subscriber->provider_statusCode = $data['responseParam']['statusCode'];
            $subscriber->provider_description =  $data['responseParam']['description'];
            $subscriber->save;
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                //return response()->json(["status" => $status, "description" => $description]); //accepted or fail
                return response()->json(['project' =>  $project->name, 'shortcode' => $premium->shortcode, 'keyword' => $premium->activation_keywords,  'number' => $msisdn, 'network' =>  'safaricom', 'type' => 'subscribe', 'status' =>  $subscriber->provider_status]);
            } else {
                return back()->with('status', $description);
            }
        }
    }

    public function unsubscribe(Request $request)
    {
        if ($request->filled('project_id')) {
            $this->authorize('create', [Premiumsms::class, $request->project_id]);
        }
        $project = $request->filled('project_id') ? Project::find($request->project_id) :
            Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        $request->validate(['number' => 'required', 'project_id' => 'nullable', 'keyword' => 'required']);
        $cleanNumber = function ($phone) {
            $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
            return $clean_number;
        };
        $e164Formatter254 = function ($phone) {
            $forced_numbber = substr($phone, -9);
            return '254' . $forced_numbber;
        };
        $number = $request->number;
        $premium = Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('offer_type', 'subscription')->exists() ?
            Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('offer_type', 'subscription')->first() : null;
        $premium = Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('status', 'active')->first();
        if ($premium === null) {
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                return response()->json(['status' => 'error', 'description' => 'Unknown, Expired, or Inactive Premium Service']);
            } else {
                return back()->withErrors('Unknown, Expired, or Inactive Premium Service');
            }
        }
        $clean_number = array_unique(array_filter(array_map($cleanNumber, [$number])));
        $formatted_number = array_map($e164Formatter254, $clean_number);
        $num = $formatted_number[0];
        $status_code = $this->unsubscribestatusGenerator($num, $project->id, $premium->id);
        $subscriber = Subscriber::where(['msisdn' => $number, 'project_id' => $project->id, 'premium_id' => $premium->id]);
        $status =  $status_code['status']; //
        $description = $status_code['description']; //
        $OfferCode = $premium->offer_code; //
        $msisdn = $formatted_number; //25478998xxx
        $CpId = $premium->cp_id; //
        if ($status !== "accepted") {
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                return response()->json(['status' =>  $status, 'description' => $description]);
            } else {
                return back()->withErrors($description);
            }
        } else {
            $subscriber = Subscriber::where(['msisdn' => $msisdn, 'project_id' => $project->id, 'premium_id' => $premium->id])->update(['status' => 'inactive']);
            $timestamp = time();
            $uniqueid = strtoupper(uniqid('unsubscribe') . time());
            $token = $this->gettoken();
            $base_uri = 'https://dsvc.safaricom.com:9480/api/'; //config('app.at_base_uri'); //'http://**************'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', 'public/SDP/deactivate', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Authorization' => 'Bearer ' . $token,
                    //'X-Requested-With' => 'XMLHttpRequest'
                    'SourceAddress' => '************'
                ],
                'json' => [
                    "requestId" => $uniqueid,
                    "channel" => 'SMS',
                    "operation" => 'UNSUBSCRIPTION',
                    "requestParam" => [
                        "data" => [
                            ["name" => "OfferCode", "value" => $OfferCode], //001029900701  F/OR SUBSCRIPTION  001029900700 FOR ONDEMAND
                            ["name" => "Msisdn", "value" =>  $msisdn],
                            // ["name" => "Language","value" =>  $this->raw_subscribe_request['Language'] ],
                            ["name" => "CpId", "value" => $CpId],
                        ]
                    ]
                ]
            ]);
            $code = $response->getStatusCode(); // 200
            $reason = $response->getReasonPhrase(); // OK
            $body = $response->getBody();
            //echo $body;  // Implicitly cast the body to a string and echo it
            $stringBody = (string) $body; // Explicitly cast the body to a string
            Log::info('unsubscribe --code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('stringbody' . $stringBody);
            $data = json_decode($stringBody, true);
            Log::info($data);
            //$subscriber = Subscriber::where(['msisdn' => $msisdn, 'project_id' => $project->id, 'premium_id' => $premium->id])->first();
            $subscriber->last_message = $uniqueid; //Unique client TransactionID used for tracking
            $subscriber->deactivate_body = $stringBody;
            $subscriber->provider_status =  $data['responseParam']['status'];
            $subscriber->provider_statusCode = $data['responseParam']['statusCode'];
            $subscriber->provider_description =  $data['responseParam']['description'];
            $subscriber->status =  $data['responseParam']['status'] == "0" ? 'inactive' : 'active'; //
            $subscriber->updated_at = Carbon::now();
            $subscriber->save();

            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                //return response()->json(["status" => $status, "description" => $description]); //accepted or fail
                //return response()->json(['keyword' => $premium->activation_keywords,  'project' =>  $project->name, 'number' => $msisdn, 'network' =>  'safaricom', 'type' => 'unsubscribe', 'status' =>  $subscriber->provider_status]); //r acceptedo
                return response()->json(['project' =>  $project->name, 'shortcode' => $premium->shortcode, 'keyword' => $premium->activation_keywords,  'number' => $msisdn, 'network' =>  'safaricom', 'type' => 'subscribe', 'status' =>  $subscriber->provider_status]);
            } else {
                return back()->with('status', $description);
            }
        }
    }
    public function consent(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('consent: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);
        $names = array_column($data['requestParam']['data'], 'name');
        $values = array_column($data['requestParam']['data'], 'value');
        $array = array_combine($names, $values);
        $additional_names = array_column($data['requestParam']['additionalData'], 'name');
        $additional_values = array_column($data['requestParam']['additionalData'], 'value');
        $additional_array = array_combine($additional_names, $additional_values);
        $requestId = $data['requestId'];
        $requestTimeStamp = $data['requestTimeStamp'];
        $operation = $data['operation'];
        $OfferCode = $array['OfferCode'];
        $Language = $array['Language'];
        $cpId = $array['CpId'];
        $Type = $array['Type']; //"CONSENT"
        $Msisdn = $array['Msisdn'];
        $ConsentValue = $additional_array['ConsentValue']; //"User Cancelled"
        //original subscriber
        $pos = strpos($ConsentValue, 'Cancelled');
        $premium = Premium::where([ /*'cp_id' => $cpId,*/'offer_code' => $OfferCode])->exists() ? Premium::where([ /*'cp_id' => $cpId,*/'offer_code' => $OfferCode])->first() : null;

        $subscriber = $premium ? Subscriber::where(['msisdn' => $Msisdn, 'premium_id' => $premium->id])->exists() : null;
        $callback = $premium ?  $premium->callback : null;
        $subscriber =  Subscriber::where(['msisdn' => $Msisdn, 'premium_id' => $premium->id])->exists() ?
            Subscriber::where(['msisdn' => $Msisdn, 'premium_id' => $premium->id])->first() :
            Subscriber::create(['msisdn' =>  $Msisdn, 'project_id' => $premium->project_id, 'premium_id' => $premium->id, 'cp_id' => $premium->cp_id, 'cp_name' => $premium->cp_name, 'status' => 'active']);
        $subscriber->status =  $pos === false ? 'active' : 'inactive'; //$pos === false means that was not found so ITS acceptedful
        $subscriber->last_message =  $requestId;
        $subscriber->provider_description =  $ConsentValue;
        $subscriber->save();
        // $shortcode = $sms->from;
        if ($callback !== null  && $premium !== null) {
            $base_uri = $callback; //config('app.at_base_uri'); //'http://**************'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', '/', [
                'headers' => [
                    'Content-Type'     => 'application/json',
                    'Accept'     => 'application/json',
                ],
                'json' => ['keyword' =>  $premium->activation_keywords, 'project' => $premium->project->name, 'number' => $Msisdn, 'network' =>  'safaricom', 'type' => 'consent', 'status' =>  $subscriber->status] //active'or'inactive
            ]);
            $code = $response->getStatusCode();
            $reason = $response->getReasonPhrase();
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string
            Log::info('stringbody from customer consent. code:' . $code . ' reason:' . $reason  . $stringBody);
            $data = json_decode($stringBody, true);
            Log::info($data);
        }

        return response()->json([
            'status' => 'Success',
            "requestId" => $requestId,
            "responseId" => uniqid('SOZCONSENTDLR') . time(),
            "responseTimeStamp" => date('Ymdhi'),
            "operation" => "CP_NOTIFICATION",
            "responseParam" => [
                "status" => "0",
                "statusCode" => "0000",
                "description" => "Success"
            ]
        ]);
    }
    public function linkNotify(Request $request)
    {

        $x = file_get_contents('php://input');
        Log::info('linknotify: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);
        $names = array_column($data['requestParam']['data'], 'name');
        $values = array_column($data['requestParam']['data'], 'value');
        $array = array_combine($names, $values);

        $request_id = $data['requestId'];
        $request_time_stamp = $data['requestTimeStamp'];
        $operation = $data['operation'];
        $link_id = $array['LinkId'];
        $offer_code = $array['OfferCode'];

        $refernce_id = $array['RefernceId'];
        $client_transaction_id = $array['ClientTransactionId'];
        $language = $array['Language'];
        $channel = $array['Channel'];
        $type = $array['Type'];
        $user_data = $array['USER_DATA'];
        $msisdn = $array['Msisdn'];

        $premium_service = Premium::where('offer_code', $offer_code)->exists() ? Premium::where('offer_code', $offer_code)->first() : null;
        if ($premium_service === null) {
            return response()->json([
                'status' => 'Success',
                "requestId" => $request_id,
                "responseId" => uniqid('SOZNOTIFYLINK') . time(),
                "responseTimeStamp" => date('Ymdhi'),
                "operation" => "CP_NOTIFICATION",
                "responseParam" => [
                    "status" => 'error',
                    "statusCode" => 1,
                    "description" => 'Offer not found'
                ]
            ]);
        }
        $project_id = $premium_service->project_id;
        $cp_id = $premium_service->cp_id;
        //$shortcode = Shortcode::where('id', $premium_service->shorcode_id)->first();
        // $subscriber = Subscriber::where('offer_code', $offer_code)->first();

        $linknotice = Linknotice::where('link_id', $link_id)->exists() ? Linknotice::where('link_id', $link_id)->first() : new Linknotice();
        $linknotice->name =   $link_id;
        $linknotice->project_id =   $project_id;
        $linknotice->premiumsms_id = null;
        $linknotice->replied = false;
        $linknotice->operation =   $operation;
        $linknotice->request_id = $request_id;
        $linknotice->request_time_stamp  = $request_time_stamp;
        $linknotice->link_id = $link_id;
        $linknotice->cp_id = $cp_id;
        $linknotice->cp_name = '';
        $linknotice->offer_code =  $offer_code;
        $linknotice->refernce_id = $refernce_id;
        $linknotice->client_transaction_id =  $client_transaction_id;
        $linknotice->language =    $language;
        $linknotice->channel =    $channel;
        $linknotice->type =     $type;
        $linknotice->user_data =    $user_data;
        $linknotice->msisdn =    $msisdn;
        //$linknotice->rate =    $premium_service->rate;
        $linknotice->created_at =    Carbon::now();
        //$linknotice->send_at =$this->raw_sms['send_at'];
        $linknotice->sent_at = Carbon::parse(strtotime($request_time_stamp));
        $linknotice->tz_id =  uniqid('link') . time();
        $linknotice->link_notify_cp_notification_from_sdp_body = (string) $x;
        $linknotice->save();
        $project_name = Project::where('id', $project_id)->first()->name;
        $premium_service_callback = $premium_service->callback;

        Log::info('start link callback' . $premium_service_callback);
        $callback_array = explode(',', $premium_service_callback);
        foreach ($callback_array as $webhook) {
            if ($webhook != null) {
                $base_uri = $webhook; //config('app.at_base_uri'); //'http://**************'
                $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
                $response = $client->request('POST', '', [
                    'headers' => [
                        'Content-Type'     => 'application/json',
                        'Accept'     => 'application/json',
                    ],
                    'json' => ['project' => $project_name, 'shortcode' => $premium_service->shortcode, 'keyword' =>  $premium_service->activation_keywords, 'number' => $msisdn, 'messageId' => $link_id, 'network' => 'safaricom', 'type' => 'linkNotification', 'linkId' => $link_id, 'status' =>  $user_data, "timestamp" => time()]

                    //'json' => ['keyword' =>  $premium_service->activation_keywords, 'project' => $project_name, 'number' => $msisdn, 'network' => 'safaricom', 'type' => 'linkNotification', 'message' =>  $user_data, 'linkId' => $link_id] //'date' => Carbon::parse(strtotime($request_time_stamp)
                ]);
                $code = $response->getStatusCode();
                $reason = $response->getReasonPhrase();
                $body = $response->getBody();
                $stringBody = (string) $body; // Explicitly cast the body to a string 
                // $linknotice->link_notify_response_from_customer_body = $stringBody;
                // $linknotice->save();
                Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
                Log::info('link notice to customer' . $stringBody);
                $data = json_decode($stringBody, true);
                Log::info($data);
                Log::info('end link callback to ' . $webhook);
            }
        }
        //consult the callback of the premium ondemand
        $response_id = uniqid('SOZNOTIFYLINK') . time();
        $response_time_tamp =  date('Ymdhi');
        $response_status =  "0";
        $response_status_code = "0000";
        $response_description =  "Accepted";

        //update response to sdp
        /* $linknotice->response_id = $response_id;
        $linknotice->response_time_stamp = $response_time_tamp;
        $linknotice->response_status = $response_status;
        $linknotice->response_status_code = $response_status_code;
        $linknotice->response_description = $response_description;
        $linknotice->save();
        */
        return response()->json([
            "requestId" => $request_id,
            "responseId" => $response_id,
            "responseTimeStamp" => $response_time_tamp,
            "operation" => "CP_NOTIFICATION",
            "responseParam" => [
                "status" => $response_status,
                "statusCode" => $response_status_code,
                "description" => $response_description
            ]
        ]);
    }
    public function sendpremium(Request $request)
    {
        Log::info($request->all());
        $project = $request->filled('project_id') ? Project::find($request->project_id) :
            Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();

        if ($request->filled('project_id')) { //authorize i from UI
            $this->authorize('create', [Premiumsms::class, $project->id]);
        }
        $request->validate(['number' => 'required', 'project_id' => 'nullable', 'premium_id' => 'nullable', 'message' => 'required']);
        $cleanNumber = function ($phone) {
            $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
            return $clean_number;
        };
        $e164Formatter254 = function ($phone) {
            $forced_numbber = substr($phone, -9);
            return '254' . $forced_numbber;
        };
        $estimated_chars = strlen($request->message); //message parts
        $estimated_sms = ceil($estimated_chars  / 160); //constant for sms
        $campaign_id = $request->has('campaignId') ? $request->campaignId : null;
        $number = $request->number;
        $clean_number = array_unique(array_filter(array_map($cleanNumber, [$number])));
        $formatted_number = array_map($e164Formatter254, $clean_number);
        $premium = Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('status', 'active')->exists() ?
            Premium::where('activation_keywords', $request->keyword)->where('project_id', $project->id)->where('status', 'active')->first() : null;
        $num = $formatted_number[0];

        if ($premium === null) {
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                return response()->json(['status' =>  'error', 'description' => 'Unknown or Inactive Premium service']); //to customer api
            } else {
                return back()->withErrors('Unknown Premium Service'); //to user ui
            }
        } elseif ($premium->offer_type === 'subscription') {
            //find if subscriber exists
            $link_id = null;
        } elseif ($premium->offer_type === 'ondemand') { //get this froom request
            //find if link id exists, else return back
            $link_id = $request->linkId;

            if (DB::table('linknotices')->where(['link_id' => $link_id, 'project_id' => $project->id, 'offer_code' =>  $premium->offer_code])->exists()) {
                DB::table('linknotices')->where(['link_id' => $link_id, 'project_id' => $project->id, 'offer_code' =>  $premium->offer_code])
                    ->update(['replied' => true]);
                //return response()->json(['status' =>  $status, 'description' => $description]); //to customer api
            } else {
                if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                    return response()->json(['status' =>  'error', 'description' => 'wrong link id']); //to customer api
                } else {
                    return back()->withErrors('Unknown link id'); //to user ui
                }
            }
        }
        $status_code = $this->sendPremiumStatusGenerator($num, $project->id, $premium->id);

        $tz_id = strtoupper(uniqid('subscribe') . time());
        $status =  $status_code['status']; //
        $description = $status_code['description']; //
        $offer_code = $premium->offer_code; //
        $msisdn = $num; //25478998xxx
        $language = "1"; //
        $message = $request->message;
        $cp_id = $premium->cp_id; //
        $rate = $premium->rate; //
        $price = $premium->rate *  $estimated_sms; //
        $type = $premium->offer_type; //
        $project_id = $project->id;
        $created_at = Carbon::now();
        $send_at =  $request->send_at;
        $campaign_id = $campaign_id;
        $message_parts = $estimated_sms;

        if ($status !== "accepted") {
            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                return response()->json(['status' =>  $status, 'description' => $description]); //to customer api
            } else {
                return back()->withErrors($description); //to user ui
            }
        } else {
            $raw_sms = [
                'tz_id' => $tz_id, 'link_id' => $link_id, 'from' => $premium->name, 'msisdn' => $msisdn, 'status' => $status, 'description' => $description, 'message' => $message,
                'offer_code' => $offer_code, 'language' => $language,  'cp_id' => $cp_id, 'rate' => $rate, 'price' => $price, 'type' => $type, 'project_id' => $project_id,
                'send_at' => $send_at, 'campaign_id' => $campaign_id, 'created_at' => $created_at, 'message_parts' => $message_parts
            ];
            // ProcessSendpremium::dispatch($raw_sms)->delay(Carbon::parse($request->send_at));

            $timestamp = time();
            $uniqueid = uniqid('SOSENDPRE');
            $token = $this->gettoken();
            $base_uri = 'https://dsvc.safaricom.com:9480/api/'; //config('app.at_base_uri'); //'http://**************'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', 'public/SDP/sendSMSRequest', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Authorization' => 'Bearer ' . $token,
                    'SourceAddress' => '************'
                ],
                'json' => [
                    "requestId" => $uniqueid,
                    "channel" => 'APIGW',
                    "operation" => 'SendSMS',
                    "requestParam" => [
                        "data" => [
                            ["name" => "LinkId", "value" => $raw_sms['link_id']], //null fro subscription
                            ["name" => "Msisdn", "value" => $raw_sms['msisdn']],
                            ["name" => "Content", "value" =>  $raw_sms['message']],
                            ["name" => "OfferCode", "value" => $raw_sms['offer_code']], //001029900701  F/OR SUBSCRIPTION  001029900700 FOR ONDEMAND
                            ["name" => "CpId", "value" => $raw_sms['cp_id']],
                        ]
                    ]
                ]
            ]);
            $code = $response->getStatusCode(); // 200
            $reason = $response->getReasonPhrase(); // OK
            $body = $response->getBody();
            //echo $body;  // Implicitly cast the body to a string and echo it
            $stringBody = (string) $body; // Explicitly cast the body to a string
            Log::info('sendpremium --code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('stringbody' . $stringBody);
            $data = json_decode($stringBody, true);
            Log::info($data);
            $premiumsms = new Premiumsms();
            $premiumsms->cost = $raw_sms['rate'];
            $premiumsms->message_id = $tz_id;
            // $premiumsms->status = $data['responseParam']['status'];
            // $premiumsms->status_code = $data['responseParam']['statusCode'];
            $premiumsms->message_parts  = $raw_sms['message_parts'];
            $premiumsms->msisdn =  $raw_sms['msisdn'];
            //$premiumsms->provider_status =  $data['responseParam']['status'];
            //$premiumsms->provider_statusCode = $data['responseParam']['statusCode'];
            //$premiumsms->desc =  $data['responseParam']['description'];
            $premiumsms->project_id =   $raw_sms['project_id'];
            $premiumsms->premium_id =  $premium->id;
            $premiumsms->message =    $raw_sms['message'];
            $premiumsms->offer_code =    $raw_sms['offer_code'];
            $premiumsms->link_id =    $raw_sms['link_id'];
            $premiumsms->reply =  $raw_sms['link_id'] ? 1 : 0;

            $premiumsms->cp_id =    $raw_sms['cp_id'];
            $premiumsms->rate =    $raw_sms['rate'];
            $premiumsms->type =    $raw_sms['type'];
            $premiumsms->campaign_id =    $raw_sms['campaign_id'];
            $premiumsms->from =   $raw_sms['from'];
            $premiumsms->price =   $raw_sms['price'];
            $premiumsms->detail =  $raw_sms['message'];
            $premiumsms->created_at =    $raw_sms['created_at'];
            $premiumsms->send_at = $raw_sms['send_at'];
            $premiumsms->sent_at = Carbon::now();
            $premiumsms->tz_id =   $uniqueid;
            // $premiumsms->response_body =  $stringBody;
            $premiumsms->save();

            if (!empty($request->apiKey) || !empty($request->bearerToken())) {
                // echo json_encode(["messageData"  => ["messages" => $sms_count], "recipients" => $short_smses_response]); //echo is marginally faster than print
                return '{"messageData":{"messages":1},"recipients":[{"messageId":$tz_id,"to":' . $premiumsms->msisdn . ',"status":"accepted","statusCode":"11","messagePart":1,"type":' . $type . '}]}';
            } else {
                return back()->with('status', $status . ' ' . $description);
            }
        }
    }
    public function premiumdlr(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('premiumdlr: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);
        $names = array_column($data['requestParam']['data'], 'name');
        $values = array_column($data['requestParam']['data'], 'value');
        $array = array_combine($names, $values);
        $requestId = $data['requestId'];
        $requestTimeStamp = $data['requestTimeStamp'];
        $operation = $data['operation'];
        $Refund = $array['Refund'];
        $Type = $array['Type']; //NOTIFY_LINKID
        $Description = $array['Description'];
        $ClientTransactionId = $array['ClientTransactionId']; //tz id and message_id
        $Msisdn = $array['Msisdn'];
        //update the premium dlr message status
        $premiumsms = Premiumsms::where('tz_id', $ClientTransactionId)->exists() ?  Premiumsms::where('tz_id', $ClientTransactionId)->first() : null;
        $premiumsms !== null ? $premiumsms->update(['status' => $Description, 'description' => $Description, 'status_code' => $Description, 'detail' => $Type, 'cost' => $Refund]) : '';
        $premium = Premium::where('id', $premiumsms->premium_id)->first();
        $project = Project::find($premium->project_id)->first();
        //webhook
        if ($premium->callback !== null) {
            $base_uri = $premium->callback; //config('app.at_base_uri'); //'http://**************'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', '/', [
                'headers' => ['Content-Type' => 'application/json', 'Accept' => 'application/json',],
                'json' => ['project' => $project->name, 'shortcode' => $premium->shortcode, 'keyword' =>  $premium->activation_keywords, 'number' => $Msisdn, 'messageId' => $premiumsms->tz_id, 'network' => 'safaricom', 'type' => 'premiumDelivery', 'status' =>  $Description, "timestamp" => time()] //'date' => Carbon::parse(strtotime($request_time_stamp)
            ]);
            $code = $response->getStatusCode();
            $reason = $response->getReasonPhrase();
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string

            Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('premiumdlr notice to customer' . $stringBody);
            $data = json_decode($stringBody, true);
            Log::info($data);
        }
        return response()->json([
            'status' => 'Success',
            "requestId" => $requestId,
            "responseId" => uniqid('SOZPREMIUMBLKDLR') . time(),
            "responseTimeStamp" => date('Ymdhi'),
            "operation" => "CP_NOTIFICATION",
            "responseParam" => [
                "status" => "0",
                "statusCode" => "0000",
                "description" => "Success"
            ]
        ]);
    }
    /**
     * This is sent when a subscriber self-activates
     * to a subscription product
     */
    public function activate(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('activation from customer: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);

        $names = array_column($data['requestParam']['data'], 'name');
        $values = array_column($data['requestParam']['data'], 'value');
        $array = array_combine($names, $values);

        $requestId = $data['requestId'];
        $requestTimeStamp = $data['requestTimeStamp'];
        $operation = $data['operation'];
        $OfferCode = $array['OfferCode']; //
        $TransactionId = $array['TransactionId']; //12
        $Language = $array['Language'];
        $SubscriberLifeCycle = $array['SubscriberLifeCycle']; //Unsubscription
        $SubscriptionStatus = $array['SubscriptionStatus']; //d
        $Channel = $array['Channel']; //10
        $Reason = $array['Reason']; //USER_INIT_REQUEST"
        $Type = $array['Type']; //ACTIVATION
        $OfferName = $array['OfferName']; //9200_Inspiration_ksh1_perday
        $Msisdn = $array['Msisdn']; //10

        $premium = Premium::where('offer_code',  $OfferCode)->first();
        $callback = $premium->callback;
        $project_name = Project::find($premium->project_id->first()->name);
        if ($Type == "ACTIVATION") {
            $subscriber = Subscriber::where(['msisdn' => $Msisdn, 'premium_id' => $premium->id])->exists() ?
                Subscriber::where(['msisdn' => $Msisdn, 'premium_id' => $premium->id])->first() :
                Subscriber::create(['msisdn' =>  $Msisdn, 'project_id' => $premium->project_id, 'premium_id' => $premium->id, 'cp_id' => $premium->cp_id, 'cp_name' => $premium->cp_name, 'status' => 'active']);
            //$subscriber->deactivate_body = $x;
            $subscriber->provider_status =  'active';
            $subscriber->status =  'active';
            // $subscriber->provider_statusCode = $data['responseParam']['statusCode'];
            $subscriber->provider_description =  $Reason;
            $subscriber->save();
        }
        if ($callback != null) {
            $base_uri = $callback; //config('app.at_base_uri'); //'http://**************'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', '/', [
                'headers' => [
                    'Content-Type'     => 'application/json',
                    'Accept'     => 'application/json',
                ],
                'json' => [
                    'project' =>  $project_name, 'shortcode' => $premium->shortcode, 'keyword' => $premium->activation_keywords,
                    'number' => $Msisdn, 'network' =>  'safaricom', 'type' => 'activation', 'status' =>  $subscriber->provider_status
                ]

                //'json' => ['keyword' => $premium->activation_keywords, 'project' =>  $premium->project_id, 'number' => $Msisdn,  'network' =>  'safaricom', 'type' => 'activation', 'status' => 'activated']
            ]);
            $code = $response->getStatusCode();
            $reason = $response->getReasonPhrase();
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string

            Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('stringbody from customer activation notice' . $stringBody);
            $data = json_decode($stringBody, true);
            Log::info($data);
        }
        Log::info('acknowledged');
        return response()->json([
            'status' => 'Success',
            "requestId" => $requestId,
            "responseId" => uniqid('SOZACTIVATIONDLR') . time(),
            "responseTimeStamp" => date('Ymdhi'),
            "operation" => "CP_NOTIFICATION",
            "responseParam" => [
                "status" => "0",
                "statusCode" => "0000",
                "description" => "Success"
            ]
        ]);
    }
    /**
     * sent when a subscriber self-deactivates from
     * a subscription product
     */
    public function deactivate(Request $request)
    {
        /*offercode and type will be ....DEACTIVATION*/
        $x = file_get_contents('php://input');
        Log::info('deactivation from customer: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);

        $names = array_column($data['requestParam']['data'], 'name');
        $values = array_column($data['requestParam']['data'], 'value');
        $array = array_combine($names, $values);

        $requestId = $data['requestId'];
        $requestTimeStamp = $data['requestTimeStamp'];
        $operation = $data['operation'];
        $OfferCode = $array['OfferCode']; //
        $TransactionId = $array['TransactionId']; //12
        $Language = $array['Language'];
        $SubscriberLifeCycle = $array['SubscriberLifeCycle']; //Unsubscription
        $SubscriptionStatus = $array['SubscriptionStatus']; //d
        $Channel = $array['Channel']; //10
        $Reason = $array['Reason']; //USER_INIT_REQUEST"
        $Type = $array['Type']; //DEACTIVATION
        $OfferName = $array['OfferName']; //9200_Inspiration_ksh1_perday
        $Msisdn = $array['Msisdn']; //10

        $premium = Premium::where('offer_code',  $OfferCode)->first();
        $callback = $premium->callback;
        if ($Type == "DEACTIVATION") {
            //original bulk sms
            $subscriber = Subscriber::where(['msisdn' => $Msisdn, 'premium_id' => $premium->id])->first();
            // $subscriber->deactivate_body = $x;
            $subscriber->provider_status =  'inactive';
            $subscriber->status =  'inactive';
            // $subscriber->provider_statusCode = $data['responseParam']['statusCode'];
            $subscriber->provider_description =  $Reason;
            $subscriber->save();
        }
        $project_name = Project::find($premium->project_id->first()->name);
        if ($callback != null) {
            $base_uri = $callback; //config('app.at_base_uri'); //'http://**************'
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('POST', '/', [
                'headers' => [
                    'Content-Type'     => 'application/json',
                    'Accept'     => 'application/json',
                ],
                //'json' => ['keyword' => $premium->activation_keywords, 'project' => $project_name, 'number' => $Msisdn, 'network' =>  'safaricom', 'type' => 'deactivation', 'status' => 'deactivated']
                'json' => [
                    'project' =>  $project_name, 'shortcode' => $premium->shortcode, 'keyword' => $premium->activation_keywords,
                    'number' => $Msisdn, 'network' =>  'safaricom', 'type' => 'deactivation', 'status' =>  $subscriber->provider_status
                ]
            ]);
            $code = $response->getStatusCode();
            $reason = $response->getReasonPhrase();
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string

            Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
            Log::info('stringbody from customer deactivation notice' . $stringBody);
            $data = json_decode($stringBody, true);
            Log::info($data);
        }

        return response()->json([
            'status' => 'Success',
            "requestId" => $requestId,
            "responseId" => uniqid('SOZDEACTIVATIONDLR') . time(),
            "responseTimeStamp" => date('Ymdhi'),
            "operation" => "CP_NOTIFICATION",
            "responseParam" => [
                "status" => "0",
                "statusCode" => "0000",
                "description" => "Success"
            ]
        ]);
    }
    public function MOSms()
    {
        // Receiving simple message using PHP through HTTP Post
        // This example will store every received SMS to a SQL table
        // http://jasminsms.com


        $MO_SMS = $_POST;
        /*
        $db = pg_connect('host=127.0.0.1 port=5432 dbname=sms_demo user=jasmin password=jajapwd');
        if (!$db)
            // We'll not ACK the message, Jasmin will resend it later
            die("Error connecting to DB");

        $QUERY = "INSERT INTO sms_mo(id, from, to, cid, priority, coding, validity, content) ";
        $QUERY.= "VALUES ('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s');";

        $Q = sprintf($QUERY, pg_escape_string($MO_SMS['id']), 
                            pg_escape_string($MO_SMS['from']), 
                            pg_escape_string($MO_SMS['to']), 
                            pg_escape_string($MO_SMS['origin-connector']), 
                            pg_escape_string($MO_SMS['priority']), 
                            pg_escape_string($MO_SMS['coding']), 
                            pg_escape_string($MO_SMS['validity']), 
                            pg_escape_string($MO_SMS['content'])
                            );
        pg_query($Q);
        pg_close($db);*/

        // Acking back Jasmin is mandatory
        echo "ACK/Jasmin";
    }

    private function getCampaignId($project_id, $keyword)
    {
        if(!$shortcode_campaign = DB::table('campaigns')->where(['name' => 'shortcode_campaign', 'project_id' => $project_id])->first()->id ?? false) {
            $shortcode_campaign = DB::table('campaigns')->insertGetId(['name' => 'shortcode_campaign', 'project_id' => $project_id]);
           } 
            if(DB::table('campaigns')->where('detail', 'like', '%'.$keyword.'%')->where('project_id', $project_id)->exists() ) {
                $campaign_id = DB::table('campaigns')->where('detail', 'like', '%'.$keyword.'%')->where('project_id', $project_id)->first()->id ;
                Log::info('campaign exists');
            } else {
                $campaign_id =  $shortcode_campaign;
                Log::info('default campaign used');
            }

        return  $campaign_id;
    }

    public function interactivebulk(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('interactivebulk: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);
        $requestId = $data['requestId'];
        $requestTimeStamp = $data['requestTimeStamp'];
        $operation = $data['operation'];
        if ($operation !== "INTERACTIVE") {
            Log::info("skipping processing because of unknown operation");
            //email
            return response()->json([
                "requestId" => $requestId,
                "responseId" => uniqid('INTERACTIVE') . time(),
                "responseTimeStamp" => date('Ymdhi'),
                "operation" => "CP_NOTIFICATION",
                "responseParam" => [
                    "status" => "0",
                    "statusCode" => "0000",
                    "description" => "Success"
                ]
            ]);
        }

        $dataNames = array_column($data['requestParam']['data'], 'name');
        $dataValues = array_column($data['requestParam']['data'], 'value');
        $dataArray = array_combine($dataNames, $dataValues);
        $additionalDataNames = array_column($data['requestParam']['additional_data'], 'name');
        $additionalDataValues = array_column($data['requestParam']['additional_data'], 'value');
        $addittionalDataArray = array_combine($additionalDataNames, $additionalDataValues);
        
        
        $from = $dataArray['Msisdn'];
        $command = $dataArray['Command'];
        $to = $addittionalDataArray['DA'];
        $message = $addittionalDataArray['SMS'];
        $messagePart =  ceil((strlen($message) + substr_count($message, "\n")) / 160); //message parts
        $shortcode =   Shortcode::where('name', $to)->where('status', 'active')->exists() ? Shortcode::where('name', $to)->first() : null;
        Log::info('Shortcode is ' .$shortcode->name);
        $messageId = bin2hex(random_bytes(20));
        $project = $shortcode ? Project::find($shortcode->project_id) : null;
        Log::info('Project is ' .$project->name ?? 'no project found');
        $campaignId = $this->getCampaignId($project->id, $message);
        $providerId = $requestId;
        try{
            $messageProcessed= $this->processMoMessage($from, $to, $message, $project, $messageId, $providerId, $requestTimeStamp, $campaignId, $messagePart, $shortcode, "safaricom");
            Log::info('mo message processed');
        } catch(Exception $e) {
            Log::info('error processing interactive bulk'.$e->getMessage());
        }

            return response()->json([
            "requestId" => $requestId,
            "responseId" => uniqid('SOZDEACTIVATIONDLR') . time(),
            "responseTimeStamp" => date('Ymdhi'),
            "operation" => "CP_NOTIFICATION",
            "responseParam" => [
                "status" => "0",
                "statusCode" => "0000",
                "description" => "Success"
            ]
        ]);
    }
    
    public function receiveAirtelMo(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('AIRTEL MO MESSAGE: ' . $x);
        echo "ACK/Jasmin";
        Log::info($_GET);
        $id = $_GET["id"] ?? "";
        $from = $_GET["from"] ?? "";
        $to = $_GET["to"] ?? "";
        $origin_connector = $_GET['origin-connector'] ?? "";
        $priority = $_GET['priority'] ?? "";
        $coding = $_GET['coding'] ?? "";
        $binary = $_GET['binary'] ?? "";
        $message =  $_GET['content'] ?? "";
        $coding = $_GET['coding'] ?? "";
        $messagePart =  ceil((strlen($message) + substr_count($message, "\n")) / 160); //message parts
        $shortcode =   Shortcode::where('name', $to)->where('status', 'active')->exists() ? Shortcode::where('name', $to)->first() : null;
        $messageId = bin2hex(random_bytes(20));
        $project = $shortcode ? Project::find($shortcode->project_id) : null;
        $campaignId = $this->getCampaignId($project->id, $message);
        $providerId = $id;
        $requestTimeStamp = Carbon::now();
        try{
            $messageProcessed= $this->processMoMessage($from, $to, $message, $project, $messageId, $providerId, $requestTimeStamp, $campaignId, $messagePart, $shortcode, "airtel");
            Log::info('mo message processed');
        } catch(Exception $e) {
            Log::info('error processing interactive bulk'.$e->getMessage());
        }
    
    }



   protected function processMoMessage($from, $to, $message, $project, $messageId, $providerId, $requestTimeStamp, $campaignId, $messagePart, $shortcode, $telco){
    if (!$project) {
        Log::info("skipping processing because of missing project or shortcode");
        exit;
    }
    try{
        $smsId = DB::table('sms')->insertGetId([
            'cost' => 0,
            'request_id' => $providerId,
            'price' => $shortcode->price,
            'message_id' => $messageId,
            'message_part' => $messagePart,
            'to' => $to,
            'detail' => $message,
            'status' => 'success',
            'status_code' => '11',
            'description' => 'Delivered',
            'trace_id' => null,
            'channel' => 'interactive',
            'package_id' => '',
            'project_id' => $shortcode->project_id,
            'message' => $message,
            'from' => $from,
            'direction' => 'inbound',
            'created_at' => now(),
            'sent_at' => Carbon::parse($requestTimeStamp),
            'campaign_id' => $campaignId,
            'tz_id' => null,
            'bulk_id' => uniqid('bulk', true) . time(),
            'telco' => $telco,
            'type' => 'shortcode',
            'uri' => '',
        ]);
      
    Log::info('mo sms inserted at id' . $smsId);
    } catch(Exception $e) {
        Log::info($e->getMessage());
    }

    $callback = $project !== null && $project->smscallbacks()->count() > 0 ? $project->smscallbacks()->first()->inboxCallbackUrl : null;
    $callback_array = explode(',', $callback);

    foreach ($callback_array as $webhook) {
        if ($webhook != null) {
            try {
                $base_uri = $webhook;
                $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
                $response = $client->request('POST', '', [
                    'headers' => [
                        'Content-Type'     => 'application/json',
                        'Accept'     => 'application/json',
                    ],
                    'json' => [
                        'project' => $project->name, 'number' => $from, 'shortcode' => $to, 'message' => $message, 'messageId' =>  $messageId,
                        'channel' => 'interactive', 'status' =>  'success', 'network' =>  'safaricom', 'type' => 'interactive', 'timestamp' => time()
                    ]
                ]);
                $code = $response->getStatusCode();
                $reason = $response->getReasonPhrase();
                $body = $response->getBody();
                $stringBody = (string) $body;
                //$sms->link_notify_response_from_customer_body = $stringBody;
                //$sms->save();
                Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
                Log::info('stringbody from customer interactive bulk ' . $stringBody);
            } catch (Exception $e) {
                Log::info('failed to fetch inbox url');
            }
        }
    }

    AutomationService::createContact($project->id, $from);
    Log::info('automating '.$from.' '.$project->name.' '.$message);
    AutomationService::automate($project, $message, $from, "interactive");
    return true;
}

    public function receiveAirtelDlr(Request $request) {
        $x = file_get_contents('php://input');
        // Acking back Jasmin is mandatory
        echo "ACK/Jasmin";
        Log::info($_GET);
        $id = $_GET["id"] ?? "";
        $level = $_GET["level"] ?? "";
        $message_status = $_GET["message_status"] ?? "";
        $connector = $_GET['connector'] ?? "";
        $id_smsc = $_GET['id_smsc'] ?? "";
        $sub = $_GET['sub'] ?? "";
        $dlvrd = $_GET['dlvrd'] ?? "";
        $subdate =  $_GET['subdate'] ?? "";
        $donedate = $_GET['donedate'] ?? "";
        $err = $_GET['err'] ?? "";
        $text = $_GET['text'] ?? "";
        $status = ($message_status == 'DELIVRD') ? 'success' : 'sent';
        $description = ($status == 'success') ? 'Delivered' : $message_status;

        try {
            ProcessAirtelBulkDlr::dispatch($id, $status, $description, $err)->onQueue('dlr')->delay(now()->addMinutes(2));
            Log::info('AIRTEL dlr sms just queued id:'.$id. ' description:'.$message_status);
        } catch(Exception $e) {
            Log::info("AIRTEL dlr sms queue error" . $e->getMessage());
        }
    }


    //Discontinued by safaricom
    public function interactivebulkdlr(Request $request)
    {
        $x = file_get_contents('php://input');
        Log::info('interactivebulkdlr: ' . $x);
        $x = file_get_contents('php://input');
        $data = json_decode($x, true);
        Log::info($data);

        $names = array_column($data['requestParam']['data'], 'name');
        $values = array_column($data['requestParam']['data'], 'value');
        $array = array_combine($names, $values);
        $additional_names = array_column($data['requestParam']['additionalData'], 'name');
        $additional_values = array_column($data['requestParam']['additionalData'], 'value');
        $additional_array = array_combine($additional_names, $additional_values);

        $requestId = $data['requestId'];
        $requestTimeStamp = $data['requestTimeStamp'];
        $operation = $data['operation']; //INTERACTIVE
        $Msisdn = $array['Msisdn'];
        $Command = $array['Command']; //CP_NOTIFICATION
        $DA = $additional_array['DA'];
        $SMS = $additional_array['SMS'];

    }
    public  function subscribestatusGenerator($phone, $projectId, $premiumId)
    {

        if (strlen($phone) !== 12) {
            return ['status' => 'error', 'description' => 'Unsupported Number'];
        } elseif (!Premium::where(['id' => $premiumId, 'project_id' => $projectId, 'status' => 'active'])->exists()) {
            return ['status' => 'error', 'description' => 'Unknown or Expired Premium Service'];
        } elseif (Subscriber::where(['msisdn' => $phone, 'project_id' => $projectId, 'premium_id' => $premiumId, 'status' => 'active'])->exists()) {
            return ['status' => 'error', 'description' => 'Subscriber already Exists'];
        } elseif (strlen($phone) == 12) {
            return ['status' => 'accepted', 'description' => 'accepted'];
        } //check if in blacklist
    }
    private  function unsubscribestatusGenerator($phone, $projectId, $premiumId)
    {
        if (strlen($phone) !== 12) {
            return ['status' => 'error', 'description' => 'Unsupported Number'];
        } elseif (!Premium::where('id', $premiumId)->where('project_id', $projectId)->where('status', 'active')->exists()) {
            return ['status' => 'error', 'description' => 'Unknown or Expired Premium Service'];
        } elseif (!Subscriber::where(['msisdn' => $phone,  'project_id' => $projectId, 'premium_id' => $premiumId])->exists()) {
            return ['status' => 'error', 'description' => 'Unknown Subscriber'];
        } elseif (Subscriber::where(['msisdn' => $phone,  'project_id' => $projectId, 'premium_id' => $premiumId, 'status' => 'inactive'])->exists()) {
            return ['status' => 'error', 'description' => 'Subscriber is already Inactive'];
        } elseif (strlen($phone) == 12) {
            return ['status' => 'accepted', 'description' => 'accepted'];
        } //check if in blacklist
    }
    public function sendPremiumStatusGenerator($phone, $projectId, $premiumId)
    {
        if (strlen($phone) !== 12) {
            return ['status' => 'error', 'description' => 'Unsupported Number'];
        } elseif (!Premium::where(['id' => $premiumId, 'project_id' => $projectId, 'status' => 'active'])->exists()) {
            return ['status' => 'error', 'description' => 'Unknown or Expired Premium Services'];
        } elseif ((Subscriber::where(['msisdn' => $phone, 'project_id' => $projectId, 'premium_id' => $premiumId, 'status' => 'active'])->exists() &&
            Premium::where(['id' => $premiumId, 'offer_type' => 'subscription'])->exists())) {
            return ['status' => 'error', 'description' => 'Unknown Subscriber'];
        } elseif (strlen($phone) == 12) {
            return ['status' => 'accepted', 'description' => 'accepted'];
        } //check if in blacklist

    }
}
