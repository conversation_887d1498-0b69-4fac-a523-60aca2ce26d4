<?php

namespace App\Http\Controllers;

use App\Queueautomation;
use Illuminate\Http\Request;

class QueueautomationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Queueautomation  $queueautomation
     * @return \Illuminate\Http\Response
     */
    public function show(Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Queueautomation  $queueautomation
     * @return \Illuminate\Http\Response
     */
    public function edit(Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Queueautomation  $queueautomation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Queueautomation  $queueautomation
     * @return \Illuminate\Http\Response
     */
    public function destroy(Queueautomation $queueautomation)
    {
        //
    }
}
