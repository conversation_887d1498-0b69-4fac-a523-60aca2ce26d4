<?php

/**
 * This interface specifies the methods required to send a message
 * actual senders will implement the methods in their own way. eg. twilio sender will package the "message"
 * according to specific twilio requirements. the product here is the message
 */

interface Msgsender
{
    //private from;
    public function validateFields(): void;
    public function response_shortener(): void;
    public function sdpShortener(): void;
    public function getProject(): void;
    public function setCampaign(): void;
    public function checkBalance(): void;
    public function setType(): void;
    public function checkMainAccountUnitsForTelcos(): void;
    public function setFromForAllTelcos(): void;
    public function formatMessage(): void;
    public function getTo(): void;
    public function removeBlacklist(): void;
    public function createStatus(): void;
    public function createTelco(): void;
    public function safaricomCostCalculator(): void;
    public function airtelCostCalculator(): void;
    public function telkomCostCalculator(): void;
    public function assignSafaricomCredits(): void;
    public function assignAirtelCredits(): void;
    public function assignSafaricomFroms(): void;
    public function assignAirtelFroms(): void;
    public function getPackageId(): void;
    public function getBulkId(): void;
    public function createRawMsg(): void;
    public function createDatabaseMsg(): void;
    public function getSupplierPayload(): void;
    public function sendToFileBroker(): void;
    public function sendtoPaymentLog(): void;
    public function queueNow(): void;
    public function schedule(): void;
    public function reset(): void;
}
class Message
{
}

class Safaricomsender implements Msgsender
{
    private Message $message;
    public function __construct()
    {
        $this->reset();
    }
    public function reset(): void
    {
        $this->message = new Message();
    }
    public function validateFields(): void
    {
    }
    public function response_shortener(): void
    {
    }
    public function sdpShortener(): void
    {
    }
    public function getProject(): void
    {
    }
    public function setCampaign(): void
    {
    }
    public function checkBalance(): void
    {
    }
    public function setType(): void
    {
    }
    public function checkMainAccountUnitsForTelcos(): void
    {
    }
    public function setFromForAllTelcos(): void
    {
    }
    public function formatMessage(): void
    {
    }
    public function getTo(): void
    {
    }
    public function removeBlacklist(): void
    {
    }
    public function createStatus(): void
    {
    }
    public function createTelco(): void
    {
    }
    public function safaricomCostCalculator(): void
    {
    }
    public function airtelCostCalculator(): void
    {
    }
    public function telkomCostCalculator(): void
    {
    }
    public function assignSafaricomCredits(): void
    {
    }
    public function assignAirtelCredits(): void
    {
    }
    public function assignSafaricomFroms(): void
    {
    }
    public function assignAirtelFroms(): void
    {
    }
    public function getPackageId(): void
    {
    }
    public function getBulkId(): void
    {
    }
    public function createRawMsg(): void
    {
    }
    public function createDatabaseMsg(): void
    {
    }
    public function getSupplierPayload(): void
    {
    }
    public function sendToFileBroker(): void
    {
    }
    public function sendtoPaymentLog(): void
    {
    }
    public function queueNow(): void
    {
    }
    public function schedule(): void
    {
    }
    public function getMessage(): Message
    {
        $message = $this->message;
        $this->reset();
        return $message;
    }
}

class Airtelsender implements Msgsender
{
    private Message $message;
    public function __construct()
    {
        $this->reset();
    }
    public function reset(): void
    {
        $this->message = new Message();
    }
    public function validateFields(): void
    {
    }
    public function response_shortener(): void
    {
    }
    public function sdpShortener(): void
    {
    }
    public function getProject(): void
    {
    }
    public function setCampaign(): void
    {
    }
    public function checkBalance(): void
    {
    }
    public function setType(): void
    {
    }
    public function checkMainAccountUnitsForTelcos(): void
    {
    }
    public function setFromForAllTelcos(): void
    {
    }
    public function formatMessage(): void
    {
    }
    public function getTo(): void
    {
    }
    public function removeBlacklist(): void
    {
    }
    public function createStatus(): void
    {
    }
    public function createTelco(): void
    {
    }
    public function safaricomCostCalculator(): void
    {
    }
    public function airtelCostCalculator(): void
    {
    }
    public function telkomCostCalculator(): void
    {
    }
    public function assignSafaricomCredits(): void
    {
    }
    public function assignAirtelCredits(): void
    {
    }
    public function assignSafaricomFroms(): void
    {
    }
    public function assignAirtelFroms(): void
    {
    }
    public function getPackageId(): void
    {
    }
    public function getBulkId(): void
    {
    }
    public function createRawMsg(): void
    {
    }
    public function createDatabaseMsg(): void
    {
    }
    public function getSupplierPayload(): void
    {
    }
    public function sendToFileBroker(): void
    {
    }
    public function sendtoPaymentLog(): void
    {
    }
    public function queueNow(): void
    {
    }
    public function schedule(): void
    {
    }
    public function getMessage(): Message
    {
        $message = $this->message;
        $this->reset();
        return $message;
    }
}
class Sendingdirector
{
    private $msgsender;

    public function setSender(Msgsender $msgsender)
    {
        $this->msgsender = $msgsender;
    }

    public function sendSafaricom(Msgsender $msgsender)
    {
        $msgsender->reset();
        $msgsender->validateFields();
        $msgsender->response_shortener();
        $msgsender->sdpShortener();
        $msgsender->getProject();
        $msgsender->setCampaign();
        $msgsender->checkBalance();
        $msgsender->sendToFileBroker();
        $msgsender->sendtoPaymentLog();
        $msgsender->schedule();
    }
    public function sendAirtel(Msgsender $msgsender)
    {
        $msgsender->reset();
        $msgsender->validateFields();
        $msgsender->response_shortener();
        $msgsender->sdpShortener();
        $msgsender->getProject();
        $msgsender->setCampaign();
        $msgsender->checkBalance();
        $msgsender->sendToFileBroker();
        $msgsender->sendtoPaymentLog();
        $msgsender->schedule();
    }
}


//sozuri app
class Sozuriapp
{

    public function sendMessage()
    {
        $director = new Sendingdirector();
        $msgsender = new Safaricomsender();
        $director->sendSafaricom($msgsender);
        $message =  $msgsender->getMessage();

        $director = new Sendingdirector();
        $msgsender = new Airtelsender();
        $director->sendAirtel($msgsender);
        $message =  $msgsender->getMessage();
    }
}
