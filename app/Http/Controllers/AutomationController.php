<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAutomationRequest;
use App\Http\Requests\UpdateAutomationRequest;
use App\Models\Automation;
use App\Policies\ProjectPolicy;
use Illuminate\Http\Request;
use App\Project;
use Auth;
class AutomationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $projectId)
    {
        $project = Project::find($projectId);

        if ($request->user()->cannot('update', $project)) {
            //abort(403);
            return back()->withErrors('Unauthorized');
        }
        $automations = Automation::where('project_id', $projectId)->orderBy('created_at', 'desc')->get();
        return view('automations.index', compact('automations', 'project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreAutomationRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $projectId)
    {
        $request->validate([
            'project_id' => 'required',
            //'reply_from' => 'required'
        ]);
        //dd($request->all());
        $project = Project::find($projectId);

        if ($request->user()->cannot('update', $project)) {
            return back()->withErrors('Unauthorized');
        }
        $automations = Automation::insertGetId([
            'name' => uniqid(),
            'project_id'=> $projectId,
            'from' => $request->content ?? null,
            'from_channel' => $request->from_channel ?? "2way sms",
            'content' => $request->content ?? null,
            'reply' => $request->reply ?? null,
            'reply_to' => $request->reply_to ?? null,
            'reply_channel' => $request->reply_channel ?? null,
            'reply_from' => $request->reply_from ?? null,
            'forward_to' => $request->forward_to ?? null,
            'is_active' => 1,
            'created_by' => $request->user()->id,
            ]);
            $automations = Automation::where('project_id', $projectId)->orderBy('created_at', 'desc')->get();
            return back()->with(['message' => 'Automation Created Successfully']);

        }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Automation  $automation
     * @return \Illuminate\Http\Response
     */
    public function show(Automation $automation)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Automation  $automation
     * @return \Illuminate\Http\Response
     */
    public function edit(Automation $automation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateAutomationRequest  $request
     * @param  \App\Models\Automation  $automation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $projectId, $automationId)
    {
        $request->validate([
            'project_id' => 'required',
            'automation_id' => 'required'
        ]);
        //dd($request->all());
        $project = Project::find($projectId);
        $automation = Automation::where(['id' => $automationId, 'project_id' => $projectId])->exists() ?
        Automation::where(['id' => $automationId, 'project_id' => $projectId])->first(): null;
        if (!$automation) {
            return back()->withErrors('Rule not found');
        }
        if ($request->user()->cannot('update', $project)) {
            return back()->withErrors('Unauthorized');
        }
        $automation->update([
            'from' => $request->content ?? null,
            'from_channel' => $request->from_channel ?? "2way sms",
            'content' => $request->content ?? null,
            'reply' => $request->reply ?? null,
            'reply_to' => $request->reply_to ?? null,
            'reply_channel' => $request->reply_channel ?? null,
            'reply_from' => $request->reply_from ?? null,
            'forward_to' => $request->forward_to ?? null,
            'is_active' => 1,
            'updated_by' => $request->user()->id,
            ]);

            return back()->with(['message' => 'Automation Updated Successfully']);
        
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Automation  $automation
     * @return \Illuminate\Http\Response
     */
    public function destroy($projectId, $automationId)
    {

        //dd($request->all());
        $project = Project::find($projectId);

        if (Auth::user()->cannot('update', $project)) {
            return back()->withErrors('Unauthorized');
        }
        Automation::where(['id' => $automationId, 'project_id' => $projectId])->delete();
        return back()->with(['message' => 'Automation Deleted Successfully']);

    }
}
