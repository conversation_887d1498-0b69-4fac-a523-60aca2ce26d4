<?php

namespace App\Http\Controllers;

use App\Exclusion;
use App\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Talklog;
use Maatwebsite\Excel\Concerns\ToArray;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
class ExclusionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Exclusion  $exclusion
     * @return \Illuminate\Http\Response
     */
    public function show(Exclusion $exclusion)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Exclusion  $exclusion
     * @return \Illuminate\Http\Response
     */
    public function edit(Exclusion $exclusion)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Exclusion  $exclusion
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        //one must own th eproject that owns this contact
        //make exclusion and delete contact
        if ($request->filled('contact_id')) {
            $original_contact = Contact::find($request->contact_id);
            $contact = $original_contact->toArray();

             unset($contact['id']);
            $exclusion = new Exclusion();
            $exclusion->fill( $contact  );
            $exclusion->save();

            $original_contact->delete();

        Log::info('User created a new exclusion' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Exclusion",'action' => "updated",
            'message' => "User ".Auth::id()." created a new exclusion:",
            'size' => "Issue",'user_id' => Auth::id(),'clientIp' => $request->ip(),
            'clientRequestMehod' => $request->method(),'clientRequestPath' => $request->path(),'clientRequestUri' => $request->fullurl(),'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);


            $request->session()->flash('message','Exclusion created');
        //make contact and delete excliusion
        } elseif ($request->filled('exclusion_id')) {
            $original_exclusion = Exclusion::find($request->exclusion_id);
            $exclusion = $original_exclusion->toArray();

            unset($exclusion['id']);

            $contact = new Contact();
            $contact->fill($exclusion);
            $contact->save();

            $original_exclusion->delete();

            Log::info('User deleted an exclusion' . Auth::id());
            $log = Talklog::create([
                'level' => 6,
                'resource' => "Exclusion",'action' => "updated",
                'message' => "User ".Auth::id()." deleted a exclusion:",
                'size' => "Issue",'user_id' => Auth::id(),'clientIp' => $request->ip(),
                'clientRequestMehod' => $request->method(),'clientRequestPath' => $request->path(),'clientRequestUri' => $request->fullurl(),'ClientRequestUserAgent' => $request->header('User-Agent'),
                'status' => "Success"
            ]);
            $request->session()->flash('message','Exclusion deleted');

        }
        elseif($request->has('mobile')) {
            if( Contact::where($request->mobile)->exists()){
            $original_contact = Contact::find($request->mobile);
            $contact = $original_contact->toArray();
             unset($contact['id']);
            $exclusion = new Exclusion();
            $exclusion->fill( $contact  );

            $exclusion->optedOut = true;
            $exclusion->optOut = Carbon::now();
            $exclusion->optOutReason = $request->reason;

            $original_contact->delete();
            }
        }
        return back();

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Exclusion  $exclusion
     * @return \Illuminate\Http\Response
     */
    public function destroy(Exclusion $exclusion)
    {
        //
    }
}
