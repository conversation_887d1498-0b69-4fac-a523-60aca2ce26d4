<?php

namespace App\Http\Controllers;

use App\Template;
use App\Project;
use Illuminate\Http\Request;

class TemplateController extends Controller
{
    public function __construct()
    {
       // $this->authorizeResource(Template::class, 'templates');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $this->authorize('viewAny', [Template::class, $id]);

        $project = Project::find($id);
        $templates = Template::where('project_id', $id)->orderBy('created_at','desc')->get();
        return view('templates.index', compact('templates','project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        $this->authorize('create', [Template::class, $id]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request,$id)
    {
        $this->authorize('create', [Template::class, $id]);

        $request->validate([
            'name' => 'required',
            'project_id' => 'required',
           // 'template_id' => 'required'
        ]);
        Template::create($request->all());
        $request->session()->flash('message','Ready to go! Template Created');
        return redirect('projects/'.$id.'/sms/create');

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Template  $template
     * @return \Illuminate\Http\Response
     */
    public function show(Template $template)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Template  $template
     * @return \Illuminate\Http\Response
     */
    public function edit( $pid, $id)
    {
        //
        $project = Project::find($pid);
        $template = Template::find($id);
        $this->authorize('update', $template);


        return view('templates.edit', compact('project','template'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Template  $template
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $pid, $id)
    {
        $project = Project::find($request->project_id);
        $template = Template::find($request->template_id);

        $this->authorize('update', $template);

        //$this->authorize('update', User::class, Template::class);
        $request->validate([
            'name' => 'required',
            'project_id' => 'required',
            'template_id' => 'required'
        ]);
     
        $template->update($request->except('template_id'));
        $request->session()->flash('message','Template Updated');
        //return redirect('projects/'.$request->project_id.'/templates');
        return redirect('projects/'.$pid.'/sms/create');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Template  $template
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$pid,$id)
    {

      //  $this->authorize('delete', Template::class);
        $template = Template::find($id);
        $this->authorize('delete', $template);

        $template->delete();
        $request->session()->flash('message','Template: '.$template->name.' deleted');
        return redirect('projects/'.$pid.'/templates');
    }
}
