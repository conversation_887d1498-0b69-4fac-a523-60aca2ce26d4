<?php

namespace App\Http\Controllers;

use App\AuthenticationProvider;
use Illuminate\Http\Request;

class AuthenticationProviderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\AuthenticationProvider  $authenticationProvider
     * @return \Illuminate\Http\Response
     */
    public function show(AuthenticationProvider $authenticationProvider)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\AuthenticationProvider  $authenticationProvider
     * @return \Illuminate\Http\Response
     */
    public function edit(AuthenticationProvider $authenticationProvider)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\AuthenticationProvider  $authenticationProvider
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AuthenticationProvider $authenticationProvider)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\AuthenticationProvider  $authenticationProvider
     * @return \Illuminate\Http\Response
     */
    public function destroy(AuthenticationProvider $authenticationProvider)
    {
        //
    }
}
