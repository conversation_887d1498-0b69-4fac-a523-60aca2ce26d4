<?php

namespace App\Http\Controllers;

use App\Transactiontype;
use Illuminate\Http\Request;

class TransactiontypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Transactiontype  $transactiontype
     * @return \Illuminate\Http\Response
     */
    public function show(Transactiontype $transactiontype)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Transactiontype  $transactiontype
     * @return \Illuminate\Http\Response
     */
    public function edit(Transactiontype $transactiontype)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Transactiontype  $transactiontype
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Transactiontype $transactiontype)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Transactiontype  $transactiontype
     * @return \Illuminate\Http\Response
     */
    public function destroy(Transactiontype $transactiontype)
    {
        //
    }
}
