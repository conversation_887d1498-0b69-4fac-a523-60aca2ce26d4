<?php

namespace App\Http\Controllers;

use App\Help;
use Illuminate\Http\Request;
use Auth;
class Help<PERSON>ontroller extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $helps = Help::get();
        return view('helps.index', compact('helps'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //$this->authorize('create', Help::class);
        return view('helps.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //$this->authorize('create', Help::class);
        Help::create($request->all());
        return back()->withMessage('Thank you. Query sent! WezaSMS will get back to you ASAP. ');
    }
    public function developerRequest(Request $request)
    {
        //$this->authorize('create', Help::class);
        Help::create($request->all());
        return back()->withMessage('This is just the beginning of a beautiful journey. No matter 
        your programming language, integration is a cinch! You will receive your next steps on email');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Help  $help
     * @return \Illuminate\Http\Response
     */
    public function show(Help $help)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Help  $help
     * @return \Illuminate\Http\Response
     */
    public function edit(Help $help)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Help  $help
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Help $help)
    {
        $this->authorize('update', $help);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Help  $help
     * @return \Illuminate\Http\Response
     */
    public function destroy(Help $help)
    {
        $this->authorize('delete', $help);
    }
}
