<?php

namespace App\Http\Controllers;

use App\Typeofservice;
use Illuminate\Http\Request;

class TypeofserviceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Typeofservice  $typeofservice
     * @return \Illuminate\Http\Response
     */
    public function show(Typeofservice $typeofservice)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Typeofservice  $typeofservice
     * @return \Illuminate\Http\Response
     */
    public function edit(Typeofservice $typeofservice)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Typeofservice  $typeofservice
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Typeofservice $typeofservice)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Typeofservice  $typeofservice
     * @return \Illuminate\Http\Response
     */
    public function destroy(Typeofservice $typeofservice)
    {
        //
    }
}
