<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use App\User;
use App\Account;
use App\AuthenticationProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;
use Symfony\Component\Console\Input\Input;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use APp\MAil\NewRegistration;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    | 
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    //protected $redirectTo = '/dashboard';
    //if the user has enabled multifactor, then UNVALIDATE the user
    protected function redirectTo()
    {
        $user = User::find(Auth::user()->id);
        $user->last_login_ip = $_SERVER['REMOTE_ADDR'];
        $user->last_login = Carbon::now();
        $user->save();

        $redirectTo  = '/dashboard';

        if ($user->isGlobalAdmin === 1) {
            $redirectTo = '/admin/dashboard';
        }
        if ($user->multiFactor === 1) {
            $user->otpVerified = false;
            $user->save();
            //$redirectTo = '/generateOtp';
            //return  $redirectTo;
        }

        return  $redirectTo;
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToProvider($service)
    {
        return Socialite::driver($service)->redirect();
        //else, check and save
        // $user = Socialite::driver('google')->user();
        //dd( $user );
    }

    public function getAuthorizationFirst($service)
    {
        return Socialite::driver($service)->stateless()->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleProviderCallback($service)
    {
        //if($request->has('code')) return $this->getAuthorizationFirst();
        //$social_user = Socialite::driver($service)->user();
        // $social_user = Socialite::driver('google')->stateless()->user();
        $social_user = Socialite::driver($service)->stateless()->user();
        Log::info(json_encode($social_user));
        //find if user exists in db
        // OAuth Two Providers
        $token = $social_user->token;
        $refreshToken = $social_user->refreshToken; // not always provided
        $expiresIn = $social_user->expiresIn;

        // All Providers
        $socialid = $social_user->getId();
        $nickname = $social_user->getNickname();
        $name = $social_user->getName();
        $email = $social_user->getEmail();
        $avatar = $social_user->getAvatar();
        Log::info($socialid);
        Log::info($nickname);
        Log::info($name);
        Log::info($email);
        Log::info($avatar);
        Log::info($token);
        Log::info($refreshToken);
        Log::info($expiresIn);
        Log::info(json_encode($social_user));

        //if new user, create user, create an account, make a transaction,


        //if ($this->doesUserExist($socialid)) {
        if (User::where('email', $email)->exists()) {

            //get user
            //$user = User::find($this->getUserId($socialid));
            $user = User::where('email', $email)->first();
            $user->last_login_ip = $_SERVER['REMOTE_ADDR'];
            $user->last_login = Carbon::now();
            $user->save();
            // Login and "remember" the given user...
            Auth::login($user, true);

            $redirectTo = "/dashboard";


            //check multifactor
            if ($user->multiFactor === 1) {
                $user->otpVerified = false;
                $user->save();
                // $redirectTo = '/generateOtp';
            }
            if ($user->isGlobalAdmin === 1) {
                $redirectTo = '/admin/dashboard';
            }
            // return redirect()->intended('dashboard');
            return redirect($redirectTo);
        } else {
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'isClerk' =>  1,
                'referral_code' => uniqid('ReTz'),
                'last_login_ip' => $_SERVER['REMOTE_ADDR'],
                'last_login' => Carbon::now(),
            ]);

            $to = [ [ 'email' => $email, 'name' => $name ] ];
            $bcc = [ [ 'email' => "<EMAIL>", 'name' => "New Registration", ] ];
            Mail::to($to)->bcc($bcc)->send(new NewRegistration($user));


            //create authentication details
            $AuthenticationProvider = AuthenticationProvider::create([
                'name' =>   $name,
                'nickname' => $nickname,
                'user_id' => $user->id,
                //'email' => $email,
                'providerkey' => $socialid,
                'providertype' =>  $service,
                'avatar' =>  $avatar,
            ]);
            //log them in
            Auth::login($user, true);
            // return redirect()->intended('dashboard');
            return redirect('dashboard');
        }
    }


    public function doesUserExist($id)
    {
        return AuthenticationProvider::where('providerkey', '=', $id)->exists();
    }
    public function getUserId($id)
    {
        return AuthenticationProvider::where('providerkey', '=', $id)->value('user_id');
    }
}

/*


Laravel\Socialite\Two\User {#412 ▼
  +token: "ya29.Il_AB2P6UU3bdymushe3dYEHT2zgj3Nc3Hx0eZy202EYDRnw2D0MTx-NZHc7sZ1bdPkVD6wlpjCziA83z9nPU6xxiu2a3RbaQs3k2yTHh_2TEsAYdTxVstoPK6SKgLYNpA"
  +refreshToken: null
  +expiresIn: 3599
  +id: "106345170328577848637"
  +nickname: null
  +name: "Lawrence Njenga"
  +email: "<EMAIL>"
  +avatar: "https://lh5.googleusercontent.com/-IuhH6UoE3wM/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rddwgk-dJtCi9B_CX7gNN5w8RJ7EQ/photo.jpg"
  +user: array:12 [▶]
  +"avatar_original": "https://lh5.googleusercontent.com/-IuhH6UoE3wM/AAAAAAAAAAI/AAAAAAAAAAA/ACHi3rddwgk-dJtCi9B_CX7gNN5w8RJ7EQ/photo.jpg"
}


i
*/
