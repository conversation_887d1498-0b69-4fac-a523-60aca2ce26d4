<?php

namespace App\Http\Controllers\Auth;

use App\User;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\RegistersUsers;
use App\Organization;
use App\Account;
use App\Mail\NewRegistration;
use App\Collaboration;
use App\Referral;
use Illuminate\Support\Facades\Mail;
use App\Project;
use Illuminate\Support\Facades\Hash;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller 
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/dashboard'; //assuming there will never be self registration

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'name' => 'required|string|max:255',
            //  'fname' => 'required|string|max:255',
            // 'lname' => 'required|string|max:255',
            'mobile' => 'required|string|max:25|unique:users', //regex:/^[+]?[0-9]{7,13}$/|
            'email' => 'required|string|email|max:255|unique:users',
            //'password' => 'required|string|min:6|confirmed',
            'password' => 'required|string|min:6',
            'challengeResult' => 'required', // Custom rule

        ]);

    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\User
     */
    protected function create(array $data)
    {
        $user_count = User::count();
        //if its a collaboration, verify existence
        if (!$this->handleChallengeResult($data['challengeResult'])) {
            return null;
        }
        $verifyNumber = function ($phone) {
            return preg_match("/^[+]?[0-9]{4,13}$/", $phone);
        };
        //double cleaning in addition to validation
        $cleanNumber = function ($phone) {
            $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
            return $clean_number;
        };
        $referral_code = uniqid('RecTz');
        $user = User::create([
            'name' => $data['name'],
            'mobile' => $cleanNumber($data['mobile']),
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
            //'organization_id' =>  $organization_id,
            'isClerk' => 1,
            'referral_code' => $referral_code,
        ]);
        $to = [['email' => $data['email'], 'name' => $data['name']]];
        $bcc = [['email' => "<EMAIL>", 'name' => "New Registration",]];
        //Mail::to($to)->bcc($bcc)->queue(new NewRegistration($user));
        $when = now()->addMinutes(60);
        Mail::to($to)->bcc($bcc)->later($when, new NewRegistration($user));
        if ($user_count == 0) {
            // $user->isGlobalAdmin = true;

            $project = new Project();
            $project->name = "primary_project";
            $project->user_id = $user->id;
            $project->code = uniqid('Tz');
            $project->save();
        }


        if (array_key_exists('collabo_code', $data) && $data['collabo_code'] !== null) {
            $code = $data['collabo_code'];
            $collaboration = Collaboration::where('collabo_code', $code)->exists() ? Collaboration::where('collabo_code', $code)->first() : null;
            if (!$collaboration) {
                return;
            }
            if ($collaboration->email != $data['email']) {
                return null;
            }
            $collaboration->status = true;
            $collaboration->user_id = $user->id;
            $collaboration->save();
        }

        if (array_key_exists('referral_code', $data) && $data['referral_code'] !== null) {
            if (User::where('referral_code', '=', $data['referral_code'])->exists()) {
                Referral::create([
                    'referrer' => User::where('referral_code', '=', $data['referral_code'])->first()->id,
                    'url' => 'n',
                    'code' => $data['referral_code'],
                    'referred' => $user->id,
                    'referrer_reward' => '0', //update with reward
                    'referred_reward' => '0',
                    'status' => 'new',
                ]);
            }
            return $user;
        }

        return $user;
    }

    function handleChallengeResult(string $tokestring)
    {

        $secretKey = config('app.TURNSATILE_SECRET');
        $serverIp = config('app.sdp_source_address');
        $url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
        Log::info('verifying challenge');
        $client = new Client(['base_uri' =>  $url, 'verify' => false]);
        $response = $client->request('POST', '', [
            'headers' => [
                'Content-Type'     => 'application/json',
                'Accept'     => 'application/json',
            ],
            'json' =>  [
                'secret' => $secretKey,
                'response' => $tokestring,
                'remoteip' => $serverIp,
            ],
        ]);
        $code = $response->getStatusCode();
        $reason = $response->getReasonPhrase();
        // Decode the response
        $outcome = json_decode($response->getBody(), true);
        Log::info($outcome);
        if ($outcome['success'] ?? false) {
            return true;
        } else {
            return false;
        }
    }
}
