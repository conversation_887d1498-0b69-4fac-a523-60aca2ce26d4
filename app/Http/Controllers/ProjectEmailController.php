<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Project;
use DB;
use App\Models\ProjectEmail;
use Carbon\Carbon;
use GuzzleHttp\Client;

use Log;

class ProjectEmailController extends Controller
{
    public function index(Request $request, $projectId)
    {
        $project = Project::find($projectId);

        if ($request->user()->cannot('update', $project)) {
            return back()->withErrors('Unauthorized');
        }
        $project_emails = DB::table('project_emails')->where('project_id', $projectId)->orderBy('created_at', 'desc')->get();
        return view('project-emails.index', compact('project_emails', 'project'));
    }

    public function store(Request $request)
    {
        $data = $request->all();
        \Log::info($data);
        \Log::info("Email received! " . $data['recipient']);
                // Parse the incoming data
                $parsedData = [
                    'date' => Carbon::parse($request->input('Date')),
                    'from' => $request->input('From'),
                    'sender' => $request->input('sender'),
                    'to' => $request->input('To'),
                    'recipient' => $request->input('recipient'),
                    'subject' => $request->input('Subject'),
                    'references' => $request->input('References'),
                    'stripped_text' => $request->input('stripped-text'),
                    'stripped_html' => $request->input('stripped_html'),
                    'body_plain' => $request->input('body-plain'),
                    'body_html' => $request->input('body-html'),
                    'message_id' => $request->input('Message-Id'),
                    'in_reply_to' => $request->input('In-Reply-To'),
                    'content_type' => $request->input('content_type'),
                    'attachment_count' => $request->input('attachment-count') ?? 0,
                    'stripped_signature' => $request->input('stripped-signature'),
                    'signature' => $request->input('signature'),
                    'user_agent' => $request->input('User-Agent'),
                    'code' => $request->input('References'),
                    'token' => $request->input('token'),
                    'timestamp' => $request->input('timestamp'),
                    'project_id' => \App\Project::first()->id,

                    // Add more fields as needed
                ];
        //Log::info($parsedData);

        try{
            $message =  $parsedData['subject'].'. '.substr($parsedData['body_plain'], 0,150);
            $email = ProjectEmail::store($parsedData);
            \Log::info('Email stored successfully!');

            $recipients = explode(',', $request->input('recipient'));
            foreach ($recipients as $recipient) {
                $parsedRecipient = $this->parseRecipientAddress($recipient);
                Log::info($parsedRecipient);
                if ($parsedRecipient) {
                    Log::info("inside parsedrecipient");

                    $project = Project::where('name', 'like','%'.$parsedRecipient['projectName'].'%')
                        ->first();
                        Log::info($project);

                    if ($project) {
                        Log::info("sending");
                        $to = $parsedRecipient['number'];
                        if($this->sendMessage($project->name, $project->api_token, $parsedRecipient['sender'], $to, $message, 'email-sms-forward', 'sms')) {
                            Log::info("Message sent successfully!");
                        } else {
                            Log::info("Error sending message!");
                        }
                        $email->forwarded = true;
                        $email->project_id = $project->id;
                        $email->save();
                    }
                }
            }

        } catch (\Exception $e) {
            return response()->json(['message' => 'Error storing email!'], 406);
        }
        return response()->json(['message' => 'Email stored successfully!'], 200);
    }

    public function sendMessage($projectName, $token, $from, $to, $message, $campaign, $channel)
    {
        Log::info($projectName." ". $token." ". $from." ".$to." ". $message." ". $campaign." ". $channel);
        $client = new Client();

        try {
            Log::warning("Sending message: " . $message);
            $response = $client->request('POST', "https://sozuri.net/api/v1/messaging", [
                 'headers' => [
                     'Content-Type' => 'application/json',
                     'Accept' => 'application/json',
                     'authorization' => 'Bearer ' . $token,
                 ],
                 'allow_redirects' => [
                    'max' => 10
                 ],
                 'json' => ['project' => $projectName, 'from' => $from, 'to' => $to, 'campaign' => $campaign, 'channel' => $channel, 'message' => $message, 'type' => 'promotional']
             ]);
             $body = (string) $response->getBody();
    
             // Log the response body
             Log::info("Response body: " . $body);
    
             
             Log::info('response log above');
             if ($response->getStatusCode() >= 300 && $response->getStatusCode() < 400) {
                Log::info("Redirected to: " . $response->getHeader('Location')[0]);
            }

            
             return true;
        } catch (Exception $e) {
            Log::warning("Error while sending message: " . $e->getMessage());
            return false;
        }

    }

    public static function parseRecipientAddress(string $recipient): ?array
    {
        // Define the pattern to match the <NAME_EMAIL> 
        $pattern = '/^(\d{12})-(.+?)-(.+?)@([a-zA-Z0-9.-]+)$/';

        // Check if the recipient matches the pattern
        if (preg_match($pattern, $recipient, $matches)) {
            // Extract the components
            $number = $matches[1];
            $projectName = $matches[2];
            $sender = $matches[3];
            $domain = $matches[4];

            // Return the extracted components
            return [
                'number' => $number,
                'projectName' => $projectName,
                'sender' => $sender,
                'domain' => $domain,
            ];
        }

        // Return null if the recipient doesn't match the pattern
        return null;
    }

}
