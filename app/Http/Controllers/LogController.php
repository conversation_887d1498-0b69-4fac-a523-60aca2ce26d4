<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;
use App\Sms;
use App\Models\SmsCopy;
use App\Models\SmsMonthlyCopy;
use App\Models\InteractiveSms;
use App\Jobs\ProcessSendsafaricombulk;
use App\Jobs\ProcessSendtelkombulk;
use App\Jobs\ProcessSendairtelbulk;
use App\Models\LogDownload;
use App\Jobs\ExportSmsLogJob;
use App\Contact;
use App\ContactList;
use App\Deposit;
use App\Campaign;
use App\Alphanumeric;
use App\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Imports\ContactsImport;
use Illuminate\Database\Eloquent\Builder;
use App\Enrollment;
use GuzzleHttp\Client;
use App\Payment;
use PDO;
use PDOException;
use App\User;


class LogController extends Controller
{

	public function __construct()
	{
		//   $this->authorizeResource(Sms::class, 'sms');
	}

	public function campaignLog(Request $request)
	{
		Log::info('test history log');
		$this->authorize('viewAny', [Sms::class, $request->id]);
		ini_set('memory_limit', '-1');

		$project = Project::find($request->id);
		// $this->authorizeResource(Sms::class, 'sms');
		$campaigns = Campaign::pluck('name', 'id')->toArray();

		// DB table to use
		$table = 'interactive_sms';
		// Table's primary key
		$primaryKey = 'id';
		// Array of database columns which should be read and sent back to DataTables.
		// The `db` parameter represents the column name in the database, while the `dt`
		// parameter represents the DataTables column identifier. In this case simple
		// indexes

		$columns = array(
			array('db' => 'created_at',  'dt' => 0),
			array('db' => 'from', 'dt' => 1),
			array('db' => 'to', 'dt' => 2),
			array('db' => 'message', 'dt' => 3),
			array('db' => 'price', 'dt' => 4),
			array('db' => 'status', 'dt' => 5),
			
			array('db' => 'description', 'dt' => 6),
			array('db' => 'campaign_name', 'dt' => 7),
			array('db' => 'telco', 'dt' => 8),
			array('db' => 'message_id', 'dt' => 9),
			array('db' => 'updated_at', 'dt' => 10),
			array('db' => 'status_code', 'dt' => 11),

			

		);
		// SQL server connection information
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		$whereAll = "project_id = " . $project->id; //<--- WORKING

		$search_date_from = $request->get('search_date_from');
		$search_date_to = $request->get('search_date_to');
		$search_from = $request->get('search_from');
		$search_to = $request->get('search_to');
		$search_message = $request->get('search_message');
		$search_cost = $request->get('search_cost');
		$search_status = $request->get('search_status');
		$search_reason = $request->get('search_reason');
		$search_campaign_name =  $request->get('search_campaign_name');
		// Campaign::where(['project_id' => $project->id, 'name' =>$request->get('search_reason')])->exists() ?
		//Campaign::where(['project_id' => $project->id, 'name' =>$request->get('search_reason')])->value('id') : null ;
		$search_network = $request->get('search_network');
		$search_message_id = $request->get('search_message_id');
		$search_delivered = $request->get('search_delivered');

		$searchConditions = [];  // To store all dynamic search conditions


		// Check for 'From Date' and 'To Date'
		if ($search_date_from && $search_date_to) {
			$searchConditions[] = "created_at BETWEEN '" . $search_date_from . " 00:00:00' AND '" . $search_date_to . " 23:59:59'";
		} elseif ($search_date_from) {
			$searchConditions[] = "created_at >= '" . $search_date_from . " 00:00:00'";
		} elseif ($search_date_to) {
			$searchConditions[] = "created_at <= '" . $search_date_to . " 23:59:59'";
		}
		if ($search_from) {
			$searchConditions[] = "from LIKE '%" . $search_from . "%'";
		}
		if ($search_to) {
			$searchConditions[] = "to LIKE '%" . $search_to . "%'";
		}
		if ($search_message) {
			$searchConditions[] = "message LIKE '%" . $search_message . "%'";
		}
		if ($search_cost) {
			$searchConditions[] = "cost = " . $search_cost;
		}
		if ($search_status) {
			$searchConditions[] = "status LIKE '%" . $search_status . "%'";
		}
		if ($search_reason) {
			$searchConditions[] = "campaign_id LIKE '%" . $search_reason . "%'";
		}
		
		if ($search_campaign_name) {
			$searchConditions[] = "campaign_name LIKE '%" . $search_campaign_name . "%'";
		}
		if ($search_network) {
			$searchConditions[] = "network LIKE '%" . $search_network . "%'";
		}
		if ($search_message_id) {
			$searchConditions[] = "message_id LIKE '%" . $search_message_id . "%'";
		}
		if ($search_delivered) {
			$searchConditions[] = "DATE(delivered_at) = '" . $search_delivered . "'";
		}

		// If any search conditions are added, append them to the base whereAll
		if (!empty($searchConditions)) {
			$whereAll .= " AND " . implode(" AND ", $searchConditions);
		}

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)

		);

	}

	public function downloadCampaignLog(Request $request, $projectId) {
		Log::info($request->all());
		ini_set('memory_limit', '18G');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        ini_set('upload_max_filesize', '64M');
        ini_set('post_max_size', '64M');
		// Retrieve search parameters (same as above)
		$search_date_from = $request->get('search_date_from');
		$search_date_to = $request->get('search_date_to');
		$search_from = $request->get('search_from');
		$search_to = $request->get('search_to');
		$search_message = $request->get('search_message');
		$search_cost = $request->get('search_cost');
		$search_status = $request->get('search_status');
		$search_reason = $request->get('search_reason');
		$search_campaign_name =  $request->get('search_campaign_name');

		//$search_reason = Campaign::where(['project_id' => $projectId, 'name' =>$request->get('search_reason')])->exists() ?
		//Campaign::where(['project_id' => $projectId, 'name' =>$request->get('search_reason')])->value('id') : null ;

		$search_network = $request->get('search_network');
		$search_message_id = $request->get('search_message_id');
		$search_delivered = $request->get('search_delivered');

		// Build query with the same filters for sms copy
		$querySmsCopy = InteractiveSms::where('project_id', $projectId);

		// Efficiently filter by date range
		/*if ($search_date_from && $search_date_to) {
			$querySmsCopy->whereBetween('created_at', [$search_date_from, $search_date_to]);
		} elseif ($search_date_from) {
			$querySmsCopy->whereDate('created_at', '>=', $search_date_from);
		} elseif ($search_date_to) {
			$querySmsCopy->whereDate('created_at', '<=', $search_date_to);
		}*/

		if ($search_date_from && $search_date_to) {
			$querySmsCopy->whereBetween('created_at', [
				$search_date_from,
				Carbon::parse($search_date_to)->endOfDay() // Includes the entire day
			]);
		} elseif ($search_date_from) {
			$querySmsCopy->whereDate('created_at', '>=', $search_date_from);
		} elseif ($search_date_to) {
			$querySmsCopy->whereDate('created_at', '<=', Carbon::parse($search_date_to)->endOfDay());
		}

		if ($search_from) {
			$querySmsCopy->where('from', 'like', '%' . $search_from . '%');
		}
		if ($search_to) {
			$querySmsCopy->where('to', 'like', '%' . $search_to . '%');
		}
		if ($search_message) {
			$querySmsCopy->where('message', 'like', '%' . $search_message . '%');
		}
		if ($search_cost) {
			$querySmsCopy->where('cost', $search_cost);
		}
		if ($search_status) {
			$querySmsCopy->where('status', 'like', '%' . $search_status . '%');
		}
		if ($search_reason) {
			$querySmsCopy->where('description', 'like', '%' . $search_reason . '%');
		}
		
		if ($search_campaign_name) {
			$querySmsCopy->where("campaign_name", 'like', '%' . $search_campaign_name . "%");
		}
		if ($search_network) {
			$querySmsCopy->where('network', 'like', '%' . $search_network . '%');
		}
		if ($search_message_id) {
			$querySmsCopy->where('message_id', 'like', '%' . $search_message_id . '%');
		}
		if ($search_delivered) {
			$querySmsCopy->whereDate('delivered_at', $search_delivered);
		}
		
		// Fetch filtered data
			//$logs = $querySmsCopy->union($querySms)->get();

		$logs = $querySmsCopy->get();
		$campaigns = Campaign::pluck('name', 'id')->toArray();


		// Generate CSV from filtered data
		$csvData = "Date,From,To,Message,Cost,Status,Campaign,Network,Message ID,Delivered @\n";
		foreach ($logs as $log) {
			$campaignName = isset($campaigns[$log->campaign_id]) ? $campaigns[$log->campaign_id] : 'none';
			$cleanMessage = str_replace(',', '', $log->message);
			$csvData .= "{$log->created_at},{$log->from},{$log->to},{$cleanMessage},{$log->price},{$log->status},{$campaignName},{$log->telco},{$log->message_id},{$log->updated_at}\n";
		}
	
		// Return CSV response
		return response($csvData)
			->header('Content-Type', 'text/csv')
			->header('Content-Disposition', 'attachment; filename="filtered_data.csv"');
	}



	public function renderMonthlySms($id)
	{
		ini_set('memory_limit', '-1');
		$this->authorize('viewAny', [Sms::class, $id]);
		$project = Project::find($id);
		// $smses = SMS::where('project_id', $id)->latest()->get();
		// return view('sms.index', compact('smses', 'project'));
		return view('sms.monthly', compact('project'));
	}

	public function logMonthly(Request $request)
	{
		Log::info('test history log');
		$this->authorize('viewAny', [Sms::class, $request->id]);
		ini_set('memory_limit', '-1');

		$project = Project::find($request->id);
		// $this->authorizeResource(Sms::class, 'sms');

		// DB table to use
		$table = 'sms_monthly_copy';
		// Table's primary key
		$primaryKey = 'id';
		// Array of database columns which should be read and sent back to DataTables.
		// The `db` parameter represents the column name in the database, while the `dt`
		// parameter represents the DataTables column identifier. In this case simple
		// indexes
		$columns = array(
			array('db' => 'created_at',  'dt' => 0),
			array('db' => 'from', 'dt' => 1),
			array('db' => 'to', 'dt' => 2),
			array('db' => 'message', 'dt' => 3),
			array('db' => 'price', 'dt' => 4),
			array('db' => 'status', 'dt' => 5),
			array('db' => 'description', 'dt' => 6),
			array('db' => 'campaign_name', 'dt' => 7),
			array('db' => 'telco', 'dt' => 8),
			array('db' => 'message_id', 'dt' => 9),
			array('db' => 'updated_at', 'dt' => 10),
			array('db' => 'status_code', 'dt' => 11),

		);
		// SQL server connection information
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		$whereAll = "project_id = " . $project->id; //<--- WORKING

		$search_date_from = $request->get('search_date_from');
		$search_date_to = $request->get('search_date_to');
		$search_from = $request->get('search_from');
		$search_to = $request->get('search_to');
		$search_message = $request->get('search_message');
		$search_cost = $request->get('search_cost');
		$search_status = $request->get('search_status');
		$search_reason = $request->get('search_reason');
		$search_campaign_name =  $request->get('search_campaign_name');
		$search_network = $request->get('search_network');
		$search_message_id = $request->get('search_message_id');
		$search_delivered = $request->get('search_delivered');

		$searchConditions = [];  // To store all dynamic search conditions


		// Check for 'From Date' and 'To Date'
		if ($search_date_from && $search_date_to) {
			$searchConditions[] = "created_at BETWEEN '" . $search_date_from . " 00:00:00' AND '" . $search_date_to . " 23:59:59'";
		} elseif ($search_date_from) {
			$searchConditions[] = "created_at >= '" . $search_date_from . " 00:00:00'";
		} elseif ($search_date_to) {
			$searchConditions[] = "created_at <= '" . $search_date_to . " 23:59:59'";
		}
		if ($search_from) {
			$searchConditions[] = "from LIKE '%" . $search_from . "%'";
		}
		if ($search_to) {
			$searchConditions[] = "to LIKE '%" . $search_to . "%'";
		}
		if ($search_message) {
			$searchConditions[] = "message LIKE '%" . $search_message . "%'";
		}
		if ($search_cost) {
			$searchConditions[] = "cost = " . $search_cost;
		}
		if ($search_status) {
			$searchConditions[] = "status LIKE '%" . $search_status . "%'";
		}
		if ($search_reason) {
			$searchConditions[] = "description LIKE '%" . $search_reason . "%'";
		}
		if ($search_campaign_name) {
			$searchConditions[] = "campaign_name LIKE '%" . $search_campaign_name . "%'";
		}
		if ($search_network) {
			$searchConditions[] = "network LIKE '%" . $search_network . "%'";
		}
		if ($search_message_id) {
			$searchConditions[] = "message_id LIKE '%" . $search_message_id . "%'";
		}
		if ($search_delivered) {
			$searchConditions[] = "DATE(delivered_at) = '" . $search_delivered . "'";
		}

		// If any search conditions are added, append them to the base whereAll
		if (!empty($searchConditions)) {
			$whereAll .= " AND " . implode(" AND ", $searchConditions);
		}

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)

		);

	}
	public function downloadSmsLogMonthly(Request $request, $projectId) {
		
		Log::info($request->all());
			ini_set('memory_limit', '18G');
			// ini_set('max_input_time', '1800');
			ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
			//set_time_limit(800);//same as above
			ini_set('default_socket_timeout', '-1');
			ini_set('upload_max_filesize', '64M');
			ini_set('post_max_size', '64M');
			// Retrieve search parameters (same as above)
			$search_date_from = $request->get('search_date_from');
			$search_date_to = $request->get('search_date_to');
			$search_from = $request->get('search_from');
			$search_to = $request->get('search_to');
			$search_message = $request->get('search_message');
			$search_cost = $request->get('search_cost');
			$search_status = $request->get('search_status');
			$search_reason = $request->get('search_reason');
			$search_campaign_name =  $request->get('search_campaign_name');
			$search_network = $request->get('search_network');
			$search_message_id = $request->get('search_message_id');
			$search_delivered = $request->get('search_delivered');

			// Build query with the same filters for sms copy
			$querySmsCopy = SmsMonthlyCopy::where('project_id', $projectId);

			// Efficiently filter by date range
			/*
			if ($search_date_from && $search_date_to) {
				$querySmsCopy->whereBetween('created_at', [$search_date_from, $search_date_to]);
			} elseif ($search_date_from) {
				$querySmsCopy->whereDate('created_at', '>=', $search_date_from);
			} elseif ($search_date_to) {
				$querySmsCopy->whereDate('created_at', '<=', $search_date_to);
			}
			*/

			if ($search_date_from && $search_date_to) {
				$querySmsCopy->whereBetween('created_at', [
					$search_date_from,
					Carbon::parse($search_date_to)->endOfDay() // Includes the entire day
				]);
			} elseif ($search_date_from) {
				$querySmsCopy->whereDate('created_at', '>=', $search_date_from);
			} elseif ($search_date_to) {
				$querySmsCopy->whereDate('created_at', '<=', Carbon::parse($search_date_to)->endOfDay());
			}


			if ($search_from) {
				$querySmsCopy->where('from', 'like', '%' . $search_from . '%');
			}
			if ($search_to) {
				$querySmsCopy->where('to', 'like', '%' . $search_to . '%');
			}
			if ($search_message) {
				$querySmsCopy->where('message', 'like', '%' . $search_message . '%');
			}
			if ($search_cost) {
				$querySmsCopy->where('cost', $search_cost);
			}
			if ($search_status) {
				$querySmsCopy->where('status', 'like', '%' . $search_status . '%');
			}
			if ($search_reason) {
				$querySmsCopy->where('description', 'like', '%' . $search_reason . '%');
			}
			if ($search_campaign_name) {
				$querySmsCopy->where('campaign_name', 'like', '%' . $search_campaign_name . '%');
			}
			if ($search_network) {
				$querySmsCopy->where('network', 'like', '%' . $search_network . '%');
			}
			if ($search_message_id) {
				$querySmsCopy->where('message_id', 'like', '%' . $search_message_id . '%');
			}
			if ($search_delivered) {
				$querySmsCopy->whereDate('delivered_at', $search_delivered);
			}
			

			// Build query with the same filters for sms daily
			$querySms = Sms::where('project_id', $projectId);

			if ($search_date_from && $search_date_to) {
				$querySms->whereBetween('created_at', [$search_date_from, $search_date_to]);
			} elseif ($search_date_from) {
				$querySms->whereDate('created_at', '>=', $search_date_from);
			} elseif ($search_date_to) {
				$querySms->whereDate('created_at', '<=', $search_date_to);
			}
			if ($search_from) {
				$querySms->where('from', 'like', '%' . $search_from . '%');
			}
			if ($search_to) {
				$querySms->where('to', 'like', '%' . $search_to . '%');
			}
			if ($search_message) {
				$querySms->where('message', 'like', '%' . $search_message . '%');
			}
			if ($search_cost) {
				$querySms->where('cost', $search_cost);
			}
			if ($search_status) {
				$querySms->where('status', 'like', '%' . $search_status . '%');
			}
			if ($search_reason) {
				$querySms->where('description', 'like', '%' . $search_reason . '%');
			}
			if ($search_campaign_name) {
				$querySms->where('campaign_name', 'like', '%' . $search_campaign_name . '%');
			}
			if ($search_network) {
				$querySms->where('network', 'like', '%' . $search_network . '%');
			}
			if ($search_message_id) {
				$querySms->where('message_id', 'like', '%' . $search_message_id . '%');
			}
			if ($search_delivered) {
				$querySms->whereDate('delivered_at', $search_delivered);
			}
			
			// Fetch filtered data
			//$logs = $query->get();

		$logs = $querySmsCopy->union($querySms)->get();

			// Generate CSV from filtered data

			$csvData = "Date,From,To,Message,Cost,Status,Reason,Campaign,Network,Message ID,Delivered @\n";

			foreach ($logs as $log) {
				// Replace newlines and escape double quotes in the message
				$cleanMessage = str_replace(["\r", "\n"], ' ', $log->message); // Replace newlines with spaces
				$cleanMessage = str_replace('"', '""', $cleanMessage); // Escape quotes by doubling them
				
				// Safely handle fields by wrapping them in double quotes if necessary
				$csvData .= "\"{$log->created_at}\",\"{$log->from}\",\"{$log->to}\",\"{$cleanMessage}\",\"{$log->price}\",\"{$log->status}\",\"{$log->description}\",\"{$log->campaign_name}\",\"{$log->telco}\",\"{$log->message_id}\",\"{$log->updated_at}\"\n";
			}
		
			// Return CSV response
			return response($csvData)
				->header('Content-Type', 'text/csv')
				->header('Content-Disposition', 'attachment; filename="filtered_monthly_data.csv"');
	}



	public function renderDailySms(Request $request, $pid) {
		ini_set('memory_limit', '-1');

		$this->authorize('viewAny', [Sms::class, $pid]);
		$project = Project::find($pid);
		return view('sms.daily', compact('project'));
	}
	public function logDaily(Request $request)
	{
		Log::info('test history log');
		$this->authorize('viewAny', [Sms::class, $request->id]);
		ini_set('memory_limit', '-1');

		$project = Project::find($request->id);
		// $this->authorizeResource(Sms::class, 'sms');

		// DB table to use
		$table = 'sms';
		// Table's primary key
		$primaryKey = 'id';
		// Array of database columns which should be read and sent back to DataTables.
		// The `db` parameter represents the column name in the database, while the `dt`
		// parameter represents the DataTables column identifier. In this case simple
		// indexes
		$columns = array(
			array('db' => 'created_at',  'dt' => 0),
			array('db' => 'from', 'dt' => 1),
			array('db' => 'to', 'dt' => 2),
			array('db' => 'message', 'dt' => 3),
			array('db' => 'price', 'dt' => 4),
			array('db' => 'status', 'dt' => 5),
			array('db' => 'description', 'dt' => 6),
			array('db' => 'campaign_name', 'dt' => 7),
			array('db' => 'telco', 'dt' => 8),
			array('db' => 'message_id', 'dt' => 9),
			array('db' => 'updated_at', 'dt' => 10),
			array('db' => 'status_code', 'dt' => 11),

		);
		// SQL server connection information
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		$whereAll = "project_id = " . $project->id; //<--- WORKING

		$search_date_from = $request->get('search_date_from');
		$search_date_to = $request->get('search_date_to');
		$search_from = $request->get('search_from');
		$search_to = $request->get('search_to');
		$search_message = $request->get('search_message');
		$search_cost = $request->get('search_cost');
		$search_status = $request->get('search_status');
		$search_reason = $request->get('search_reason');
		$search_campaign_name =  $request->get('search_campaign_name');
		$search_network = $request->get('search_network');
		$search_message_id = $request->get('search_message_id');
		$search_delivered = $request->get('search_delivered');

		$searchConditions = [];  // To store all dynamic search conditions


		// Check for 'From Date' and 'To Date'
		if ($search_date_from && $search_date_to) {
			$searchConditions[] = "created_at BETWEEN '" . $search_date_from . " 00:00:00' AND '" . $search_date_to . " 23:59:59'";
		} elseif ($search_date_from) {
			$searchConditions[] = "created_at >= '" . $search_date_from . " 00:00:00'";
		} elseif ($search_date_to) {
			$searchConditions[] = "created_at <= '" . $search_date_to . " 23:59:59'";
		}
		if ($search_from) {
			$searchConditions[] = "from LIKE '%" . $search_from . "%'";
		}
		if ($search_to) {
			$searchConditions[] = "to LIKE '%" . $search_to . "%'";
		}
		if ($search_message) {
			$searchConditions[] = "message LIKE '%" . $search_message . "%'";
		}
		if ($search_cost) {
			$searchConditions[] = "cost = " . $search_cost;
		}
		if ($search_status) {
			$searchConditions[] = "status LIKE '%" . $search_status . "%'";
		}
		if ($search_reason) {
			$searchConditions[] = "reason LIKE '%" . $search_reason . "%'";
		}
		if ($search_campaign_name) {
			$searchConditions[] = "campaign_name LIKE '%" . $search_campaign_name . "%'";
		}
		if ($search_network) {
			$searchConditions[] = "network LIKE '%" . $search_network . "%'";
		}
		if ($search_message_id) {
			$searchConditions[] = "message_id LIKE '%" . $search_message_id . "%'";
		}
		if ($search_delivered) {
			$searchConditions[] = "DATE(delivered_at) = '" . $search_delivered . "'";
		}

		// If any search conditions are added, append them to the base whereAll
		if (!empty($searchConditions)) {
			$whereAll .= " AND " . implode(" AND ", $searchConditions);
		}

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)

		);

	}
	public function downloadDailySms(Request $request, $projectId) {
		
		Log::info($request->all());
			ini_set('memory_limit', '18G');
			// ini_set('max_input_time', '1800');
			ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
			//set_time_limit(800);//same as above
			ini_set('default_socket_timeout', '-1');
			ini_set('upload_max_filesize', '64M');
			ini_set('post_max_size', '64M');
			// Retrieve search parameters (same as above)
			$search_date_from = $request->get('search_date_from');
			$search_date_to = $request->get('search_date_to');
			$search_from = $request->get('search_from');
			$search_to = $request->get('search_to');
			$search_message = $request->get('search_message');
			$search_cost = $request->get('search_cost');
			$search_status = $request->get('search_status');
			$search_reason = $request->get('search_reason');
			$search_campaign_name =  $request->get('search_campaign_name');
			$search_network = $request->get('search_network');
			$search_message_id = $request->get('search_message_id');
			$search_delivered = $request->get('search_delivered');

			// Build query with the same filters for sms copy
			$querySmsCopy = Sms::where('project_id', $projectId);

			// Efficiently filter by date range
			/*
			if ($search_date_from && $search_date_to) {
				$querySmsCopy->whereBetween('created_at', [$search_date_from, $search_date_to]);
			} elseif ($search_date_from) {
				$querySmsCopy->whereDate('created_at', '>=', $search_date_from);
			} elseif ($search_date_to) {
				$querySmsCopy->whereDate('created_at', '<=', $search_date_to);
			}
			*/
			if ($search_from) {
				$querySmsCopy->where('from', 'like', '%' . $search_from . '%');
			}
			if ($search_to) {
				$querySmsCopy->where('to', 'like', '%' . $search_to . '%');
			}
			if ($search_message) {
				$querySmsCopy->where('message', 'like', '%' . $search_message . '%');
			}
			if ($search_cost) {
				$querySmsCopy->where('cost', $search_cost);
			}
			if ($search_status) {
				$querySmsCopy->where('status', 'like', '%' . $search_status . '%');
			}
			if ($search_reason) {
				$querySmsCopy->where('description', 'like', '%' . $search_reason . '%');
			}
			if($search_campaign_name) {
				$querySmsCopy->where('campaign_name', 'like', '%' . $search_campaign_name . '%');
			}
			if ($search_network) {
				$querySmsCopy->where('network', 'like', '%' . $search_network . '%');
			}
			if ($search_message_id) {
				$querySmsCopy->where('message_id', 'like', '%' . $search_message_id . '%');
			}
			if ($search_delivered) {
				$querySmsCopy->whereDate('delivered_at', $search_delivered);
			}
			

			// Fetch filtered data
			//$logs = $query->get();

		$logs = $querySmsCopy->get();

			// Generate CSV from filtered data
			$csvData = "Date,From,To,Message,Cost,Status,Reason,Campaign,Network,Message ID,Delivered @\n";

			foreach ($logs as $log) {
				// Replace newlines and escape double quotes in the message
				$cleanMessage = str_replace(["\r", "\n"], ' ', $log->message); // Replace newlines with spaces
				$cleanMessage = str_replace('"', '""', $cleanMessage); // Escape quotes by doubling them
				
				// Safely handle fields by wrapping them in double quotes if necessary
				$csvData .= "\"{$log->created_at}\",\"{$log->from}\",\"{$log->to}\",\"{$cleanMessage}\",\"{$log->price}\",\"{$log->status}\",\"{$log->description}\",\"{$log->campaign_name}\",\"{$log->telco}\",\"{$log->message_id}\",\"{$log->updated_at}\"\n";
			}
		
			// Return CSV response
			return response($csvData)
				->header('Content-Type', 'text/csv')
				->header('Content-Disposition', 'attachment; filename="filtered_data.csv"');
	}


	public function logHistory(Request $request)
	{
		Log::info('test history log');
		$this->authorize('viewAny', [Sms::class, $request->id]);
		ini_set('memory_limit', '-1');

		$project = Project::find($request->id);
		// $this->authorizeResource(Sms::class, 'sms');

		// DB table to use
		$table = 'sms_copy';
		// Table's primary key
		$primaryKey = 'id';
		// Array of database columns which should be read and sent back to DataTables.
		// The `db` parameter represents the column name in the database, while the `dt`
		// parameter represents the DataTables column identifier. In this case simple
		// indexes
		$columns = array(
			array('db' => 'created_at',  'dt' => 0),
			array('db' => 'from', 'dt' => 1),
			array('db' => 'to', 'dt' => 2),
			array('db' => 'message', 'dt' => 3),
			array('db' => 'price', 'dt' => 4),
			array('db' => 'status', 'dt' => 5),
			array('db' => 'description', 'dt' => 6),
			array('db' => 'telco', 'dt' => 7),
			array('db' => 'message_id', 'dt' => 8),
			array('db' => 'updated_at', 'dt' => 9),
			array('db' => 'status_code', 'dt' => 10),
			array('db' => 'campaign_name', 'dt' => 11),

		);
		// SQL server connection information
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		$whereAll = "project_id = " . $project->id; //<--- WORKING

		$search_date_from = $request->get('search_date_from');
		$search_date_to = $request->get('search_date_to');
		$search_from = $request->get('search_from');
		$search_to = $request->get('search_to');
		$search_message = $request->get('search_message');
		$search_cost = $request->get('search_cost');
		$search_status = $request->get('search_status');
		$search_reason = $request->get('search_reason');
		$search_campaign_name =  $request->get('search_campaign_name');
		$search_network = $request->get('search_network');
		$search_message_id = $request->get('search_message_id');
		$search_delivered = $request->get('search_delivered');

		$searchConditions = [];  // To store all dynamic search conditions


		// Check for 'From Date' and 'To Date'
		if ($search_date_from && $search_date_to) {
			$searchConditions[] = "created_at BETWEEN '" . $search_date_from . " 00:00:00' AND '" . $search_date_to . " 23:59:59'";
		} elseif ($search_date_from) {
			$searchConditions[] = "created_at >= '" . $search_date_from . " 00:00:00'";
		} elseif ($search_date_to) {
			$searchConditions[] = "created_at <= '" . $search_date_to . " 23:59:59'";
		}
		if ($search_from) {
			$searchConditions[] = "from LIKE '%" . $search_from . "%'";
		}
		if ($search_to) {
			$searchConditions[] = "to LIKE '%" . $search_to . "%'";
		}
		if ($search_message) {
			$searchConditions[] = "message LIKE '%" . $search_message . "%'";
		}
		if ($search_cost) {
			$searchConditions[] = "cost = " . $search_cost;
		}
		if ($search_status) {
			$searchConditions[] = "status LIKE '%" . $search_status . "%'";
		}
		if ($search_reason) {
			$searchConditions[] = "description LIKE '%" . $search_reason . "%'";
		}
		if ($search_campaign_name) {
			$searchConditions[] = "campaign_name LIKE '%" . $search_campaign_name . "%'";
		}
		if ($search_network) {
			$searchConditions[] = "network LIKE '%" . $search_network . "%'";
		}
		if ($search_message_id) {
			$searchConditions[] = "message_id LIKE '%" . $search_message_id . "%'";
		}
		if ($search_delivered) {
			$searchConditions[] = "DATE(delivered_at) = '" . $search_delivered . "'";
		}

		// If any search conditions are added, append them to the base whereAll
		if (!empty($searchConditions)) {
			$whereAll .= " AND " . implode(" AND ", $searchConditions);
		}

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)

		);
	}
	
	public function downloadSmsLogHistory(Request $request, $projectId) {
		Log::info($request->all());
		ini_set('memory_limit', '18G');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        ini_set('upload_max_filesize', '64M');
        ini_set('post_max_size', '64M');	
		
		$filters = [
			'search_date_from' => $request->get('search_date_from'),
			'search_date_to' => $request->get('search_date_to'),
			'search_from' => $request->get('search_from'),
			'search_to' => $request->get('search_to'),
			'search_message' => $request->get('search_message'),
			'search_cost' => $request->get('search_cost'),
			'search_status' => $request->get('search_status'),
			'search_reason' => $request->get('search_reason'),
			'search_campaign_name' => $request->get('search_campaign_name'),
			'search_network' => $request->get('search_network'),
			'search_message_id' => $request->get('search_message_id'),
			'search_delivered' => $request->get('search_delivered'),
		];
		$user = User::where('id', Auth::user()->id)->first();
		
		ExportSmsLogJob::dispatch($projectId, $user, $filters ); // Add any filters you need to pass
		return response()->json(['message' => 'Export started. You will be notified when the file is ready.']);

	}
	public function downloadHistoryLogFile($pId, $logDownloadId)
    {
		$logDownload = LogDownload::findOrFail($logDownloadId);
		if ($logDownload->project_id != $pId) {	
			abort(403, 'Unauthorized action.');
		}



        $filePath = storage_path("app/{$logDownload->file_path}");

        if (!file_exists($filePath)) {
            abort(404, 'File not found.');
        }

        return response()->download($filePath, $logDownload->file_name);
    }
	public function deleteHistoryLogFile($pId, $logDownloadId)
	{
		Log::info('test delete log file');
		$logDownload = LogDownload::findOrFail($logDownloadId);
		Log::info($logDownload);
		if ($logDownload->project_id != $pId) {	
			Log::info('Unmatching project.');
			abort(403, 'Unauthorized action.');
		}

		$filePath = storage_path("app/{$logDownload->file_path}");
	try {
		if (file_exists($filePath)) {
		Log::info('Checking file permissions.');
				if (!is_writable($filePath)) {
					Log::info('File is not writable. Attempting to change permissions.');
					chmod($filePath, 0777);
				}
			Log::info('Deleting existing file.');
			unlink($filePath);
			Log::info('File deleted successfully.');
		}
		$logDownload->delete();
		return response()->json(['message' => 'File deleted successfully.'], 200);
	} catch (\Exception $e) {
		Log::error('Error deleting file: ' . $e->getMessage());
		return response()->json(['message' => 'Error deleting file.'], 500);
	}
	}

	

	public function loadSchedule(Request $request)
	{
		//$this->authorize('viewAny', [Sms::class, $request->id]);
		ini_set('memory_limit', '-1');
		Log::info('trying');
		$project = Project::find($request->id);
		// $this->authorizeResource(Sms::class, 'sms');

		$table = 'scheduledsms';
		$primaryKey = 'id';
		$columns = array(
			array('db' => 'created_at',  'dt' => 0),
			array('db' => 'from', 'dt' => 1),
			array('db' => 'to', 'dt' => 2),
			array('db' => 'message', 'dt' => 3),
			array('db' => 'price', 'dt' => 4),
			array('db' => 'status', 'dt' => 5),
			array('db' => 'description', 'dt' => 6),
			array('db' => 'telco', 'dt' => 7),
			array('db' => 'message_id', 'dt' => 8),
			array('db' => 'send_at', 'dt' => 9),
			array('db' => 'status_code', 'dt' => 10),
			array('db' => 'campaign_name', 'dt' => 11),

		);
		// SQL server connection information
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
		* If you just want to use the basic configuration for DataTables with PHP
		* server-side, there is no need to edit below this line.
		*/
		$whereAll = "project_id = " . $project->id; //<--- WORKING

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)

		);
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */

	public function contacts(Request $request)
	{
		$this->authorize('viewAny', [Contact::class, $request->id]);
		ini_set('memory_limit', '-1');
		$project = Project::find($request->id);
		$table = 'contacts';
		$primaryKey = 'id';
		$columns = array(
			array('db' => 'fname',  'dt' => 0),
			array('db' => 'mobile', 'dt' => 1),
			array('db' => 'created_at', 'dt' => 2),
			array('db' => 'email', 'dt' => 3),
			array('db' => 'company', 'dt' => 4),
			array('db' => 'city', 'dt' => 5),
            array('db' => 'tag', 'dt' => 6,'formatter' => function ($d, $row) { return ContactList::find($d)->name ?? ""; }),
			array('db' => 'id', 'dt' => 7),
		);
		// SQL server connection information
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		$whereAll = "project_id = " . $project->id; //<--- WORKING

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)

		);
	}

	public function contactslists($pid, $listid)
	{
		$contactList = ContactList::find($listid);
		$project = Project::find($pid);
		$table = 'contacts';
		$primaryKey = 'id';
		$columns = array(
			array('db' => 'fname',  'dt' => 0),
			array('db' => 'mobile', 'dt' => 1),
			array('db' => 'created_at', 'dt' => 2),
			array('db' => 'email', 'dt' => 3),
			array('db' => 'job', 'dt' => 4),
			array('db' => 'company', 'dt' => 5),
			array('db' => 'city', 'dt' => 6),
            array('db' => 'tag', 'dt' => 7),
			array('db' => 'id', 'dt' => 8),
		);
		$sql_details = array(
			'user' =>  config('app.db_username'),
			'pass' => config('app.db_password'),
			'db'   => config('app.db_database'),
			'host' => config('app.db_host'),
		);

		$whereAll = "tag = " . $contactList->id; //<--- WORKING

		echo json_encode(
			SSP::complex($_GET, $sql_details, $table, $primaryKey, $columns, $whereResult = null, $whereAll)
		);
	}

	public function renderHistorySms($id)
	{
		ini_set('memory_limit', '-1');

		$this->authorize('viewAny', [Sms::class, $id]);
		$project = Project::find($id);
		// $smses = SMS::where('project_id', $id)->latest()->get();
		// return view('sms.index', compact('smses', 'project'));
		return view('sms.history', compact('project'));
	}
	public function search(Request $request, $id)
	{
		$arr = explode(' ', $request->between);
		$project = Project::find($id);
		$this->authorize('viewAny', [Sms::class, $id]);
		if ($arr[0] == "") { //empty form submitted
			$smses = Sms::where('project_id', $project->id)->latest()->get();
			return view('sms.index', compact('smses', 'project'));
		}
		$from  = $arr[0];
		$to = $arr[2];
		$smses = Sms::whereDate('created_at', '>=', $from)
			->whereDate('created_at', '<=', $to)
			// whereBetween('created_at', [Carbon::parse($to), Carbon::parse($from) ] )
			->where('project_id', $id)
			->latest()->get();
		return view('sms.index', compact('smses', 'project'));
	}

	/**
	 * Show the form for creating a new resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function create($id)
	{
		$project = Project::find($id);
		$this->authorize('create', [Sms::class, $id]);
		return view('sms.create', compact('project'))->withMessage("3 ways or all at once! Sms will go to all, contacts in Lists you have selected,
        numbers you have typed manually, and all numbers in the mobile column of your excel");
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request, $id = null)
	{
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  \App\Sms  $sms
	 * @return \Illuminate\Http\Response
	 */
	public function show(Sms $sms)
	{
		//
	}

	/**
	 * Show the form for editing the specified resource.
	 *
	 * @param  \App\Sms  $sms
	 * @return \Illuminate\Http\Response
	 */
	public function edit(Sms $sms)
	{
		$this->authorize('update', $sms);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  \App\Sms  $sms
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, Sms $sms)
	{
		$this->authorize('update', $sms);
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  \App\Sms  $sms
	 * @return \Illuminate\Http\Response
	 */
	public function destroy(Sms $sms)
	{
		$this->authorize('delete', $sms);
	}
}





class SSP
{
	/**
	 * Create the data output array for the DataTables rows
	 *
	 *  @param  array $columns Column information array
	 *  @param  array $data    Data from the SQL get
	 *  @return array          Formatted data in a row based format
	 */
	static function data_output($columns, $data)
	{
		$out = array();

		for ($i = 0, $ien = count($data); $i < $ien; $i++) {
			$row = array();

			for ($j = 0, $jen = count($columns); $j < $jen; $j++) {
				$column = $columns[$j];

				// Is there a formatter?
				if (isset($column['formatter'])) {
					if (empty($column['db'])) {
						$row[$column['dt']] = $column['formatter']($data[$i]);
					} else {
						$row[$column['dt']] = $column['formatter']($data[$i][$column['db']], $data[$i]);
					}
				} else {
					if (!empty($column['db'])) {
						$row[$column['dt']] = $data[$i][$columns[$j]['db']];
					} else {
						$row[$column['dt']] = "";
					}
				}
			}

			$out[] = $row;
		}

		return $out;
	}


	/**
	 * Database connection
	 *
	 * Obtain an PHP PDO connection from a connection details array
	 *
	 *  @param  array $conn SQL connection details. The array should have
	 *    the following properties
	 *     * host - host name
	 *     * db   - database name
	 *     * user - user name
	 *     * pass - user password
	 *     * Optional: `'charset' => 'utf8'` - you might need this depending on your PHP / MySQL config
	 *  @return resource PDO connection
	 */
	static function db($conn)
	{
		if (is_array($conn)) {
			return self::sql_connect($conn);
		}

		return $conn;
	}


	/**
	 * Paging
	 *
	 * Construct the LIMIT clause for server-side processing SQL query
	 *
	 *  @param  array $request Data sent to server by DataTables
	 *  @param  array $columns Column information array
	 *  @return string SQL limit clause
	 */
	static function limit($request, $columns)
	{
		$limit = '';

		if (isset($request['start']) && $request['length'] != -1) {
			$limit = "LIMIT " . intval($request['start']) . ", " . intval($request['length']);
		}

		return $limit;
	}


	/**
	 * Ordering
	 *
	 * Construct the ORDER BY clause for server-side processing SQL query
	 *
	 *  @param  array $request Data sent to server by DataTables
	 *  @param  array $columns Column information array
	 *  @return string SQL order by clause
	 */
	static function order($request, $columns)
	{
		$order = '';

		if (isset($request['order']) && count($request['order'])) {
			$orderBy = array();
			$dtColumns = self::pluck($columns, 'dt');

			for ($i = 0, $ien = count($request['order']); $i < $ien; $i++) {
				// Convert the column index into the column data property
				$columnIdx = intval($request['order'][$i]['column']);
				$requestColumn = $request['columns'][$columnIdx];

				$columnIdx = array_search($requestColumn['data'], $dtColumns);
				$column = $columns[$columnIdx];

				if ($requestColumn['orderable'] == 'true') {
					$dir = $request['order'][$i]['dir'] === 'asc' ?
						'ASC' :
						'DESC';

					$orderBy[] = '`' . $column['db'] . '` ' . $dir;
				}
			}

			if (count($orderBy)) {
				$order = 'ORDER BY ' . implode(', ', $orderBy);
			}
		}

		return $order;
	}


	/**
	 * Searching / Filtering
	 *
	 * Construct the WHERE clause for server-side processing SQL query.
	 *
	 * NOTE this does not match the built-in DataTables filtering which does it
	 * word by word on any field. It's possible to do here performance on large
	 * databases would be very poor
	 *
	 *  @param  array $request Data sent to server by DataTables
	 *  @param  array $columns Column information array
	 *  @param  array $bindings Array of values for PDO bindings, used in the
	 *    sql_exec() function
	 *  @return string SQL where clause
	 */
	static function filter($request, $columns, &$bindings)
	{
		$globalSearch = array();
		$columnSearch = array();
		$dtColumns = self::pluck($columns, 'dt');

		if (isset($request['search']) && $request['search']['value'] != '') {
			$str = $request['search']['value'];

			for ($i = 0, $ien = count($request['columns']); $i < $ien; $i++) {
				$requestColumn = $request['columns'][$i];
				$columnIdx = array_search($requestColumn['data'], $dtColumns);
				$column = $columns[$columnIdx];

				if ($requestColumn['searchable'] == 'true') {
					if (!empty($column['db'])) {
						$binding = self::bind($bindings, '%' . $str . '%', PDO::PARAM_STR);
						$globalSearch[] = "`" . $column['db'] . "` LIKE " . $binding;
					}
				}
			}
		}

		// Individual column filtering
		if (isset($request['columns'])) {
			for ($i = 0, $ien = count($request['columns']); $i < $ien; $i++) {
				$requestColumn = $request['columns'][$i];
				$columnIdx = array_search($requestColumn['data'], $dtColumns);
				$column = $columns[$columnIdx];

				$str = $requestColumn['search']['value'];

				if (
					$requestColumn['searchable'] == 'true' &&
					$str != ''
				) {
					if (!empty($column['db'])) {
						$binding = self::bind($bindings, '%' . $str . '%', PDO::PARAM_STR);
						$columnSearch[] = "`" . $column['db'] . "` LIKE " . $binding;
					}
				}
			}
		}

		// Combine the filters into a single string
		$where = '';

		if (count($globalSearch)) {
			$where = '(' . implode(' OR ', $globalSearch) . ')';
		}

		if (count($columnSearch)) {
			$where = $where === '' ?
				implode(' AND ', $columnSearch) :
				$where . ' AND ' . implode(' AND ', $columnSearch);
		}

		if ($where !== '') {
			$where = 'WHERE ' . $where;
		}

		return $where;
	}


	/**
	 * Perform the SQL queries needed for an server-side processing requested,
	 * utilising the helper functions of this class, limit(), order() and
	 * filter() among others. The returned array is ready to be encoded as JSON
	 * in response to an SSP request, or can be modified if needed before
	 * sending back to the client.
	 *
	 *  @param  array $request Data sent to server by DataTables
	 *  @param  array|PDO $conn PDO connection resource or connection parameters array
	 *  @param  string $table SQL table to query
	 *  @param  string $primaryKey Primary key of the table
	 *  @param  array $columns Column information array
	 *  @return array          Server-side processing response array
	 */
	static function simple($request, $conn, $table, $primaryKey, $columns)
	{
		$bindings = array();
		$db = self::db($conn);

		// Build the SQL query string from the request
		$limit = self::limit($request, $columns);
		$order = self::order($request, $columns);
		$where = self::filter($request, $columns, $bindings);

		// Main query to actually get the data
		$data = self::sql_exec(
			$db,
			$bindings,
			"SELECT `" . implode("`, `", self::pluck($columns, 'db')) . "`
			 FROM `$table`
			 $where
			 $order
			 $limit"
		);

		// Data set length after filtering
		$resFilterLength = self::sql_exec(
			$db,
			$bindings,
			"SELECT COUNT(`{$primaryKey}`)
			 FROM   `$table`
			 $where"
		);
		$recordsFiltered = $resFilterLength[0][0];

		// Total data set length
		$resTotalLength = self::sql_exec(
			$db,
			"SELECT COUNT(`{$primaryKey}`)
			 FROM   `$table`"
		);
		$recordsTotal = $resTotalLength[0][0];

		/*
		 * Output
		 */
		return array(
			"draw"            => isset($request['draw']) ?
				intval($request['draw']) :
				0,
			"recordsTotal"    => intval($recordsTotal),
			"recordsFiltered" => intval($recordsFiltered),
			"data"            => self::data_output($columns, $data)
		);
	}


	/**
	 * The difference between this method and the `simple` one, is that you can
	 * apply additional `where` conditions to the SQL queries. These can be in
	 * one of two forms:
	 *
	 * * 'Result condition' - This is applied to the result set, but not the
	 *   overall paging information query - i.e. it will not effect the number
	 *   of records that a user sees they can have access to. This should be
	 *   used when you want apply a filtering condition that the user has sent.
	 * * 'All condition' - This is applied to all queries that are made and
	 *   reduces the number of records that the user can access. This should be
	 *   used in conditions where you don't want the user to ever have access to
	 *   particular records (for example, restricting by a login id).
	 *
	 * In both cases the extra condition can be added as a simple string, or if
	 * you are using external values, as an assoc. array with `condition` and
	 * `bindings` parameters. The `condition` is a string with the SQL WHERE
	 * condition and `bindings` is an assoc. array of the binding names and
	 * values.
	 *
	 *  @param  array $request Data sent to server by DataTables
	 *  @param  array|PDO $conn PDO connection resource or connection parameters array
	 *  @param  string $table SQL table to query
	 *  @param  string $primaryKey Primary key of the table
	 *  @param  array $columns Column information array
	 *  @param  string|array $whereResult WHERE condition to apply to the result set
	 *  @param  string|array $whereAll WHERE condition to apply to all queries
	 *  @return array          Server-side processing response array
	 */
	static function complex(
		$request,
		$conn,
		$table,
		$primaryKey,
		$columns,
		$whereResult = null,
		$whereAll = null
	) {
		$bindings = array();
		$whereAllBindings = array();
		$db = self::db($conn);
		$whereAllSql = '';

		// Build the SQL query string from the request
		$limit = self::limit($request, $columns);
		$order = self::order($request, $columns);
		$where = self::filter($request, $columns, $bindings);

		// whereResult can be a simple string, or an assoc. array with a
		// condition and bindings
		if ($whereResult) {
			$str = $whereResult;

			if (is_array($whereResult)) {
				$str = $whereResult['condition'];

				if (isset($whereResult['bindings'])) {
					self::add_bindings($bindings, $whereResult['bindings']);
				}
			}

			$where = $where ?
				$where . ' AND ' . $str :
				'WHERE ' . $str;
		}

		// Likewise for whereAll
		if ($whereAll) {
			$str = $whereAll;

			if (is_array($whereAll)) {
				$str = $whereAll['condition'];

				if (isset($whereAll['bindings'])) {
					self::add_bindings($whereAllBindings, $whereAll['bindings']);
				}
			}

			$where = $where ?
				$where . ' AND ' . $str :
				'WHERE ' . $str;

			$whereAllSql = 'WHERE ' . $str;
		}

		// Main query to actually get the data
		$data = self::sql_exec(
			$db,
			$bindings,
			"SELECT `" . implode("`, `", self::pluck($columns, 'db')) . "`
			 FROM `$table`
			 $where
			 $order
			 $limit"
		);

		// Data set length after filtering
		$resFilterLength = self::sql_exec(
			$db,
			$bindings,
			"SELECT COUNT(`{$primaryKey}`)
			 FROM   `$table`
			 $where"
		);
		$recordsFiltered = $resFilterLength[0][0];

		// Total data set length
		$resTotalLength = self::sql_exec(
			$db,
			$whereAllBindings,
			"SELECT COUNT(`{$primaryKey}`)
			 FROM   `$table` " .
				$whereAllSql
		);
		$recordsTotal = $resTotalLength[0][0];

		/*
		 * Output
		 */
		return array(
			"draw"            => isset($request['draw']) ?
				intval($request['draw']) :
				0,
			"recordsTotal"    => intval($recordsTotal),
			"recordsFiltered" => intval($recordsFiltered),
			"data"            => self::data_output($columns, $data)
		);
	}


	/**
	 * Connect to the database
	 *
	 * @param  array $sql_details SQL server connection details array, with the
	 *   properties:
	 *     * host - host name
	 *     * db   - database name
	 *     * user - user name
	 *     * pass - user password
	 * @return resource Database connection handle
	 */
	static function sql_connect($sql_details)
	{
		try {
			$db = @new PDO(
				"mysql:host={$sql_details['host']};dbname={$sql_details['db']}",
				$sql_details['user'],
				$sql_details['pass'],
				array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
			);
		} catch (PDOException $e) {
			self::fatal(
				"An error occurred while connecting to the database. " .
					"The error reported by the server was: " . $e->getMessage()
			);
		}

		return $db;
	}


	/**
	 * Execute an SQL query on the database
	 *
	 * @param  resource $db  Database handler
	 * @param  array    $bindings Array of PDO binding values from bind() to be
	 *   used for safely escaping strings. Note that this can be given as the
	 *   SQL query string if no bindings are required.
	 * @param  string   $sql SQL query to execute.
	 * @return array         Result from the query (all rows)
	 */
	static function sql_exec($db, $bindings, $sql = null)
	{
		// Argument shifting
		if ($sql === null) {
			$sql = $bindings;
		}

		$stmt = $db->prepare($sql);
		//echo $sql;

		// Bind parameters
		if (is_array($bindings)) {
			for ($i = 0, $ien = count($bindings); $i < $ien; $i++) {
				$binding = $bindings[$i];
				$stmt->bindValue($binding['key'], $binding['val'], $binding['type']);
			}
		}

		// Execute
		try {
			$stmt->execute();
		} catch (PDOException $e) {
			self::fatal("An SQL error occurred: " . $e->getMessage());
		}

		// Return all
		return $stmt->fetchAll(PDO::FETCH_BOTH);
	}


	/* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *
	 * Internal methods
	 */

	/**
	 * Throw a fatal error.
	 *
	 * This writes out an error message in a JSON string which DataTables will
	 * see and show to the user in the browser.
	 *
	 * @param  string $msg Message to send to the client
	 */
	static function fatal($msg)
	{
		echo json_encode(array(
			"error" => $msg
		));

		exit(0);
	}

	/**
	 * Create a PDO binding key which can be used for escaping variables safely
	 * when executing a query with sql_exec()
	 *
	 * @param  array &$a    Array of bindings
	 * @param  *      $val  Value to bind
	 * @param  int    $type PDO field type
	 * @return string       Bound key to be used in the SQL where this parameter
	 *   would be used.
	 */
	static function bind(&$a, $val, $type)
	{
		$key = ':binding_' . count($a);

		$a[] = array(
			'key' => $key,
			'val' => $val,
			'type' => $type
		);

		return $key;
	}

	static function add_bindings(&$a, $vals)
	{
		foreach ($vals['bindings'] as $key => $value) {
			$bindings[] = array(
				'key' => $key,
				'val' => $value,
				'type' => PDO::PARAM_STR
			);
		}
	}


	/**
	 * Pull a particular property from each assoc. array in a numeric array, 
	 * returning and array of the property values from each item.
	 *
	 *  @param  array  $a    Array to get data from
	 *  @param  string $prop Property to read
	 *  @return array        Array of property values
	 */
	static function pluck($a, $prop)
	{
		$out = array();

		for ($i = 0, $len = count($a); $i < $len; $i++) {
			if (empty($a[$i][$prop]) && $a[$i][$prop] !== 0) {
				continue;
			}

			//removing the $out array index confuses the filter method in doing proper binding,
			//adding it ensures that the array data are mapped correctly
			$out[$i] = $a[$i][$prop];
		}

		return $out;
	}


	/**
	 * Return a string from an array or a string
	 *
	 * @param  array|string $a Array to join
	 * @param  string $join Glue for the concatenation
	 * @return string Joined string
	 */
	static function _flatten($a, $join = ' AND ')
	{
		if (!$a) {
			return '';
		} else if ($a && is_array($a)) {
			return implode($join, $a);
		}
		return $a;
	}
}
