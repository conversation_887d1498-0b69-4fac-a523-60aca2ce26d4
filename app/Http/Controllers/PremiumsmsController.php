<?php

namespace App\Http\Controllers;

use App\Premiumsms;
use App\Project;
use Illuminate\Http\Request;

class PremiumsmsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        //
        $this->authorize('viewAny', [Premiumsms::class, $id]);

        $project = Project::find($id);
        $smses = Premiumsms::where('project_id', $id)->latest()->get();
        return view('premiumsms.index', compact('smses', 'project'));

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function show(Premiumsms $premiumsms)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function edit(Premiumsms $premiumsms)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Premiumsms $premiumsms)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Premiumsms  $premiumsms
     * @return \Illuminate\Http\Response
     */
    public function destroy(Premiumsms $premiumsms)
    {
        //
    }
}
