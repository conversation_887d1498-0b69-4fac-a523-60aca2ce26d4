<?php

namespace App\Http\Controllers;

use App\Blog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BlogController extends Controller
{
    public function __construct()
    {
       // $this->authorizeResource(Blog::class, 'categories');
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $blogs = Blog::orderBy('created_at','desc')->get();
        return view('blogs.index', compact('blogs'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $this->authorize('create', Blog::class);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
        ]);
        //$this->authorize('create', Blog::class);
        $blog = new Blog();
        $blog->name = $request->title;
        $blog->title = $request->title;
        $blog->subtitle = $request->subtitle;
        $blog->introduction = $request->introduction;
        $blog->body = $request->body;
        $blog->image = $request->image;
        $blog->tags = $request->tags;
        $blog->published = $request->filled('published') ? 1 : 0;
        $blog->created_by = Auth::id();
        $blog->description = $request->description;
        $blog->priority = $request->priority;
        $blog->save();
        $request->session()->flash('message','Ready to go! Blog Created');
        return redirect('admin/blogs');


    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Blog  $blog
     * @return \Illuminate\Http\Response
     */
    public function show(Blog $blog)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Blog  $blog
     * @return \Illuminate\Http\Response
     */
    public function edit(Blog $blog )
    {
        //
        return view('blogs.edit', compact('blog'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Blog  $blog
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,Blog $blog)
    {
        //$this->authorize('update', User::class, Blog::class);
        $request->validate([
            'title' => 'required',
        ]);
        $blog->name = $request->title;
        $blog->title = $request->title;
        $blog->subtitle = $request->subtitle;
        $blog->introduction = $request->introduction;
        $blog->body = $request->body;
        $blog->image = $request->image;
        $blog->tags = $request->tags;
        $blog->published = $request->filled('published') ? 1 : 0;
        $blog->created_by = Auth::id();
        $blog->description = $request->description;
        $blog->priority = $request->priority;
        $blog->save();
        $request->session()->flash('message','Blog Updated');
        return redirect('admin/blogs');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Blog  $blog
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,Blog $blog)
    {

      //  $this->authorize('delete', Blog::class);
        $blog->delete();
        $request->session()->flash('message','Blog: '.$blog->name.' deleted');
        return redirect('admin/blogs');
    }
}
