<?php

namespace App\Http\Controllers;

use App\ContactList;
use App\Contact;
use Auth;
use Illuminate\Http\Request;
use App\Project;

class ContactListController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($pid)
    {
        //
        $this->authorize('viewany',[ContactList::class,$pid]);
        $project = Project::find($pid);
        $contact_lists = ContactList::where('project_id', $pid)->orderBy('created_at','desc')->get();
        return view('contactLists.index',compact('contact_lists','project'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($pid)
    {
        $this->authorize('create',[ContactList::class,$pid]);
        $project = Project::find($pid);
        $contact_lists = ContactList::where('project_id', $pid)->orderBy('created_at','desc')->get();
        return view('contactLists.create',compact('contact_lists','project'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store( Request $request, $pid)
    {
        $this->authorize('create', [ContactList::class,$pid]);
        $request->validate(['name' => 'required']);
        $project_id = $request->project_id;
        $request->merge(['project_id' => $project_id]);
        ContactList::create($request->all());
        $request->session()->flash('message','Contact Group Created successfully');
        return response()->json(['status' => 'success', 'message' => 'Contact Group created']);

    }

    /**
     * Display the specified resource.
     *
     * @param  \App\ContactList  $contactList
     * @return \Illuminate\Http\Response
     */
    public function show(ContactList $contactList)
    {
        // 
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\ContactList  $contactList
     * @return \Illuminate\Http\Response
     */
    public function edit($pid, $contactList)
    {
        ini_set('memory_limit', '10000M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
       $contactList = ContactList::where('id',$contactList)->first();

       $this->authorize('update', $contactList);
        $project = Project::find($pid);
        $contacts = Contact::where('project_id',$project->id)->get();
       // $contacts = Contact::where('project_id', $pid)->orderBy('created_at','desc')->get();//where('tag',$contactList->id)->get();
        return view('contactLists.log', compact('contactList','contacts','project'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\ContactList  $contactList
     * @return \Illuminate\Http\Response
     */
    //public function update(Request $request, ContactList $contactList)
    public function update(Request $request, $pid, ContactList $contactList)    {

        ini_set('max_input_vars', '100000');
        $request->validate(['name' => 'required']);
        $this->authorize('update', $contactList);
        $project_id = $pid;
        $request->merge(['project_id' => $project_id]);
        $contactList->update($request->except('contacts'));
        //comment to avoid pagination issues
        //$contactList->contacts()->sync($request->contacts); //delete those that are absent

        $request->session()->flash('message','Contact Group Updated');
        return redirect('projects/'.$pid.'/contacts');

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\ContactList  $contactList
     * @return \Illuminate\Http\Response
     */
    public function destroy($pid, ContactList $contactList)
    {
        $this->authorize('delete', $contactList);
        $contactList->contacts()->detach();
        $contactList->delete();
        return redirect('projects/'.$pid.'/contacts')->with('status', 'Contact group '.$contactList->name.' deleted successfully');

}
}
