<?php

namespace App\Http\Controllers;

use App\Country;
use Illuminate\Http\Request;

class CountryController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function show(Country $country)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function edit(Country $country)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Country $country)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Country  $country
     * @return \Illuminate\Http\Response
     */
    public function destroy(Country $country)
    {
        //
    }

    public function createCountries()
    {

        $curl = curl_init();

        //dd($to);

        $url = "https://restcountries.eu/rest/v2/all";
        $data = [
            //  'from' => 'Wezadata',
        ];

        $curl = curl_init($url . '?' . http_build_query($data));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPGET, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, [
            "accept: application/json",
            // "authorization: Bearer Nz8bXapdf866S0cYLTPxJ50ijqFA8xlgNYHwERhB1VQ0nRgDUAb8lzG3z6wy",
            "content-type: application/json"
        ]);

        $response = curl_exec($curl);
        curl_close($curl);
        $json_countries = $response;
        $countries_data = json_decode($json_countries, true);
        foreach ($countries_data as $data) {
            //insert country


            $country = new Country();
            $country->name = $data['name'];
            $country->alpha2Code = $data['alpha2Code'];
            $country->alpha3Code = $data['alpha3Code'];
            $country->callingCodes = $data['callingCodes'][0];
            $country->timezones = $data['timezones'][0];
            $country->currency_code = $data['currencies'][0]['code'];
            $country->currency_name = $data['currencies'][0]['name'];
            $country->currency_symbol = $data['currencies'][0]['symbol'];
            $country->flag = $data['flag'];
            $country->timezone = $data['timezones'][0];
            // $country->language = $data['languages'][0] ;
            $country->save();

            // Update Key
        }
    }
}
