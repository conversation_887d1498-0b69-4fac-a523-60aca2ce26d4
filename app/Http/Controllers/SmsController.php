<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;
use App\Sms;
use App\Jobs\ProcessSendsafaricombulk;
use App\Jobs\ProcessSendtelkombulk;
use App\Jobs\ProcessSendairtelbulk;
use App\Contact;
use App\ContactList;
use App\Deposit;
use App\Campaign;
use App\Alphanumeric;
use App\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Imports\ContactsImport;
use Illuminate\Database\Eloquent\Builder;
use App\Enrollment;
use App\Jobs\ProcessMessage;
use GuzzleHttp\Client;
use App\Payment;
use App\Jobs\Schedulesms;
use Exception;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exchange\AMQPExchangeType;
use App\Mail\AppNotification;
use Illuminate\Support\Facades\Mail;
use App\Template;

class RabbitConnection
{
    private static $instance = null;

    private $conn;
    private $host = 'pepea-rabbit';
    private $port = '5672';
    private $username = 'root';
    private $password = 'secret';

    private function __construct()
    {
        $this->conn =  new AMQPStreamConnection($this->host, $this->port, $this->username, $this->password);
    }
    public static function getInstance()
    {
        if (!self::$instance) { //instantiate it only once
            self::$instance = new RabbitConnection(); //this instantiation will create a new connection from within the constructor
        }
        return self::$instance;
    }

    /**
     * return the connection for pushing messages queries
     */
    public function getConnection()
    {
        return $this->conn;
    }
}


class SmsController extends Controller
{
    public function __construct()
    {
        //   $this->authorizeResource(Sms::class, 'sms');
    }
    private function redirectAndContinue($url)
    {
        ignore_user_abort(true);
        header("Location: $url");
        header("Connection: close");
        header("Content-Length: 0");
        flush();
    }
    function removeBannedNUmbers($senderId, $project_id, $numbers)
    {
        $unique_banned_numbers = array_values(array_unique(DB::table('banned_numbers')->where('project_id', $project_id)->where('sender_name', $senderId)->pluck('mobile')->toArray()));
        return array_unique(array_diff($numbers, $unique_banned_numbers)); //end of cleaning
    }
    function assignSafaricomFroms($array, $from)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'safaricom'),  $from));
        return $ar;
    }
    function assignAirtelFroms($array, $from)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'airtel'),  $from));
        return $ar;
    }
    function assignTelkomFroms($array, $from)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'telkom'),  $from));
        return $ar;
    }
    function assignSafaricomCredits($array, $rate)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'safaricom'),  $rate));
        return $ar;
    }
    function assignAirtelCredits($array, $rate)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'airtel'),  $rate));
        return $ar;
    }
    function assignTelkomCredits($array, $rate)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'telkom'),  $rate));
        return $ar;
    }
    function assignUnsupportedCredits($array, $rate)
    {
        $ar = array_replace($array,  array_fill_keys(array_keys($array, 'unsupported'),  $rate));
        return $ar;
    }
    public function gen_uid($message_id, $mobile)
    {
        $id = substr($message_id, 0, 4); //$mobile = base_convert($mobile, 10, 36); 
        //$mobile = substr($mobile, -4, 4);//complications in searching suffix
        $mobile_string = substr($mobile, 3, 7); //7251642 //254 is similar so remove to keep string shhort
        $mobile_string = base_convert($mobile_string, 10, 36);
        return  $id . $mobile_string;
    }

    public function analytical_sms($message, $message_id, $number)
    {
        $new_link = 'https://cli.ke/a/' . $this->gen_uid($message_id, $number);
        if (str_contains($message, 'STOP*456*9*5#')) {
            $message = str_replace("STOP*456*9*5#", $new_link . " STOP*456*9*5#", $message);
        } else {
            $message = $message . ' ' . $new_link;
        }
        return ($message);
    }
    //finally chunk the final payload by telco, to allow for more fine-graned processing
    public function safaricomNumbers($data)
    {
        $new_array = array_filter($data, function ($obj) {
            if ($obj['telco'] == 'safaricom') {

                return true;
            }
            return false;
        });
        return $new_array;
    }
    public function airtelNumbers($data)
    {
        $new_array = array_filter($data, function ($obj) {
            if ($obj['telco'] == 'airtel') {
                return true;
            }
            return false;
        });
        return $new_array;
    }
    public function telkomNumbers($data)
    {
        $new_array = array_filter($data, function ($obj) {
            if ($obj['telco'] == 'telkom ') {
                return true;
            }
            return false;
        });
        return $new_array;
    }
  


    public function checkTelco($number)
    {
        $prefix = substr($number, -9, 3);
        $safaricom_prefixes = array_flip([110, 111, 112, 113, 114, 115, 117, 687, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 740, 741, 742, 743, 745, 746, 747, 748, 749, 757, 758, 759, 768, 769, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799]);
        $airtel_prefixes = array_flip([100, 101, 102, 103, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 750, 751, 752, 753, 754, 755, 756, 762, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789]);
        $telkom_prefixes = array_flip([770, 771, 772, 773, 774, 775, 776, 777, 778, 779]);
        $equitel_prefixes = array_flip([765]);
        $eferio_prefixes = array_flip([761]);
        $faiba4G_prefixes = array_flip([747]);
        $semamobile_prefixes = array_flip([767]);
        $mobilepay_prefixes = array_flip([760]);
        $homelandsmedia_prefixes = array_flip([744]);
        if (isset($safaricom_prefixes[$prefix])) {
            return 'safaricom';
        } elseif (isset($airtel_prefixes[$prefix])) {
            return 'airtel';
        } elseif (isset($telkom_prefixes[$prefix])) {
            return 'telkom';
        } elseif (isset($faiba4G_prefixes[$prefix])) {
            return 'faiba4G';
        }
        /* } elseif (isset($equitel_prefixes[$prefix])) {
            return 'equitel';
        } elseif (isset($eferio_prefixes[$prefix])) {
            return 'eferio';
        }elseif (isset($semamobile_prefixes[$prefix])) {
            return 'semamobile';
        } elseif (isset($mobilepay_prefixes[$prefix])) {
            return 'mobilepay';
        } elseif (isset($homelandsmedia_prefixes[$prefix])) {
            return 'homelandsmedia';   */ else {
            return 'unsupported';
        }
    }
    public function cleanNumber($phone)
    {
        return  preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
    }
    private function verifyNumber($phone)
    {
        return preg_match("/^[+]?[0-9]{7,13}$/", $phone) ? 'valid' : 'invalid';
    }
    public function e164Formatter254($phone)
    {
        $forced_numbber = substr($phone, -9);
        return '254' . $forced_numbber;
    }
    private function statusGenerator($phone)
    {
        if (strlen($phone) == 12) {
            return "sent";
        } else {
            return "unsupported_number";
        }
    }
    private function status_codeGenerator($phone)
    {
        if (strlen($phone) == 12) {
            return "11";
        } else {
            return "12";
        }
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id)
    {
        ini_set('memory_limit', '4096M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $this->authorize('viewAny', [Sms::class, $id]);
        $project = Project::find($id);
        $templates = Template::where('project_id', $id)->orderBy('created_at','desc')->get();
        $alphanumerics = Alphanumeric::where('project_id', $id)->orderBy('created_at', 'desc')->get();

        //$smses = SMS::where('project_id', $id)->latest()->get();
        return view('sms.log', compact('project', 'templates', 'alphanumerics'));
    }
    public function search(Request $request, $id)
    {
        ini_set('memory_limit', '1024M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $arr = explode(' ', $request->between);
        $project = Project::find($id);
        $this->authorize('viewAny', [Sms::class, $id]);
        if ($arr[0] == "") { //empty form submitted
            $smses = DB::table('sms_copy')->where('project_id', $project->id)->latest()->get();
            return view('sms.index', compact('smses', 'project'));
        }
        $from  = $arr[0];
        $to = $arr[2];
        $smses = DB::table('sms_copy')->whereDate('created_at', '>=', $from)
            ->whereDate('created_at', '<=', $to)
            ->where('project_id', $id)
            ->latest()->get();
        return view('sms.index', compact('smses', 'project'));
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($id)
    {
        $project = Project::find($id);
        $this->authorize('create', [Sms::class, $id]);
        $project = Project::find($id);
        $templates = Template::where('project_id', $id)->orderBy('created_at','desc')->get();
        $alphanumerics = Alphanumeric::where('project_id', $id)->orderBy('created_at', 'desc')->get();

        //$smses = SMS::where('project_id', $id)->latest()->get();
        return view('sms.create', compact('project', 'templates', 'alphanumerics'))->with("Message", "");
    }
    public function createhsm($id)
    {
        $project = Project::find($id);
        $this->authorize('create', [Sms::class, $id]);
        return view('sms.hsm', compact('project'))->with("Message", "3 ways or all at once! Sms will go to all, contacts in Lists you have selected,
        numbers you have typed manually, and all numbers in the mobile column of your excel");
    }
    public function createanalytical($id)
    {
        $project = Project::find($id);
        $this->authorize('create', [Sms::class, $id]);
        return view('sms.analytical', compact('project'))->with("Message", "Send Analytical Messages to your customers and monitor when and who clicked the embedded link");
    }

    private function assignFroms(
        $type,
        $telcos,
        $saf_promo_from,
        $saf_trans_from,
        $airtel_promo_from,
        $airtel_trans_from,
        $telkom_promo_from,
        $telkom_trans_from
    ) {
        if ($type == 'promotional') {
            $safcom_from_assigned = $this->assignSafaricomFroms($telcos, $saf_promo_from);
            $safaricom_airtel_from_assigned = $this->assignAirtelFroms($safcom_from_assigned, $airtel_promo_from);
            $froms = $this->assignTelkomFroms($safaricom_airtel_from_assigned, $telkom_promo_from);
        } elseif ($type == 'transactional') {
            $safcom_from_assigned = $this->assignSafaricomFroms($telcos, $saf_trans_from);
            $safaricom_airtel_from_assigned = $this->assignAirtelFroms($safcom_from_assigned, $airtel_trans_from);
            $froms = $this->assignTelkomFroms($safaricom_airtel_from_assigned, $telkom_trans_from);
        }
        return $froms;
    }
   
    private function processFromAddress($project_id, $from)
    {
        $project_saf_promo_from = Alphanumeric::where(['project_id' => $project_id, 'name' => $from, 'type' => 'promotional', 'telco' => 'safaricom', 'status' => 'active'])->get();
        $project_saf_trans_from = Alphanumeric::where(['project_id' => $project_id, 'name' => $from, 'type' => 'transactional', 'telco' => 'safaricom', 'status' => 'active'])->get();
        $project_airtel_promo_from = Alphanumeric::where(['project_id'  => $project_id, 'name' => $from, 'type' => 'promotional', 'telco' => 'airtel', 'status' => 'active'])->get();
        $project_airtel_trans_from = Alphanumeric::where(['project_id' => $project_id, 'name' => $from, 'type' => 'transactional', 'telco' => 'airtel', 'status' => 'active'])->get();
        $project_telkom_promo_from = Alphanumeric::where(['project_id' => $project_id, 'name' => $from, 'type' => 'promotional', 'telco' => 'telkom', 'status' => 'active'])->get();
        $project_telkom_trans_from = Alphanumeric::where(['project_id' => $project_id, 'name' => $from, 'type' => 'transactional', 'telco' => 'telkom', 'status' => 'active'])->get();

        $primary_id = DB::table('projects')->where('name', 'primary_project')->value('id');
        $saf_promo_from =  $project_saf_promo_from->count() > 0 ? $project_saf_promo_from->first()->name : (Alphanumeric::where(['project_id' => $primary_id, 'name' => 'Sozuri', 'type' => 'promotional', 'telco' => 'safaricom', 'status' => 'active'])->exists() ?
            Alphanumeric::where(['project_id' => $primary_id, 'name' => 'Sozuri', 'type' => 'promotional', 'telco' => 'safaricom', 'status' => 'active'])->value('name')   : null);
        $saf_trans_from =  $project_saf_trans_from->count() > 0 ? $project_saf_trans_from->first()->name : (Alphanumeric::where(['project_id' => $primary_id, 'name' => 'Sozuri', 'type' => 'transactional', 'telco' => 'safaricom', 'status' => 'active'])->exists() ?
            Alphanumeric::where(['project_id' => $primary_id, 'name' => 'Sozuri', 'type' => 'transactional', 'telco' => 'safaricom', 'status' => 'active'])->value('name') : null);
        $airtel_promo_from =  $project_airtel_promo_from->count() > 0 ? $project_airtel_promo_from->first()->name : (Alphanumeric::where(['project_id' => $primary_id, 'name' => 'Sozuri', 'type' => 'promotional', 'telco' => 'airtel', 'status' => 'active'])->exists() ?
            Alphanumeric::where(['project_id' => $primary_id, 'name' => 'Sozuri', 'type' => 'promotional', 'telco' => 'airtel', 'status' => 'active'])->value('name') : null);
        $airtel_trans_from =  $project_airtel_trans_from->count() > 0 ? $project_airtel_trans_from->first()->name : (Alphanumeric::where(['project_id' =>  $primary_id, 'name' => 'Sozuri', 'type' => 'transactional', 'telco' => 'airtel', 'status' => 'active'])->exists() ?
            Alphanumeric::where(['project_id' =>  $primary_id, 'name' => 'Sozuri', 'type' => 'transactional', 'telco' => 'airtel', 'status' => 'active'])->value('name') : null);
        $telkom_promo_from =  $project_telkom_promo_from->count() > 0 ? $project_telkom_promo_from->first()->name : (Alphanumeric::where(['project_id' =>  $primary_id, 'name' => 'Sozuri', 'type' => 'promotional', 'telco' => 'telkom', 'status' => 'active'])->exists() ?
            Alphanumeric::where(['project_id' =>  $primary_id, 'name' => 'Sozuri', 'type' => 'promotional', 'telco' => 'telkom', 'status' => 'active'])->value('name') : null);
        $telkom_trans_from =  $project_telkom_trans_from->count() > 0 ? $project_telkom_trans_from->first()->name : (Alphanumeric::where(['project_id' =>  $primary_id, 'name' => 'Sozuri', 'type' => 'transactional', 'telco' => 'telkom', 'status' => 'active'])->exists() ?
            Alphanumeric::where(['project_id' =>  $primary_id, 'name' => 'Sozuri', 'type' => 'transactional', 'telco' => 'telkom', 'status' => 'active'])->value('name') : null);

        return [$saf_promo_from, $saf_trans_from, $airtel_promo_from,  $airtel_trans_from,  $telkom_promo_from,  $telkom_trans_from];
    }

    private function getCampaignId($project_id, $campaign = null)
    {
        if ($campaign && $campaign != "") {
            $campaign_id = DB::table('campaigns')->where('name', 'like', $campaign)->where('project_id', $project_id)->exists() ?
                DB::table('campaigns')->where('name', 'like', $campaign)->where('project_id', $project_id)->first()->id :
                DB::table('campaigns')->insertGetId(['name' => $campaign, 'goal' => $campaign, 'project_id' => $project_id]);
        } else {
            $campaign_id = DB::table('campaigns')->where(['name' => 'default', 'project_id' => $project_id])->exists() ? DB::table('campaigns')->where(['name' => 'default', 'project_id' => $project_id])->first()->id :
                DB::table('campaigns')->insertGetId(['name' => 'default', 'goal' => 'default', 'project_id' => $project_id]);
        }
        $campaignName = $campaign ?? "default";
        return  [$campaign_id,  $campaignName] ;
    }
    private function resolveFromAddress($type, $from, $project_id)
    {
        $type =   strtolower($type) == 'transactional' ? 'transactional' : 'promotional';
        Log::info('original type' . $type);
        Log::info($type);
        Log::info('original from' . $from);
        $request_from = DB::table('alphanumerics')->where(['project_id' => $project_id, 'status' => 'active', 'type' => $type, 'name' => $from])->exists() ?
            DB::table('alphanumerics')->where(['project_id' => $project_id, 'status' => 'active', 'type' => $type, 'name' => $from])->get()->first()->name : null;
        log::info('request from' . $request_from);
        $from = $request_from !== null  ? $request_from : 'Sozuri';
        log::info('from' . $from);
        log::info('project id ' . $project_id);
        return $from;
    }

    private function getFileRecipients($file)
    {
        Log::info("checking file...");
        $isExcel = $this->checkExcel($file);
        if (!$isExcel) {
            Log::info('You appear to have uploaded unallowed types. Use MS Excel xlsx, xls or CS');
            return [];
        }
        $file_collection = (new ContactsImport)->toCollection($file, \Maatwebsite\Excel\Excel::XLSX); //remove type to test with other types
        $file_recipients = array_column($file_collection->collapse()->toArray(), 'mobile');
        $clean_file_recipients = array_map(array($this, 'cleanNumber'), $file_recipients);
        $formatted_file_recipients = array_map(array($this, 'e164Formatter254'), $clean_file_recipients);
        return array_unique($formatted_file_recipients);
    }

    private function getListRecipients(string $recipientList)
    {
        $recipientListWithoutSpaces = str_replace(" ", "", $recipientList);
        $recipient_list_numbers = explode(",", $recipientListWithoutSpaces);
        $list_recipients = array_filter(array_map(array($this, 'cleanNumber'), $recipient_list_numbers)); //array_filterIf no callback is supplied, all entries of input equal to false (see converting to boolean) will be removed.
        $formatted_list_recipients = array_map(array($this, 'e164Formatter254'), $list_recipients);
        $valid_list_recipients = array_map(array($this, 'verifyNumber'), $formatted_list_recipients);
        $labeled_list_array = preg_grep("/invalid/", $valid_list_recipients);
        if (!empty($labeled_list_array)) {
            $filtered_list = preg_grep("/invalid/", $valid_list_recipients); //filter for where verify num set to invalid
            $filtered_list_rows = array_keys($filtered_list); //send this back to view as error
            //$request->session()->flash('message', 'Your contact List had errors near line(s): ' . implode(", ", $filtered_list_rows));
            return [];
        } else { //
            return array_unique($formatted_list_recipients);
        }
    }

    private function getContactGroupRecipients(array $contactlists, $project_id)
    {
        $numbers  = [];
        foreach ($contactlists  as $possible_lists => $value) {
            $numbers = array_merge($numbers, ContactList::find($value)->contacts()->pluck('mobile')->toArray());
            //the group will always belong to same project
            $numbers = array_merge($numbers, DB::table('contacts')->where(['tag' => $value, 'project_id' => $project_id])->pluck('mobile')->toArray());
        }
        $contactlist_recipients =  $numbers;
        $group_recipients = array_filter(array_map(array($this, 'cleanNumber'), $contactlist_recipients));
        $formatted_group_recipients =  array_map(array($this, 'e164Formatter254'), $group_recipients);
        return array_unique($formatted_group_recipients);
    }
    
    private function safaricomCostCalculator($project_id, $telcos, $messagePart, $type)
    {
        if (array_key_exists('safaricom', array_count_values($telcos))  !== true) {
            return  [0, 0, 0];
        }
        $safcom_count = array_count_values($telcos)['safaricom'] * $messagePart; //its either safaricom, airtel or uknown.
        $primary_payment = DB::table('payments')->where(['project_id' => DB::table('projects')->where('name', 'primary_project')->value('id'), 'type' => $type, 'telco' => 'safaricom', 'status' => 'success'])->where('balance', '>',  $safcom_count)->first();
        $primary_payment->balance -= $safcom_count; //deduct one csp unit
        $project = Project::where('id',$project_id)->first();
        if ($project->account_type == "postpay") {
            $unit_cost = (float)explode(',', $project->details)[0]; // Ensure this is a float
            $total_cost = $safcom_count * $unit_cost;
            
            return [$safcom_count, $total_cost, $primary_payment->unit_cost];
           // return  [$safcom_count, ($safcom_count * explode(',', $project->details)[0]), $primary_payment->unit_cost];
        }
        $payments = DB::table('payments')->where('project_id', $project_id)->where('balance', '>', 5)->where('status', 'success')->get();
        try {
            $payment_data = ['payment_id'  => $primary_payment->id, 'usage' => $safcom_count];
            $data_string = json_encode($payment_data);
            $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
            fwrite($file_stream, $data_string . "\n");
            fclose($file_stream);
            Log::info('primary payments appended to file done');
        } catch (Exception $e) {
            echo $e->getMessage();
        }
        $units_needed = $safcom_count;
        $cash = 0;
        $total_units_paid = 0;
        foreach ($payments as $payment) {
            $units_available = $payment->balance / ($payment->safaricom);
            $units_paid =  $units_needed >  $units_available ? $units_available :  $units_needed; //if needed exceeds available 
            $payment->balance = $payment->balance - ($units_paid * $payment->safaricom);
            try {
                $payment_data = ['payment_id'  => $payment->id, 'usage' => ($units_paid * $payment->safaricom)];
                $data_string = json_encode($payment_data);
                $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
                fwrite($file_stream, $data_string . "\n");
                fclose($file_stream);
                Log::info("project payment records appended to file");
            } catch (Exception $e) {
                echo  $e->getMessage();
            }
            Log::info('available ' . $units_available . ' paid units ' . $units_paid . ' payment balance ' . $payment->balance . 'units needed' . $units_needed);
            $units_needed = $units_needed - $units_paid; //these will be forwarded to next payment on the loop
            $cash +=  ($units_paid * $payment->safaricom);
            $total_units_paid += $units_paid;
            if ($units_needed == 0) {
                return  [$total_units_paid, $cash, $primary_payment->unit_cost];
            }
        }
        return  [0, 0, 0];
    }
    private function airtelCostCalculator($project_id, $telcos, $messagePart, $type)
    {
        if (array_key_exists('airtel', array_count_values($telcos))  !== true) {
            return  [0, 0, 0];
        }
        $airtel_count = array_count_values($telcos)['airtel'] * $messagePart;
        $primary_payment = Payment::where(['project_id' => Project::where('name', 'primary_project')->value('id'), /*'type' => $type, */ 'telco' => 'airtel', 'status' => 'success'])->where('balance', '>',  $airtel_count)->first();
        $primary_payment->balance = $primary_payment->balance - $airtel_count; //deduct one csp unit
        //$primary_payment->save();

        $project = Project::where('id',$project_id)->first();
        
        if ($project->account_type == "postpay") {
            return  [$airtel_count, ($airtel_count * explode(',', $project->details)[1]), $primary_payment->unit_cost];
        }
        $payments = DB::table('payments')->where('project_id', $project_id)->where('balance', '>', 5)->where('status', 'success')->get();
        try {
            $payment_data = ['payment_id'  => $primary_payment->id, 'usage' => $airtel_count];
            $data_string = json_encode($payment_data);
            $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
            fwrite($file_stream, $data_string . "\n");
            fclose($file_stream);
            Log::info('primary payments appended to file done');
        } catch (Exception $e) {
            echo $e->getMessage();
        }
        $units_needed = $airtel_count;
        $cash = 0;
        $total_units_paid = 0;
        foreach ($payments as $payment) {
            //does this payment cover all safcom count ?
            $units_available = $payment->balance / ($payment->airtel); //
            $units_paid =  $units_needed >  $units_available ? $units_available :  $units_needed; //if needed exceeds available 
            $payment->balance =  $payment->balance - ($units_paid * $payment->airtel); //
            //$payment->save();
            try {
                $payment_data = ['payment_id'  => $payment->id, 'usage' => ($units_paid * $payment->airtel)];
                $data_string = json_encode($payment_data);
                $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
                fwrite($file_stream, $data_string . "\n");
                fclose($file_stream);
                Log::info("project payment records appended to file");
            } catch (Exception $e) {
                echo  $e->getMessage();
            }
            Log::info('available ' . $units_available . ' paid units ' . $units_paid . ' payment balance ' . $payment->balance . 'units needed' . $units_needed);
            $units_needed = $units_needed - $units_paid; //these will be forwarded to next payment on the loop
            $cash +=  ($units_paid * $payment->airtel);
            $total_units_paid += $units_paid;
            if ($units_needed == 0) {
                return  [$total_units_paid, $cash, $primary_payment->unit_cost];
            }
        }
    }
    private function telkomCostCalculator($project_id, $telcos, $messagePart, $type)
    {
        if (array_key_exists('telkom', array_count_values($telcos))  !== true) {
            return  [0, 0, 0];
        }
        $telkom_count = array_count_values($telcos)['telkom'] * $messagePart; //its either telkom,  or uknown.
        $primary_payment = Payment::where(['project_id' => Project::where('name', 'primary_project')->value('id'), /*'type' => $type,*/ 'telco' => 'telkom', 'status' => 'success'])->where('balance', '>',  $telkom_count)->first();
        $primary_payment->balance = $primary_payment->balance - $telkom_count; //deduct one csp unit
        $project = Project::where('id',$project_id)->first();
        if ($project->account_type == "postpay") {
            return  [$telkom_count, ($telkom_count * explode(',', $project->details)[2]), $primary_payment->unit_cost];
        }
        $payments = DB::table('payments')->where('project_id', $project_id)->where('balance', '>', 5)->where('status', 'success')->get();

        try {
            $payment_data = ['payment_id'  => $primary_payment->id, 'usage' => $telkom_count];
            $data_string = json_encode($payment_data);
            $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
            fwrite($file_stream, $data_string . "\n");
            fclose($file_stream);
            Log::info('primary payments appended to file done');
        } catch (Exception $e) {
            echo $e->getMessage();
        }
        $units_needed = $telkom_count;
        $cash = 0;
        $total_units_paid = 0;
        foreach ($payments as $payment) {
            $units_available = $payment->balance / ($payment->telkom); //
            $units_paid =  $units_needed >  $units_available ? $units_available :  $units_needed; //if needed exceeds available 
            $payment->balance = $payment->balance - ($units_paid * $payment->telkom); //
            try {
                $payment_data = ['payment_id'  => $payment->id, 'usage' => ($units_paid * $payment->telkom)];
                $data_string = json_encode($payment_data);
                $file_stream = fopen(base_path() . "/storage/logs/payments-to-persist.log", "a"); //or die("Unable to open file!");
                fwrite($file_stream, $data_string . "\n");
                fclose($file_stream);
                Log::info("project payment records appended to file");
            } catch (Exception $e) {
                echo  $e->getMessage();
            }
            Log::info('available ' . $units_available . ' paid units ' . $units_paid . ' payment balance ' . $payment->balance . 'units needed' . $units_needed);
            $units_needed = $units_needed - $units_paid; //these will be forwarded to next payment on the loop
            $cash +=  ($units_paid * $payment->telkom);
            $total_units_paid += $units_paid;
            if ($units_needed == 0) {
                return  [$total_units_paid, $cash, $primary_payment->unit_cost];
            }
        }
    }
    /*private function assignCredits($telcos, $safaricom_unit_cost, $airtel_unit_cost, $telkom_unit_cost, $estimated_sms)
    {

        $safcom_credit_assigned = $this->assignSafaricomCredits($telcos, ($safaricom_unit_cost * $estimated_sms));
        $safcom_airtel_credit_assigned = $this->assignAirtelCredits($safcom_credit_assigned, ($airtel_unit_cost * $estimated_sms));
        $safcom_airtel_telkom_credit_assigned = $this->assignTelkomCredits($safcom_airtel_credit_assigned, ($telkom_unit_cost * $estimated_sms));
        $credits = $this->assignUnsupportedCredits($safcom_airtel_telkom_credit_assigned, ($telkom_unit_cost * $estimated_sms));
        return $credits;
    }*/
    private function assignCredits($telcos, $safaricom_unit_cost, $airtel_unit_cost, $telkom_unit_cost, $message_part)
    {
        // Validate inputs
        if (!is_array($telcos) || empty($telcos)) {
            throw new InvalidArgumentException("Telcos array cannot be empty or non-array.");
        }
        
        $unit_rates = [
            'safaricom' => $safaricom_unit_cost,
            'airtel'    => $airtel_unit_cost,
            'telkom'    => $telkom_unit_cost
        ];

        // Assign credits while maintaining order
        $credits = array_map(function ($telco) use ($unit_rates, $message_part) {
            return isset($unit_rates[$telco]) ? $unit_rates[$telco] * $message_part : 0;
        }, $telcos);

        return $credits;
    }

    public function costCalculator($project_id, $telcos, $message_part, $type)
    {
        // Fetch telco costs
        $safaricom_costs = $this->safaricomCostCalculator($project_id, $telcos, $message_part, $type);
        $airtel_costs = $this->airtelCostCalculator($project_id, $telcos, $message_part, $type);
        $telkom_costs = $this->telkomCostCalculator($project_id, $telcos, $message_part, $type);
    
        // Extract values safely
        [$safaricom_units, $safaricom_total_cost, $primary_safaricom_cost] = $safaricom_costs;
        [$airtel_units, $airtel_total_cost, $primary_airtel_cost] = $airtel_costs;
        [$telkom_units, $telkom_total_cost, $primary_telkom_cost] = $telkom_costs;
    
        // Prevent division by zero
        $safaricom_unit_cost = $safaricom_units == 0 ? 0 : $safaricom_total_cost / max(1, $safaricom_units);
        $airtel_unit_cost =  $airtel_units == 0 ? 0 : $airtel_total_cost / max(1, $airtel_units);
        $telkom_unit_cost =  $telkom_units == 0 ? 0 : $telkom_total_cost / max(1, $telkom_units);
    
        return [$safaricom_unit_cost, $airtel_unit_cost, $telkom_unit_cost, $primary_safaricom_cost, $primary_airtel_cost, $primary_telkom_cost];
    }

    private function setSmsPayload($project_id, $message_part, $type, $froms_array, $recipients, $messages, $sendAt, $campaign_id, $links, $message_ids, $channel, $campaignName)
    {// message_parts is estimated_sms
        $time_start = microtime(true);
        $telcos = array_map(array($this, 'checkTelco'), $recipients);
         // Get unit costs
        $unit_costs = $this->costCalculator($project_id, $telcos, $message_part, $type);
        [$safaricom_unit_cost, $airtel_unit_cost, $telkom_unit_cost] = array_slice($unit_costs, 0, 3);

        Log::info('API cost calculated in ' . (string)(microtime(true) - $time_start));
        
        // Assign credits correctly
        $credits = $this->assignCredits($telcos, $safaricom_unit_cost, $airtel_unit_cost, $telkom_unit_cost, $message_part);

        $costs = $this->assignCredits($telcos, $unit_costs[3], $unit_costs[4], $unit_costs[5], $message_part);
        Log::info('cost mapped in array in' . (string)(microtime(true) - $time_start));
        $froms = $this->assignFroms($type, $telcos, $froms_array[0], $froms_array[1], $froms_array[2], $froms_array[3], $froms_array[4], $froms_array[5]);
        Log::info('from(s) address assigned in array in' . (string)(microtime(true) - $time_start));
        $sms_count = count($recipients);
        Log::info(array_sum($credits) . 'for ' . count($recipients) . ' recipients');
        $primary_id = DB::table('projects')->where('name', 'primary_project')->value('id');
        $safcomPackageId = array_key_exists('safaricom', array_count_values($telcos)) ?  (DB::table('payments')->where(['project_id' =>  $primary_id, 'type' =>  $type, 'telco' => 'safaricom', 'status' => 'success'])->where('balance', '>', (array_count_values($telcos)['safaricom'] *  $message_part))->exists() ?
            DB::table('payments')->where(['project_id' =>  $primary_id, 'type' =>  $type, 'telco' => 'safaricom', 'status' => 'success'])->where('balance', '>', (array_count_values($telcos)['safaricom'] *  $message_part))->first()->package_id : false) : true;
        $airtelPackageId = array_key_exists('airtel', array_count_values($telcos)) ?  (DB::table('payments')->where(['project_id' =>  $primary_id, /*'type' =>  $type,*/ 'telco' => 'airtel', 'status' => 'success'])->where('balance', '>', (array_count_values($telcos)['airtel'] *  $message_part))->exists() ?
            DB::table('payments')->where(['project_id' =>  $primary_id, /*'type' =>  $type, */'telco' => 'airtel', 'status' => 'success'])->where('balance', '>', (array_count_values($telcos)['airtel'] *  $message_part))->first()->id : false) : true;

        if (!$safcomPackageId || !$airtelPackageId) {
            Log::info('no safricom or airtel package exists');
            return false;
        }
        $packageIds = array_fill(0,  $sms_count, $safcomPackageId);
        $statuses = array_map(array($this, 'statusGenerator'), $recipients);
        $status_codes = array_map(array($this, 'status_codeGenerator'), $recipients);
        $message_parts = array_fill(0, $sms_count, $message_part);
        $tos = $recipients;
        $project_ids = array_fill(0, $sms_count,  $project_id);
        $types = array_fill(0,  $sms_count, $type); //type
        $now = date('Y-m-d H:i:s');
        $created_ats = array_fill(0,  $sms_count, $now);
        $send_ats = array_fill(0, $sms_count,  $sendAt);
        $campaign_ids = array_fill(0, $sms_count, $campaign_id);
        $campaignNames = array_fill(0, $sms_count, $campaignName);
        $sdp_portal_username = config('app.sdp_portal_username');
        $userNames = array_fill(0, $sms_count, $sdp_portal_username);
        $sdp_response_uri = config('app.sdp_response_uri');
        $actionResponseURLs = array_fill(0,  $sms_count, $sdp_response_uri);
        $channels = array_fill(0,  $sms_count, $channel);
        $oas = $froms;
        $msisdns = $recipients;
        $uniqueIds = $message_ids;
        $bulk_unique_id = uniqid('bulk', true) . time();
        $bulk_ids = array_fill(0, $sms_count, $bulk_unique_id);
        return [$userNames, $channels, $packageIds, $oas, $msisdns, $messages, $uniqueIds, $actionResponseURLs, $credits, $project_ids, $froms,  $created_ats, $send_ats, $campaign_ids, $telcos, $message_ids, $tos, $statuses, $status_codes, $bulk_ids, $message_parts,  $types, $links, $costs, $campaignNames];
    }
    //save to database via an external process
    private function persistToDb($raw_db_smses)
    {
        $raw_db_smses = array_chunk($raw_db_smses, 1000);
        foreach ($raw_db_smses as $raw_db_sms) {
            //ProcessRecords::dispatch($raw_db_smsess)->onQueue('otp');   ALTERNATIVE
            $data_string = json_encode($raw_db_sms);
            $file_stream = fopen(base_path() . "/storage/logs/sms-to-persist.log", "a"); //or die("Unable to open file!");
            fwrite($file_stream, $data_string . "\n");
            fclose($file_stream);
            Log::info('1000 messages logged for saving');
        }
        return true;
    }
    public function sendToLogBroker($data)
    {
        $data_string = json_encode($data);
        $file_stream = fopen(base_path() . "/storage/logs/consumer-persist-http.log", "a"); //or die("Unable to open file!");
        fwrite($file_stream, $data_string . "\n");
        fclose($file_stream);
        return true;
    }
    public function dispatchMessages($safaricom_payload, $airtel_payload, $telkom_payload, $raw_sms, $raw_db_smses, $sendAt, $api = null)
    {
        $sdpShortener = function ($arr) {
            array_splice($arr, 8);
            return $arr;
        };
        $time_start = microtime(true);
        Log::info($sendAt);
        if (Carbon::now()->addMinutes(15)   >  Carbon::parse($sendAt)) {
            //save all messages before sending out
            $this->persistToDb($raw_db_smses);
            if (count($safaricom_payload) > 0) { //39rps
                Log::info('begin queueing safaricom loop in' . (string)(microtime(true) - $time_start));
                $raw_smsed = array_chunk($safaricom_payload, 400);
                foreach ($raw_smsed as $raw_sms) {
                    $sdp_smses =  array_map($sdpShortener, $raw_sms);
                    //$api ? $this->sendToLogBroker($sdp_smses) : ProcessMessage::dispatch($sdp_smses)->onQueue('otp');
                    $this->sendToLogBroker($sdp_smses);
                    //ProcessMessage::dispatch($sdp_smses)->onQueue('otp');
                }
                Log::info('end safaricom queue loop in' . (string)(microtime(true) - $time_start));
            }
            if (count($airtel_payload) > 0) {
                Log::info('begin queueing airtel loop in' . (string)(microtime(true) - $time_start));
                foreach ($airtel_payload as $raw_smses) {
                    ProcessSendairtelbulk::dispatch($raw_smses, 1)->onQueue('otp');
                }
                Log::info('end airtel queue loop in' . (string)(microtime(true) - $time_start));
            }
            if (count($telkom_payload) > 0) {
                Log::info('begin queueing telkom loop in' . (string)(microtime(true) - $time_start));
                foreach ($telkom_payload as $raw_smses) {
                    ProcessSendtelkombulk::dispatch($raw_smses, 1)->onQueue('high');
                }
                Log::info('end telkom queue loop in' . (string)(microtime(true) - $time_start));
            }
        } else { //que to schedule for sending later
            $chunkedMessages = array_chunk($raw_db_smses, 2000);
            foreach ($chunkedMessages as $messages) {
                Schedulesms::dispatch($messages)->onQueue('high');
                Log::info('sent 4k to schedule');
            }
        }
        return true;
    }
    public function objectToArray($object) {
        if (is_object($object)) {
            // If it's an object, convert it to an array recursively
            $object = (array)$object;
        }
        if (is_array($object)) {
            // If it's an array, convert its elements to arrays recursively
            return array_map(array($this, 'objectToArray'), $object);
            
        } else {
            // If it's neither an object nor an array, return it as is
            return $object;
        }
    }
    public function authTest(Request $request)
    {
        $request->validate(['apiKey' => 'required', 'project' => 'required']);
        $token = $request->apiKey ? $request->apiKey : $request->bearerToken();
        $project = Project::where(['api_token' => $token, 'name' => $request->project])->first();

        if ($project) {
            return response()->json(["messageData" => ["message" => "Success. Project found."]], 200);
        } else {
            return response()->json(["messageData" => ["message" => "Error. Project not found."]], 404);
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $id = null)
    {  
        try {
            $accessType = (!empty($request->apiKey) || !empty($request->bearerToken())) ? "API" : "WEB";
            Log::channel('attempts')->info('Attempt logged', [
                'request_data' => $request->all(),
                'access_type' => $accessType,
                'ip_address' => $request->ip(),
                'timestamp' => now()
            ]);
        } catch(Exception $e) {
            Log::channel('attempts')->error('Logging attempt failed', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString()
            ]);
        }

        $request->validate(['type' => 'required']);
        ini_set('memory_limit', '40960M'); // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode  //set_time_limit(800);//same as this
        ini_set('default_socket_timeout', '-1');
        if ($request->filled('project_id')) {
            $this->authorize('create', [Sms::class, $id]);
        }
        $request->validate(['recipients' => 'nullable', 'recipients_file' => 'nullable', 'channel' => 'nullable', 'message' => 'required']);
        $response_shortener = function ($arr) {
            $arr = array_slice($arr, 15, 7);
            return $arr;
        };
        $make_wzId = function () {
            return  bin2hex(random_bytes(20));
        };
        $project = $request->filled('project_id') ? Project::find($request->project_id) :
            Project::where('api_token', $request->apiKey ? $request->apiKey : $request->bearerToken())->where('name', 'like', $request->project . '%')->first();
        if ($project == null) {
            return response()->json(["messageData" => ["message" => "Error. Unknown project."]]); //ensure project is resolved
        }
        $project_id =  $project->id;
        //76rps
        $payments = Payment::where('project_id', $project->id)->where('balance', '>', 5)->where('status', 'like', 'success')->latest()->get();
        $campaignData = $this->getCampaignId($project->id, $request->campaign);
        $campaign_id = $campaignData[0];
        $campaignName = $campaignData[1];


        if ($project->account_type == "postpay") {
            echo "";
        } elseif ($payments->sum('balance') <  5) {
            Log::info('Project '.$project->name.' has insufficient funds');
            return response()->json(["messageData" => ["message" => "Error. Insufficient balance. Top up and try again."]]);
            exit;
        }
        $sendAt = $request->sendAt;
        $type = $request->type;
        $from = $this->resolveFromAddress($type, $request->from, $project->id);
        $froms_array = $this->processFromAddress($project->id, $from);
        $channel = 'sms';//will support interactive later
        $message = preg_replace('/[^A-Za-z0-9\-\s\/\}\{\^\|\(\)\+\<\>\[\]\$\"\':;\.=,&@\*#!%\?\_]/', '', $request->message);
        $message = preg_replace('/\R/', "\n", $message);
        //save template
        if ($request->filled('saveTemplate') && isset($request->saveTemplate)) {
            Log::info('saving template' . $request->saveTemplate);
            DB::table('templates')->insert(['name' => date('Y-m-d H:m'), 'project_id' => $project_id, 'message' => $message]);
        }
        $message  = $request->filled('optout') ?  $message . ' STOP*456*9*5#' : $message;
        $estimated_rate = $payments->first()->safaricom ? $payments->first()->safaricom : 0.3; //latest rate
        if (!empty($request->apiKey) || !empty($request->bearerToken())) {
            $api = true;
            $recipient_api_numbers = explode(",", $request->to);
            $clean_api_recipients = array_filter(array_map(array($this, 'cleanNumber'), $recipient_api_numbers)); //array_filter-If no callback is supplied, all empty entries of array will be removed
            $recipients = array_unique(array_map(array($this, 'e164Formatter254'), $clean_api_recipients));
            $recipients = $this->removeBannedNUmbers($from, $project_id, $recipients);
            $estimated_chars = strlen($message) + substr_count($message, "\n"); //message parts
            $message_parts = ceil($estimated_chars  / 160); //constant for sms
            $total_sms_units = $message_parts * count($recipients);
            $time_start = microtime(true);
            if ($project->account_type == "postpay") {
                echo "";
            } elseif (($payments->sum('balance') / $estimated_rate) < $total_sms_units) {
                return response()->json(["messageData" => ["message" => "Error. Insufficient balance. Top up and try again."]]);
            }
            $messages = array_fill(0, count($recipients), $message);
            $links = array_fill(0, count($recipients), '');
            $sms_count = count($recipients);
            $message_ids = array_map($make_wzId, $recipients);
            $payload = $this->setSmsPayload($project_id, $message_parts, $type, $froms_array, $recipients, $messages, $sendAt, $campaign_id, $links, $message_ids, $channel, $campaignName);
          
            if (!isset($payload)) {
                Mail::to("<EMAIL>")->cc("<EMAIL>")->queue(new AppNotification("Primary account balance depleted. Please check and reload", "Primary Account Depleted"));
                return response()->json(["messageData" => ["message" => "Error. A system error was encountered. Sozuri support has been notified of the same."]]);
            }
            $raw_smses = array_map(function ($userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from,  $created_at, $send_at,  $campaign_id, $telco, $message_id, $to, $status, $status_code, $bulk_id, $message_part, $type, $link) {
                return array_combine(
                    ['userName', 'channel', 'packageId', 'oa', 'msisdn', 'message', 'uniqueId', 'actionResponseURL', 'credits', 'project_id', 'from', 'created_at', 'send_at', 'campaign_id', 'telco', 'messageId', 'to', 'status', 'statusCode', 'bulkId', 'messagePart', 'type', 'link_id'],
                    [$userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link]
                );
            }, $payload[0], $payload[1], $payload[2], $payload[3], $payload[4], $payload[5], $payload[6], $payload[7], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15], $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22]);
            $raw_db_smses = array_map(function ($channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName) {
                return array_combine(
                    [
                        'channel', 'package_id', 'message', 'description', 'price', 'project_id', 'from',
                        'created_at', 'send_at', 'campaign_id', 'telco', 'message_id',
                        'to', 'status', 'status_code', 'bulk_id', 'message_part', 'type', 'uri', 'cost', 'campaign_name'
                    ],
                    [$channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName]
                );
            }, $payload[1], $payload[2], $payload[5], $payload[17], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15],  $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22], $payload[23],  $payload[24]);
            Log::info('raw sms array ready in' . (string)(microtime(true) - $time_start));
            $short_smses_response = array_map($response_shortener, $raw_smses);
            $safaricom_payload = $this->safaricomNumbers($raw_smses);
            $airtel_payload = $this->airtelNumbers($raw_smses);
            $telkom_payload = $this->telkomNumbers($raw_smses);
            $this->dispatchMessages($safaricom_payload, $airtel_payload, $telkom_payload, $raw_smses, $raw_db_smses, $request->sendAt, $api);
            echo json_encode(["messageData"  => ["messages" => $sms_count], "recipients" => $short_smses_response]); //echo is marginally faster than print
            exit; //response()->json(["messageData" => ["message" => "Success. Messages Accepted."]]);
        }

        if (isset($request->link) && filter_var($request->link, FILTER_VALIDATE_URL) !== FALSE) {
            $time_start = microtime(true);
            Log::info('analytical here');
            $link  = $request->link;
            $valid_file_recipients = $request->hasFile('recipients_file') ? $this->getFileRecipients($request->recipients_file) : [];
            $valid_list_recipients = $request->filled('recipient') ? $this->getListRecipients($request->recipient) : [];
            $valid_contact_recipients = $request->has('contactlists') ? $this->getContactGroupRecipients($request->contactlists, $project_id) : [];
            $recipients = array_unique(array_merge($valid_file_recipients, $valid_contact_recipients, $valid_list_recipients)); //lists if some are attached
            $recipients = $this->removeBannedNUmbers($from, $project_id, $recipients);
            $estimated_chars = strlen($message) + 24 + substr_count($message, "\n"); ////https://cli.ke/qwertyuq
            $message_parts = ceil($estimated_chars  / 160); //constant for sms
            $total_sms_units = $message_parts * count($recipients);
            if ($project->account_type == "postpay") {
                echo "";
            } elseif (($payments->sum('balance') / $estimated_rate) < ($message_parts * count($recipients))) {
                return response()->json(["messageData" => ["message" => "Error. Insufficient balance. Top up and try again."]]);
            }
            $messages = array_fill(0, count($recipients), $message);
            $message_ids = array_map($make_wzId, $recipients);
            $messages =   array_map(array($this, 'analytical_sms'), $messages, $message_ids, $recipients);
            $sms_count = count($recipients);
            $links = array_fill(0, $sms_count,  $link); //analytical
            $payload = $this->setSmsPayload($project_id, $message_parts, $type, $froms_array, $recipients, $messages, $sendAt, $campaign_id, $links, $message_ids, $channel, $campaignName);
            if (!isset($payload)) {
                Mail::to("<EMAIL>")->cc("<EMAIL>")->queue(new AppNotification("Primary account balance depleted. Please check and reload", "Primary Account Depleted"));
                return response()->json(["messageData" => ["message" => "Error. A system error was encountered. Sozuri support has been notified of the same. Contact ********** or use the chat icon to chat with us."]]);
            }
            $raw_smses =  array_map(function ($userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from,  $created_at, $send_at,  $campaign_id, $telco, $message_id, $to, $status, $status_code, $bulk_id, $message_part, $type, $link) {
                return array_combine(
                    ['userName', 'channel', 'packageId', 'oa', 'msisdn', 'message', 'uniqueId', 'actionResponseURL', 'credits', 'project_id', 'from', 'created_at', 'send_at', 'campaign_id', 'telco', 'messageId', 'to', 'status', 'statusCode', 'bulkId', 'messagePart', 'type', 'link_id'],
                    [$userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link]
                );
            }, $payload[0], $payload[1], $payload[2], $payload[3], $payload[4], $payload[5], $payload[6], $payload[7], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15], $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22]);
            $raw_db_smses = array_map(function ($channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName) {
                return array_combine(
                    [
                        'channel', 'package_id', 'message', 'description', 'price', 'project_id', 'from',
                        'created_at', 'send_at', 'campaign_id', 'telco', 'message_id',
                        'to', 'status', 'status_code', 'bulk_id', 'message_part', 'type', 'uri', 'cost', 'campaign_name'
                    ],
                    [$channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName]
                );
            }, $payload[1], $payload[2], $payload[5], $payload[17], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15],  $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22], $payload[23],  $payload[24]);
            Log::info('raw sms array ready in' . (string)(microtime(true) - $time_start));
            $safaricom_payload = $this->safaricomNumbers($raw_smses);
            $airtel_payload = $this->airtelNumbers($raw_smses);
            $telkom_payload = $this->telkomNumbers($raw_smses);
            $this->dispatchMessages($safaricom_payload, $airtel_payload, $telkom_payload, $raw_smses, $raw_db_smses, $request->sendAt);
            return response()->json(["messageData" => ["message" => "Success. Messages Accepted."]]);
            exit;
        }
        if ($request->filled('normal_message')) {
            $time_start = microtime(true);
            $valid_file_recipients = $request->hasFile('recipients_file') ? $this->getFileRecipients($request->recipients_file) : [];
            $valid_list_recipients = $request->filled('recipient') ? $this->getListRecipients($request->recipient) : [];
            $valid_contact_recipients = $request->has('contactlists') ? $this->getContactGroupRecipients($request->contactlists, $project_id) : [];
            $recipients = array_unique(array_merge($valid_file_recipients, $valid_contact_recipients, $valid_list_recipients)); //lists if some are attached
            $recipients = $this->removeBannedNUmbers($from, $project_id, $recipients);
            $estimated_chars = strlen($message) + substr_count($message, "\n"); //message parts
            $message_parts = ceil($estimated_chars  / 160); //constant for sms
            $total_sms_units = $message_parts * count($recipients);
            if ($project->account_type == "postpay") {
                echo "";
            } elseif (($payments->sum('balance') / $estimated_rate) < $total_sms_units) {
                Log::info('insufficient funcds for this project to sendsms');
                return response()->json(["messageData" => ["message" => "Error. Insufficient balance. Top up and try again."]]);
            }
            $messages = array_fill(0, count($recipients), $message);
            //if the message has custom variables readjust accordingly
            if(preg_match('/\{\{/',$message)){
                Log::info('bulk custom sms for project' . $project->name);
                if(!isset($request->contactlists)) {
                    Log::info('sending bulk custom failed');
                    return response()->json(["messageData" => ["message" => "Error. Failure loading group contacts for custom bulk sms. When you include contact parameters like {{fname}} in the message, ensure to ONLY include recipients from existing Contact Groups"]]);
                }
                //logic: process contact list recipients first
                $sms  = $message;
                $newArray = array();
                $hsm_sms = function ($array, $string) {
                    foreach (array_keys($array) as $keyName) { //the keys are the column values eg. name, bal 
                        $string = preg_replace('/\{\{' . $keyName . '\}\}/i', $array[$keyName], $string);  //replace the keys with their values in the sms
                    }
                    return ($string);
                };
                $contacts  = [];
                foreach ($request->contactlists  as $possible_lists => $value) {
                  $contacts = array_merge($contacts, DB::table('contacts')->where(['tag' => $value, 'project_id' => $project_id])
                  ->select(['mobile','fname','mname','lname','name','email','company'])->get()->toArray());
                }
                $contacts = $this->objectToArray($contacts);
                //remove duplicates
                $uniqueContacts = [];
                $mobileNumbers = [];

                foreach ($contacts as $contact) {
                    $mobile = $contact["mobile"];
                    if (!in_array($mobile, $mobileNumbers)) {
                        // If the mobile number is not already in the list of seen numbers, add the contact to uniqueContacts
                        $uniqueContacts[] = $contact;
                        $mobileNumbers[] = $mobile;
                    }
                }
                if(count($uniqueContacts) < 1 ) {
                    Log::info('sending bulk custom failed');
                    return response()->json(["messageData" => ["message" => "Error. Failure loading group contacts/messages for custom bulk sms."]]);
                }
                foreach ($uniqueContacts as $key => $contactArray) {
                    $newArray[$key] = array(
                        'number' => $contactArray['mobile'],  
                        'message' =>  $hsm_sms($contactArray, $sms)
                    );
                }
                $numbers =  array_column($newArray, 'number');
                $messages =  array_column($newArray, 'message');
                $clean_numbers = array_filter(array_map(array($this, 'cleanNumber'), $numbers));
                $recipients = array_map(array($this, 'e164Formatter254'), $clean_numbers);
            }

            $links = array_fill(0, count($recipients), '');
            $message_ids = array_map($make_wzId, $recipients);
            $payload = $this->setSmsPayload($project_id, $message_parts, $type, $froms_array, $recipients, $messages, $sendAt, $campaign_id, $links, $message_ids, $channel, $campaignName);
            if (!isset($payload)) {
                Mail::to("<EMAIL>")->cc("<EMAIL>")->queue(new AppNotification("Primary account balance depleted. Please check and reload", "Primary Account Depleted"));
                return response()->json(["messageData" => ["message" => "Error. A system error was encountered. Sozuri support has been notified of the same. Contact ********** or use the chat icon to chat with us."]]);
            }
            $raw_smses = array_map(function ($userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from,  $created_at, $send_at,  $campaign_id, $telco, $message_id, $to, $status, $status_code, $bulk_id, $message_part, $type, $link) {
                return array_combine(
                    ['userName', 'channel', 'packageId', 'oa', 'msisdn', 'message', 'uniqueId', 'actionResponseURL', 'credits', 'project_id', 'from', 'created_at', 'send_at', 'campaign_id', 'telco', 'messageId', 'to', 'status', 'statusCode', 'bulkId', 'messagePart', 'type', 'link_id'],
                    [$userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link]
                );
            }, $payload[0], $payload[1], $payload[2], $payload[3], $payload[4], $payload[5], $payload[6], $payload[7], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15], $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22]);
            $raw_db_smses = array_map(function ($channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName) {
                return array_combine(
                    [
                        'channel', 'package_id', 'message', 'description', 'price', 'project_id', 'from',
                        'created_at', 'send_at', 'campaign_id', 'telco', 'message_id',
                        'to', 'status', 'status_code', 'bulk_id', 'message_part', 'type', 'uri', 'cost', 'campaign_name'
                    ],
                    [$channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName]
                );
            }, $payload[1], $payload[2], $payload[5], $payload[17], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15],  $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22], $payload[23],  $payload[24]);
            
            Log::info('raw sms array ready in' . (string)(microtime(true) - $time_start));
            $safaricom_payload = $this->safaricomNumbers($raw_smses);
            $airtel_payload = $this->airtelNumbers($raw_smses);
            $telkom_payload = $this->telkomNumbers($raw_smses);
            $this->dispatchMessages($safaricom_payload, $airtel_payload, $telkom_payload, $raw_smses, $raw_db_smses, $request->sendAt);
            $request->session()->flash('message', 'All good! Messages accepted.');
            Log::info('Entire Processing took' . (string)(microtime(true) - $time_start));
            return response()->json(["messageData" => ["message" => "Success. Messages Accepted."]]);
        }

        if ($request->filled('hsm_message')) {
            $time_start = microtime(true);
            Log::info('hsm here');
            $message_array = json_decode($request->recipient_list, true);
            $numbers =  array_column($message_array, 'mobile');
            $recipient_count = count($numbers);
            $message_parts = 1.5;
            $total_sms_units = $message_parts * $recipient_count;
            if ($project->account_type == "postpay") {
                echo "";
            } elseif (($payments->sum('balance') / $estimated_rate) < ($message_parts * $recipient_count)) {
                return response()->json(["messageData" => ["message" => "Error. Insufficient balance. Top up and try again."]]);
            }
            $sms  = $message;
            $newArray = array();
            $hsm_sms = function ($array, $string) {
                foreach (array_keys($array) as $keyName) { //the keys are the column values eg. name, bal 
                    $string = preg_replace('/\{\{' . $keyName . '\}\}/i', $array[$keyName], $string);  //replace the keys with their values in the sms
                }
                return ($string);
            };
            foreach ($message_array as $key => $message) {
                $newArray[$key] = array(
                    'number' => $message['mobile'],   //'message' => $sms,
                    'message' =>  $hsm_sms($message, $sms)  //message is an array like 'mobile' => '+254725164293', 'name' => 'dav',  'email' => 'dav@', 'bal' => '90',
                );
            }
            $numbers =  array_column($newArray, 'number');
            $clean_numbers = array_filter(array_map(array($this, 'cleanNumber'), $numbers));
            $recipients = array_map(array($this, 'e164Formatter254'), $clean_numbers);
            $messages =  array_column($newArray, 'message');
            $links = array_fill(0, count($recipients), '');
            $message_ids = array_map($make_wzId, $recipients);
            $payload = $this->setSmsPayload($project_id, $message_parts, $type, $froms_array, $recipients, $messages, $sendAt, $campaign_id, $links, $message_ids, $channel, $campaignName);
            if (!isset($payload)) {
                Mail::to("<EMAIL>")->cc("<EMAIL>")->queue(new AppNotification("Primary account balance depleted. Please check and reload", "Primary Account Depleted"));
                return response()->json(["messageData" => ["message" => "Error. A system error was encountered. Sozuri support has been notified of the same. Contact ********** or use the chat icon to chat with us."]]);
            }
            $raw_smses =  array_map(function ($userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from,  $created_at, $send_at,  $campaign_id, $telco, $message_id, $to, $status, $status_code, $bulk_id, $message_part, $type, $link) {
                return array_combine(
                    ['userName', 'channel', 'packageId', 'oa', 'msisdn', 'message', 'uniqueId', 'actionResponseURL', 'credits', 'project_id', 'from', 'created_at', 'send_at', 'campaign_id', 'telco', 'messageId', 'to', 'status', 'statusCode', 'bulkId', 'messagePart', 'type', 'link_id'],
                    [$userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link]
                );
            }, $payload[0], $payload[1], $payload[2], $payload[3], $payload[4], $payload[5], $payload[6], $payload[7], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15], $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22]);
            $raw_db_smses = array_map(function ($channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName) {
                return array_combine(
                    [
                        'channel', 'package_id', 'message', 'description', 'price', 'project_id', 'from',
                        'created_at', 'send_at', 'campaign_id', 'telco', 'message_id',
                        'to', 'status', 'status_code', 'bulk_id', 'message_part', 'type', 'uri', 'cost', 'campaign_name'
                    ],
                    [$channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName]
                );
            }, $payload[1], $payload[2], $payload[5], $payload[17], $payload[8], $payload[9], $payload[10], $payload[11], $payload[12], $payload[13], $payload[14], $payload[15],  $payload[16], $payload[17], $payload[18], $payload[19], $payload[20], $payload[21], $payload[22], $payload[23],  $payload[24]);
            
            Log::info('raw sms array ready in' . (string)(microtime(true) - $time_start));
            $safaricom_payload = $this->safaricomNumbers($raw_smses);
            $airtel_payload = $this->airtelNumbers($raw_smses);
            $telkom_payload = $this->telkomNumbers($raw_smses);
            $this->dispatchMessages($safaricom_payload, $airtel_payload, $telkom_payload, $raw_smses, $raw_db_smses, $request->sendAt);
            return response()->json(["messageData" => ["message" => "Success. Messages Accepted."]]);
        }

    }




    
    public function trysms(Request $request)
    {
        Log::info('test sms');
        $to = $request->to;
        $message = 'Thank you for testing Sozuri SMS. Create a free Sozuri account to keep your customers close by sending them relevant promotion and transaction SMS.'; //update this
        $request->validate(['to' => 'required']);
        $recipients =  [$to];
        $clean_recipients = array_unique(array_filter(array_map(array($this, 'cleanNumber'), $recipients)));
        $formatted_recipients = array_map(array($this, 'e164Formatter254'), $clean_recipients);
        $recipient =   $formatted_recipients[0];
        Log::info($recipient);
        Log::info(DB::table('testtexts')->where('mobile', 'like', $recipient . '%')->count());
        if (DB::table('testtexts')->where('mobile', 'like', $recipient . '%')->count() > 4) {
            return response()->json(array('msg' => "You have Exceeded the number of Test SMS. <br>Create a Sozuri
            account  <a href='register'>HERE</a> :to send more", 'status' => "failed", 'to' => '9999'), 200);
        }

        DB::table('testtexts')->insert(['mobile' => $recipient, 'message' => $message]);
        $time = 'uptime-pingsoz: ' . date('Y-m-d h:i:s');
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://sozuri.net/api/v1/messaging',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_POSTFIELDS => "{\"project\": \"Devs\",
            \"from\": \"Sozuri\",
            \"campaign\": \"TEST SMS\",
            \"channel\": \"sms\",
            \"apiKey\": \"zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ\", 
            \"message\": \"$message\",
            \"type\": \"promotional\",
            \"to\": \"$recipient\"}",
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        Log::info($response);
        Log::info('TEST SMS');
        return response()->json(array('msg' => "Your Test SMS was successful. <br>Create a Sozuri
        account  <a href='register'>HERE</a> :to explore more", 'status' => "accepted", 'to' => '9999'), 200);
    }
    public function simulatesms(Request $request, $pid)
    {
        $request->validate(['to' => 'required|regex:/^[+]?[0-9]{9,13}$/', 'project_id' => 'required']);
        $this->authorize('create', [Sms::class, $request->project_id]);
        $project = Project::find($request->project_id);
        $payments = Payment::where('project_id', $project->id)->where('balance', '>', 0)->where('status', 'like', 'success')->get();
        $message = preg_replace('/[^A-Za-z0-9\-]/', '', $request->message);
        $to = explode(',', $request->to);
        $recipients =  $to;
        $clean_recipients = array_unique(array_filter(array_map(array($this, 'cleanNumber'), $recipients)));
        $formatted_recipients = array_map(array($this, 'e164Formatter254'), $clean_recipients);
        $recipient_count = count($formatted_recipients);
        $recipient =   implode(',', $formatted_recipients);
        $from = "Sozuri";
        if (($payments->sum('balance') / 0.78) <  $recipient_count * 0.78) {
            $request->session()->flash('message', 'Error: Insufficient balance. Kindly top up and retry. Go to the billing menu to see how to make payment');
            return back();
        } else {
            try{

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => url('/').'api/v1/messaging',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_POSTFIELDS => "{\"project\": \"$project->name\",
                    \"from\": \"$from\",
                    \"campaign\": \"Sandbox SMS\",
                    \"channel\": \"sms\",
                    \"apiKey\": \"$project->api_token\", 
                    \"message\": \"$message\",
                    \"type\": \"promotional\",
                    \"to\": \"$recipient\"}",
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json'
                    ),
                ));
    
                $response = curl_exec($curl);
                curl_close($curl);
                Log::info($response);
    
                $response = json_decode($response, true);
                Log::info($response);
                return response()->json(array(
                   /* 'msg' => $message,
                    'from' => $from,
                    'messageId' => $response["recipients"][0]['messageId'],
                    'to' => $response["recipients"][0]['to'],
                    'status' => $response["recipients"][0]['status'],
                    'statusCode' => $response["recipients"][0]['statusCode'],
                    'bulkId' => $response["recipients"][0]['bulkId'],
                    'messagePart' => $response["recipients"][0]['messagePart'],
                    'type' => $response["recipients"][0]['type'],*/
                    'count' => 1,
                    'time' => Carbon::now()
                ), 200);
            }catch(Exception $e) {
                Log::info($e->getMessage());
                return response()->json(array(
                    'msg' => $message,
                    'from' => $from,
                    'messageId' => null,
                    'to' => null,
                    'status' => 'error',
                    'statusCode' => '11',
                    'bulkId' => null,
                    'messagePart' => null,
                    'type' => 'promotional',
                    'count' => 1,
                    'time' => Carbon::now()
                ), 500);

            }

        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function show(Sms $sms)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function edit(Sms $sms)
    {
        $this->authorize('update', $sms);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Sms $sms)
    {
        $this->authorize('update', $sms);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Sms  $sms
     * @return \Illuminate\Http\Response
     */
    public function destroy(Sms $sms)
    {
        $this->authorize('delete', $sms);
    }

    protected function getToken()
    {
        $base_uri = config('app.sdp_base_uri');
        $sdp_username = config('app.sdp_username');
        $sdp_password = config('app.sdp_password');
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'auth/login', [
            'headers' => [
                'Content-Type'     => 'application/json',
                'Accept'     => 'application/json',
                'X-Requested-With'     => 'XMLHttpRequest',
            ],
            'json' => ['username' => $sdp_username, 'password' =>  $sdp_password]
        ]);
        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        $data = json_decode($stringBody, true);
        $token =  $data['token'];
        //Log::info('token is ' .$token);
        //$msg =  $data['msg'];
        $refreshToken =  $data['refreshToken'];
        return $token;
    }

    public function cleanPhoneNumber($phone)
    {
        $clean_number = preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
        return $clean_number;
    }
    public function verifyPhoneNumber($phone)
    {
        return preg_match("/[^[+]?[0-9]{7-13}$/", $phone) ? true : false;
    }
    public function arrayHasDuplicates(array $input_array)
    {
        return count($input_array) === count(array_flip($input_array));
    }
    public function checkExcel($file)
    {
        $allowedfileExtension = ['xlsx', 'xls', 'csv'];
        //$filename = $image->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        $check = in_array($extension, $allowedfileExtension);
        if ($check) {
            return true;
        } else {
            return false;
        }
    }
}
