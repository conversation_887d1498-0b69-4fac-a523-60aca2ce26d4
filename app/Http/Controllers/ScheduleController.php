<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Sms;
use App\Payment;
use App\Project;

class ScheduleController extends Controller
{
    //


    public function viewSchedule(Request $request, $pid)
    {
        $this->authorize('create', [Sms::class, $pid]);

        $project = Project::where('id', $pid)->first();

        return view('sms.scheduled', compact('project'));
    }
    public function deleteSingle(Request $request, $pid, $smsId)
    {
        $this->authorize('create', [Sms::class, $pid]);
        DB::table('scheduledsms')
            ->where(['message_id' => $smsId])
            ->delete();

        Log::info('schedule single sms deleted for project' . $pid . 'by' . $request->user()->id);
        return back()->with('message', 'Scheduled Message Deleted Successfully');
    }

    public function deleteSchedule(Request $request, $pid)
    {
        $request->validate(['bulk_id' => 'required']);
        $this->authorize('create', [Sms::class, $pid]);
        $time_start = microtime(true);
        ini_set('memory_limit', '20000M');
        ini_set('max_execution_time', 3600);
        ini_set('default_socket_timeout', '-1');
        Log::info('starting scheduler delete');
        $chunkSize = 4000;
        $bulkId = $request->bulk_id;
        if (!DB::table('scheduledsms')->where(['bulk_id' => $bulkId, 'project_id' => $pid])->exists()) {
            return back()->with('message', 'No Scheduled Messages Found');
        }
        $project = Project::where('id', $pid)->first();
        if ($project->account_type == "postpay") {
            Log::info('post pay project so no refund');
        } else {
            $priceTotal =  DB::table('scheduledsms')->where(['bulk_id' => $bulkId, 'project_id' => $pid])->sum('price');
            $payment = Payment::where(['project_id' => $pid])->first();
            $payment->increment('balance', $priceTotal);
            $payment->status = 'success';
            $payment->save();
            Log::info('schedule cancel refund of ' . $priceTotal . ' for project' . $project->name . 'by ' . $request->user()->email);
        }
        DB::table('scheduledsms')
            ->where(['bulk_id' => $bulkId, 'project_id' => $pid])
            ->orderBy('send_at')
            ->chunk($chunkSize, function ($records) {
                $recordIds = $records->pluck('id')->toArray();
                DB::table('scheduledsms')->whereIn('id', $recordIds)->delete();
            });

        //Mail user owning the project  ->send mail to default queue
        //$emailTo = User::where('id', Project::find($project_ids[0])->first()->user_id)->value('email');
        //  if ($emailTo) {
        //      $bcc = "<EMAIL>";
        //     $message = "Hi, Your scheduled SMS messages have been sent";
        //    $subject = "Schedules SMS have been Sent";
        //     Mail::to($emailTo)->bcc($bcc)->queue(new AppNotification($message, $subject));
        //}

        Log::info('schedule deleted for project' . $pid . 'by' . $request->user()->id);
        return back()->with('message', 'Scheduled Messages Deleted Successfully');
    }
}
