<?php

namespace App\Http\Controllers;
use App\Project;

use Illuminate\Http\Request;
use App\Sms;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use App\Jobs\ProcessSendbulk;
use App\Contact;
use App\Deposit;
use App\Campaign;
use Illuminate\Support\Facades\DB;
use Auth;
use App\Message;
use App\Imports\ContactsImport;
use AfricasTalking\SDK\AfricasTalking;
use App\Jobs\ProcessSms;
use Illuminate\Database\Eloquent\Builder;
use App\Enrollment;
use GuzzleHttp\Client;
class TestsmsController extends Controller
{
    //
    public function index($id) {
        //authorize project access
        $project = Project::find($id);
        return view('simulations.smssimulation', compact('project'));
    }


}
