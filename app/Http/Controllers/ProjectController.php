<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Auth;
use App\Project;
use App\Collaboration;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\User;
use App\Policies\ProjectPolicy;
use App\Talklog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use App\Paymentmethod;
use App\Paymentstatus;
use App\Contact;
use App\ContactList;
use App\Payment;
use App\Traits\CalculateCredits;
use App\Campaign;

class ProjectController extends Controller
{
    use CalculateCredits;
    public function archived()
    {
        $this->authorize('viewAny', Project::class);
        //$collabo_projects = DB::table('collaborations')->where('user_id', Auth::id())->select('project_id')->get();
        $projects = Auth::user()->projects()->where('isArchived', '=', 1)->latest()->get();
        // $archived_projects = Auth::user()->projects()->where('isArchived',false)->get();
        return view('projects.archived', compact('projects'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $this->authorize('viewAny', Project::class);
        //$collabo_projects = DB::table('collaborations')->where('user_id', Auth::id())->select('project_id')->get();
        $collabo_projects_ids = Collaboration::where('user_id', Auth::id())->where('status', '=', 1)->pluck('project_id')->toArray();
        $collabo_projects = DB::table('projects')->whereIn('id',  $collabo_projects_ids)->latest()->get();
        //doesnt apply to collabo ones. for collabo
        $projects = User::find(Auth::id())->projects()->where('isArchived', '!=', 1)->latest()->get();
        // $projects = DB::table('projects')->where('user_id',Auth::id())->where('isArchived','=',1)->latest()->get();
        //dd($projects->where('isArchived','=',0)->count()  );
        $archivedProjects = Auth::user()->projects()->where('isArchived', '=', 0)->latest()->get();
        return view('projects.index', compact('collabo_projects', 'projects', 'archivedProjects'));
    }

    public function archive(Request $request, $id)
    {
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $project->isArchived = $project->isArchived === 1 ? 0 : 1;
        $project->save();
        return redirect('projects')->with('status', 'Project Updated');
    }
    /**
     * Update the specified user.
     *
     * @param  Request  $request
     * @param  string  $id
     * @return Response
     */
    public function hatewords(Request $request, $id)
    {
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $project->hatewords = implode(',', explode('\r\n', $request->hatewords));
        $project->hatewordsActive = $request->filled('hatewordsActive') ? true : false;
        $project->save();
        return back()->with('status', 'Hate words updated');
    }

    public function balAlert(Request $request)
    {
        $project = Project::find($request->project_id);

        $this->authorize('update', $project);

        $request->validate([
            'project_id' => "required",
            'min_balance' => "required",
            'min_balance_tel' => 'required',
            'min_balance_email' => 'required'
        ]);
        $project->min_balance = $request->min_balance;
        $project->min_balance_tel = $request->min_balance_tel;
        $project->min_balance_email = $request->min_balance_email;
        $project->save();

        Log::info('User updated min_balance projects dashboard' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User updated min_balance" . Auth::id(),
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);
        //copy the log code/ add log and talklog classes to class, editcode to reflect resource
        $request->session()->flash('message', 'Min Balance updated');
        return back();
    }

    public function budget(Request $request)
    {
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $request->validate([
            'project_id' => "required",
            'max_credit' => "required",
        ]);
        $project->max_credit = $request->max_credit;
        $project->save();

        Log::info('User updated monthly budget for project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User updated min_balance" . Auth::id(),
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        //copy the log code/ add log and talklog classes to class, editcode to reflect resource

        $request->session()->flash('message', 'Monthly Budget updated');
        return back();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {

        $this->authorize('create');
        return view('projects.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $this->authorize('create');
        //copy the log code/ add log and talklog classes to class, editcode to reflect resource
        //$this->authorize('create', Project::class);
        if (Project::where('name', 'like', $request->name . "%")->exists()) {
            return back()->withErrors('That project name is already in use. Select a new name to make your project unique');
        }
        $amount = config('app.free_trial_amount');
        /*      $credits = $this->CalculateCredits($amount);//new project reward
        $manage_p_project = Project::where('name','primary_project')->first();
        if($manage_p_project->credits < $credits) {
           return back()->withErrors('Cannot create a project at the moment. Contact <EMAIL>');
        }
        $manage_p_project->credits -=  $credits;
        $manage_p_project->save();
*/
        $project = new Project();
        $project->fill($request->all());
        $project->user_id = Auth::id();
        $project->code = uniqid('Tzpr');
        $project->credits = 0; //$credits;
        $project->code = uniqid('Tzpr');
        $project->save();

        $payment = new payment();
        //$payment->transaction_id =  $transaction_id;
        $payment->txRef =  uniqid('P2O'); //unique internal ID
        $payment->project_id =  $project->id;
        $payment->resp_respmsg =  'Primary to Other';
        $payment->amount  =  $amount;
        $payment->balance  =  $amount;
        $payment->credit = "units";
        $payment->debit = "promo";
        $payment->unit_cost =  0;
        $payment->credits = $amount; //$credits;
        $payment->safaricom = $this->CalculateSafaricomCredits($amount);
        $payment->airtel = $this->CalculateAirtelCredits($amount);
        $payment->telkom = $this->CalculateTelkomCredits($amount);
        $payment->international = $this->CalculateInternationalCredits($amount);
        $payment->other = $this->CalculateOtherCredits($amount);
        $payment->package_id = null;
        $payment->date = Carbon::now();
        $payment->status  =  'success';
        $payment->tx_status  =  'completed';
        $payment->currency  =  'kes';
        $payment->detail  =  'Trial Reload';
        $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'trial' . '%')->exists() ?
            Paymentmethod::where('name', 'like', 'trial' . '%')->value('id') :
            Paymentmethod::insertGetId(['name' => 'trial', 'detail' => 'trial', 'created_by' => Auth::id(),]);
        $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'completed' . '%')->exists() ?
            Paymentstatus::where('name', 'like', 'completed' . '%')->value('id') :
            Paymentstatus::insertGetId(['name' => 'completed', 'detail' => 'completed', 'created_by' => Auth::id(),]);
        $payment->save();

        Log::info('User created a new project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User " . Auth::id() . " created a new project:" . $project->name,
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        $request->session()->flash('message', 'Project Created');
        return redirect('projects');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $all = DB::table('sms')->where('project_id', $project->id)->count();
        $safaricom_all =  DB::table('sms')->where('project_id', $project->id)->where('telco', 'like', "%safaricom%")->count();
        $airtel_all =  DB::table('sms')->where('project_id', $project->id)->where('telco', 'like', "%airtel%")->count();
        $all_today = DB::table('sms')->where('project_id', $project->id)->whereDate('created_at', Carbon::today())->count();
        return response()->json([
            'all' => $all,
            'safaricom_all' => $safaricom_all,
            'airtel_all' => $airtel_all,
            'all_today' => $all_today,
            'msg' => 'received'
        ]);
    }

    public function contacts(Request $request)
    {
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $opt_out = DB::table('contacts')->where('project_id', $project->id)->where('optedOut', 1)->count();
        $all_contacts = DB::table('contacts')->where('project_id', $project->id)->count();
        $all_contactlists = DB::table('contact_lists')->where('project_id', $project->id)->get();
        $contactlists =  $all_contactlists->count();
        //$ungrouped  = Contact::where('project_id', $project->id)->whereDoesntHave('contactlists')->count();
        $ungrouped  = Contact::where('project_id', $project->id)->whereNull('tag')->count();

        $groups = [];
        foreach( $all_contactlists as $contactlist) {
        //array_push( $groups, [$contactlist->name , DB::table('contact_contact_list')->where('contact_list_id',$contactlist->id)->count()] );
        array_push( $groups, [$contactlist->name , DB::table('contacts')->where('tag',$contactlist->id)->count()] );

               }   
        return response()->json([
            'opt_out' => $opt_out,
            'all_contacts' => $all_contacts,
            'groups' => $groups,
            'ungrouped' => $ungrouped,
            'msg' => 'received'
        ]);
    }

    public function success(Request $request)
    {
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $all = DB::table('sms')->where('project_id', $project->id)->count();
        $success = DB::table('sms')->where('project_id', $project->id)->whereIn('status', ['success','accepted','sent'])->count();
        $not_success = $all - $success;
        $safaricom_success = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%success%")->where('telco', 'like', "%safaricom%")->count();
        $airtel_success = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%success%")->where('telco', 'like', "%airtel%")->count();
        return response()->json([
            'success' => $success,
            'safaricom_success' => $safaricom_success,
            'airtel_success' => $airtel_success,
            'not_success' => $not_success,
            'msg' => 'success received'
        ]);
    }

    public function accepted(Request $request)
    {
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $accepted = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%accepted%")->count();
        $safaricom_accepted = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%accepted%")->where('telco', 'like', "%safaricom%")->count();
        $airtel_accepted = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%accepted%")->where('telco', 'like', "%airtel%")->count();
        return response()->json([
            'accepted' => $accepted,
            'safaricom_accepted' => $safaricom_accepted,
            'airtel_accepted' => $airtel_accepted,
            'msg' => 'accepted received'
        ]);
    }

    public function errors(Request $request)
    {
        ini_set('memory_limit', '40960M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $network_failure =  DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%network_failure%")->count();
        $unsupported =  DB::table('sms')->where('project_id', $project->id)->where('telco', 'like', "%unsupported%")->count();
        $delivery_impossible = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%delivery_impossible%")->count();
        $absent_subscriber =  DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%absent_subscriber%")->count();
        $unknown_error =  DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%unknown error%")->count();
        $blacklisted = DB::table('sms')->where('project_id', $project->id)->where('description', 'like', "%SenderName Blacklisted%")->count();
        $network_failure = DB::table('sms')->where('project_id', $project->id)->where('status', 'like', "%network_failure%")->count();
        $dlr_errors = DB::table('sms')->where('project_id', $project->id)->whereNotIn('status', ['success','accepted','sent'])->count();

        return response()->json(['dlr_errors' => $dlr_errors,
            'network_failure' => $network_failure,
            'unsupported' => $unsupported,
            'delivery_impossible' => $delivery_impossible,
            'absent_subscriber' => $absent_subscriber,
            'unknown_error' => $unknown_error,
            'blacklisted' => $blacklisted,
            'msg' => 'errors received'
        ]);
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function edit(Project $project)
    {
       // ini_set('memory_limit', '4096M');
        ini_set('memory_limit', '-1');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');

        $this->authorize('update', $project);
        $campaigns = Campaign::where('project_id', $project->id)->get();

        return view('projects.new-edit', compact('project', 'campaigns'));
    }

    public function analytics(Request $request, $id) {
         $project = Project::find($id);
        $this->authorize('update', $project);
        return view('analytic', compact('project'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Project $project)
    {
        $project = Project::find($request->project_id);
        if ($project->name == "primary_project") {
            return back();
        }
        $this->authorize('update', $project);
        $project->update($request->except('project_id'));

        Log::info('User updated a project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User " . Auth::id() . "updated a project" . $project->name,
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        $request->session()->flash('message', 'Project Updated');
        return back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $project = Project::find($request->project_id);
        $this->authorize('delete', $project);

        if ($project->name == "primary_project") {
            return back()->with('status', 'Action not Allowed');
        }
        $this->authorize('delete', $project);
        $project->delete();

        Log::info('User deleted a project' . Auth::id());
        $log = Talklog::create([
            'level' => 6,
            'resource' => "Talklog",
            'action' => "View",
            'message' => "User " . Auth::id() . "deleted a project" . $project->name,
            'size' => "Issue",
            'user_id' => Auth::id(),
            'clientIp' => $request->ip(),
            'clientRequestMethod' => $request->method(),
            'clientRequestPath' => $request->path(),
            'clientRequestUri' => $request->fullurl(),
            'ClientRequestUserAgent' => $request->header('User-Agent'),
            'status' => "Success"
        ]);

        $request->session()->flash('message', 'Project Deleted');
        return redirect('projects');
    }
}
