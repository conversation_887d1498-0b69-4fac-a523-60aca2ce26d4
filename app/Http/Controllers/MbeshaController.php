<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Payment;
use Carbon\Carbon;
use App\Paymentmethod;
use App\Paymentstatus;
use Illuminate\Support\Facades\Log;
use App\Project;
use Illuminate\Support\Facades\Auth;

class MbeshaController extends Controller
{
    public function getCredentials()
    {
        $secret = config('app.TALKZURI_Mbesha_s');
        $key = config('app.TALKZURI_Mbesha_k');
        $crede = base64_encode($key . ':' . $secret);
        Log::info($key);
        //get credentials from oauth
        $url = 'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Authorization: Basic ' . $crede)); //setting a custom header
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $curl_response = curl_exec($curl);
        Log::info($curl_response);
        $tokenresult =  json_decode($curl_response, true);
        $access_token = $tokenresult['access_token'];
        Log::info($access_token);
        return $access_token;
        /* {"access_token": "P4ppCmA6tsGRSFMfLHX6PDZPxiEA", "expires_in": "3599" }*/
    }
    public function stkpushrequestCallback()
    {
        // An accepted request
        // Retrieve the request's body
        /*

// A cancelled request
  {
    "Body":{
      "stkCallback":{
        "MerchantRequestID":"8555-67195-1",
        "CheckoutRequestID":"ws_CO_27072017151044001",
        "ResultCode":1032,
        "ResultDesc":"[STK_CB - ]Request cancelled by user"
      }
    }
  }

  // An accepted request
{
    "Body":{
      "stkCallback":{
        "MerchantRequestID":"19465-780693-1",
        "CheckoutRequestID":"ws_CO_27072017154747416",
        "ResultCode":0,
        "ResultDesc":"The service request is processed successfully.",
        "CallbackMetadata":{
          "Item":[
            {
              "Name":"Amount",
              "Value":1
            },
            {
              "Name":"MpesaReceiptNumber",
              "Value":"LGR7OWQX0R"
            },
            {
              "Name":"Balance"
            },
            {
              "Name":"TransactionDate",
              "Value":20170727154800
            },
            {
              "Name":"PhoneNumber",
              "Value":254721566839
            }
          ]
        }
      }
    }
}
*/
        $x = file_get_contents('php://input');
        Log::info("incoming stkpush payment callback");
        Log::info($x);
        $to_write = json_decode($x, true);
        // $myfile =  fopen("../data.txt", "a") or die("Unable to open file!");
        // fwrite($myfile, $x);
        //fclose($myfile);
        // $resp = json_decode($to_write);
        $body = $to_write['Body'];

        $resp = $to_write;
        $body = $resp['Body'];
        $stkCallback = $body['stkCallback'];
        $MerchantRequestID = $body['stkCallback']['MerchantRequestID'];
        $CheckoutRequestID = $body['stkCallback']['CheckoutRequestID'];
        $ResultCode = $body['stkCallback']['ResultCode'];
        $ResultDesc = $body['stkCallback']['ResultDesc'];

        if ($ResultCode == 0) {
            $CallbackMetadata = $body['stkCallback']['CallbackMetadata'];
            $Item = $body['stkCallback']['CallbackMetadata']['Item'];
            $Amount = $body['stkCallback']['CallbackMetadata']['Item'][0]['Value'];
            $MpesaReceiptNumber = $body['stkCallback']['CallbackMetadata']['Item'][1]['Value'];
            // TIP: you may still verify the transaction  before giving value.tx_status
            $payment = true; //Payment::where('txRef', '=', $MerchantRequestID)->where('tx_status', '=', 'new')->exists() ?
            if ($payment == null) {
                //exit();
            }
            //update payment
            return response()->json([
                'status' => 'Success'
            ]);
        } elseif ($ResultCode == 1032) {
            return response()->json([
                'status' => 'Fail'
            ]);
            //fail payment
        } else {
            return response()->json([
                'status' => 'Fail'
            ]);
        }
    }

    public function stkpushrequest(Request $request /*$mobile, $amount, $transaction_id, $project_id*/)
    {
        $mobile = $request->mobile;
        $amount = $request->amount;
        $transaction_id = $request->transaction_id;
        $callback = $request->callback;
        $project_id = 1;
        $PartyB = $request->PartyB;
        // dd($request->all());
        $timestamp = date('YmdHis'); //The timestamp of the transaction in the format yyyymmddhhiiss
        //$project = Project::find($project_id);
        $passkey = 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919';
        $pass = base64_encode($PartyB . $passkey . $timestamp);
        $tzcode = uniqid('sozujriReload') ;
        $url = "https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest";

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header
        $curl_post_data = array(
            'BusinessShortCode' =>  $PartyB, //'174379',//The organization shortcode used to receive the transaction.
            'Password' => $pass, //'Safcom145!', //the password used for encrypting the request sent: A base64 encoded string. (The base64 string is a combination of Short-code+Passkey+Timestamp)
            'Timestamp' =>  $timestamp,
            'TransactionType' => 'CustomerPayBillOnline',
            'Amount' => $amount,
            'PartyA' => $mobile, //use shortcode 1..initiator name and security credentials...174379         The MSISDN sending the funds.
            'PartyB' =>  $PartyB, //'174379',  //u The organization shortcode receiving the funds
            'PhoneNumber' =>  $mobile, //The MSISDN sending the funds.
            'CallBackURL' => $callback, //.
            'AccountReference' => $tzcode, //Used with M-Pesa PayBills.
            'TransactionDesc' => $transaction_id //'Talkzuri Recharge'//A description of the transaction.
        );
        Log::info($curl_post_data);

        $data_string = json_encode($curl_post_data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        Log::info($curl_response);
        $result = json_decode($curl_response, true);
        $err = curl_error($curl);
        return $curl_response;

        if ($err) {
            return back()->withErrors('an error was encountered' . $err);
            // die('Curl returned error: ' . $err);
        }

        if (!empty($result['errorCode'])) {
            return 'Error' . $result['errorMessage'];
        }
        if ($result['ResponseCode'] === "0") {
            /* $payment = new payment();
            $payment->transaction_id =  $transaction_id;
            $payment->txRef =  $result['MerchantRequestID']; //unique internal ID
            $payment->transaction_id =  $transaction_id;
            $payment->project_id =  $project_id;
            $payment->tx_customer_phone =  $mobile;
            $payment->tx_orderRef =   $result['CheckoutRequestID'];
            $payment->respcode =  $result['ResponseCode'];
            $payment->resp_respmsg =  $result['CustomerMessage'];
            $payment->amount  =  $amount;
            $payment->currency  =  'kes';
            $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'lipanampesa' . '%')->exists() ?
                Paymentmethod::where('name', 'like', 'lipanampesa' . '%')->value('id') :
                Paymentmethod::insertGetId(['name' => 'lipanampesa', 'detail' => 'lipanampesa', 'created_by' => Auth::id(),]);
            $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'new' . '%')->exists() ?
                Paymentstatus::where('name', 'like', 'new' . '%')->value('id') :
                Paymentstatus::insertGetId(['name' => 'new', 'detail' => 'new', 'created_by' => Auth::id(),]);
            $payment->save();*/
            return "Success".$curl_response;
        }
    }

    public function stkpushquery(Request $request)
    {
        $CheckoutRequestID = $request->CheckoutRequestID;
        $project_id = 1;
        $PartyB = $request->PartyB;
        // dd($request->all());
        $timestamp = date('YmdHis'); //The timestamp of the transaction in the format yyyymmddhhiiss
        //$project = Project::find($project_id);
        $passkey = 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919';
        $pass = base64_encode($PartyB . $passkey . $timestamp);
        $url = 'https://sandbox.safaricom.co.ke/mpesa/stkpushquery/v1/query';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header
        $curl_post_data = array(
            //Fill in the request parameters with valid values
            'BusinessShortCode' => $PartyB,
            'Password' => $pass,
            'Timestamp' =>  $timestamp,
            'CheckoutRequestID' =>  $CheckoutRequestID
        );
        $data_string = json_encode($curl_post_data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        Log::info($curl_response);
        return ($curl_response);
    }

    public function b2c(Request $request)
    {
        $PartyA = $request->PartyA;
        $PartyB = $request->PartyB;
        $amount = $request->amount;
        $InitiatorName = $request->InitiatorName;
        $callback = $request->callback;
        $plaintext = $request->plaintext; // $plaintext = "YOUR_PASSWORD";
        $publicKey = file_get_contents(base_path() . '/mbeshacert.cer'); //$publicKey = "PATH_TO_CERTICATE_FILE";
        openssl_public_encrypt($plaintext, $encrypted, $publicKey, OPENSSL_PKCS1_PADDING);

        $SecurityCredential = "P3XgZU9Y1EHl0Q/T7uIeLQ51wR1vmndijqss59N1HEiJJgk1MaSyZIgmurSjnFRCZGFSAAmmdxeDV39Bjw25cWocBMbiT2T9bfkAbQ4UgSZZYvQ5aSDJT8kOZG0GFYMzzmRbdPe9cb9SKi/CtH8HpI1x7JG47U6TkRZScxEU3khUoYb/4BYwsBgPJNKm2zelAWKveaEdw1+0sevVKsazY2GKHot8pbH5M7jQpneAT67S7HbKdn92wITWL0Pup4ySQj36F4xLXbWS+1IEKjCV9ancXC5ZmnawqIpEDrj5/qD6mam38s8fnCigLG7ugL1wLP8DIHoPdJXKw/skP3tpiw=="; // base64_encode($encrypted);

        $url = 'https://sandbox.safaricom.co.ke/mpesa/b2c/v1/paymentrequest';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header

        $curl_post_data = array(
            'InitiatorName' => $InitiatorName, //$InitiatorName, //initiator_1    John_Doe John Doe
            'SecurityCredential' => $SecurityCredential,
            'CommandID' => 'PromotionPayment',//'BusinessPayment',
            'Amount' => $amount,
            'PartyA' => $PartyA,
            'PartyB' => $PartyB,
            'Remarks' => 'Supplier ',
            'QueueTimeOutURL' => $callback, //'https://************:443/mbesha/b2cCallback',
            'ResultURL' => $callback, //'https://************:443/mbesha/b2cCallback',
            'Occasion' => 'BusinessPayment'
        );
        $data_string = json_encode($curl_post_data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        Log::info($curl_response);
        return ($curl_response);
        // echo $curl_response;
        //{ "ConversationID": "AG_20201002_00007b7e0bd3188f7071", "OriginatorConversationID": "9796-178083110-1", "ResponseCode":"0", "ResponseDescription": "Accept the service request successfully." }
    }


    public function b2cCallback()
    {
        $x = file_get_contents('php://input');
        Log::info("incoming b2c payment callback");
        Log::info($x);
        // Sample M-Pesa Core response received on the callback url.
        /*
{
    "Result":{
    "ResultType":0,
    "ResultCode":0,
    "ResultDesc":"The service request has been accepted successfully.",
    "OriginatorConversationID":"19455-424535-1",
    "ConversationID":"AG_20170717_00006be9c8b5cc46abb6",
    "TransactionID":"LGH3197RIB",
    "ResultParameters":{
      "ResultParameter":[
        {
          "Key":"TransactionReceipt",
          "Value":"LGH3197RIB"
        },
        {
          "Key":"TransactionAmount",
          "Value":8000
        },
        {
          "Key":"B2CWorkingAccountAvailableFunds",
          "Value":150000
        },
        {
          "Key":"B2CUtilityAccountAvailableFunds",
          "Value":133568
        },
        {
          "Key":"TransactionCompletedDateTime",
          "Value":"17.07.2017 10:54:57"
        },
        {
          "Key":"ReceiverPartyPublicName",
          "Value":"************ - John Doe"
        },
        {
          "Key":"B2CChargesPaidAccountAvailableFunds",
          "Value":0
        },
        {
          "Key":"B2CRecipientIsRegisteredCustomer",
          "Value":"Y"
        }
      ]
    },
    "ReferenceData":{
      "ReferenceItem":{
        "Key":"QueueTimeoutURL",
        "Value":"https://internalsandbox.safaricom.co.ke/mpesa/b2cresults/v1/submit"
      }
    }
  }
}
*/
    }

    public function b2b(Request $request)
    {
        $PartyA = $request->PartyA;
        $PartyB = $request->PartyB;
        $amount = $request->amount;
        $InitiatorName = $request->InitiatorName;
        $callback = $request->callback;
        $plaintext = $request->plaintext; // $plaintext = "YOUR_PASSWORD";
        $publicKey = file_get_contents(base_path() . '/mbeshacert.cer'); //$publicKey = "PATH_TO_CERTICATE_FILE";
        openssl_public_encrypt($plaintext, $encrypted, $publicKey, OPENSSL_PKCS1_PADDING);

        $SecurityCredential = "P3XgZU9Y1EHl0Q/T7uIeLQ51wR1vmndijqss59N1HEiJJgk1MaSyZIgmurSjnFRCZGFSAAmmdxeDV39Bjw25cWocBMbiT2T9bfkAbQ4UgSZZYvQ5aSDJT8kOZG0GFYMzzmRbdPe9cb9SKi/CtH8HpI1x7JG47U6TkRZScxEU3khUoYb/4BYwsBgPJNKm2zelAWKveaEdw1+0sevVKsazY2GKHot8pbH5M7jQpneAT67S7HbKdn92wITWL0Pup4ySQj36F4xLXbWS+1IEKjCV9ancXC5ZmnawqIpEDrj5/qD6mam38s8fnCigLG7ugL1wLP8DIHoPdJXKw/skP3tpiw=="; // base64_encode($encrypted);

        $url = 'https://sandbox.safaricom.co.ke/mpesa/b2b/v1/paymentrequest';

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ACCESS_TOKEN')); //setting custom header


        $curl_post_data = array(
            //Fill in the request parameters with valid values
            'Initiator' => ' ',
            'SecurityCredential' => ' ',
            'CommandID' => ' ',
            'SenderIdentifierType' => ' ',
            'RecieverIdentifierType' => ' ',
            'Amount' => ' ',
            'PartyA' => ' ',
            'PartyB' => ' ',
            'AccountReference' => ' ',
            'Remarks' => ' ',
            'QueueTimeOutURL' => 'http://your_timeout_url',
            'ResultURL' => 'http://your_result_url'
        );

        $data_string = json_encode($curl_post_data);

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);

        $curl_response = curl_exec($curl);
        print_r($curl_response);

        echo $curl_response;
    }
    public function b2bCallback()
    {
        $x = file_get_contents('php://input');
        Log::info("incoming b2b payment callback");
        Log::info($x);
        /*
 {
    "Result":{
      "ResultType":0,
      "ResultCode":0,
      "ResultDesc":"The service request has been accepted successfully.",
      "OriginatorConversationID":"8551-61996-3",
      "ConversationID":"AG_20170727_00006baee344f4ce0796",
      "TransactionID":"LGR519G2QV",
      "ResultParameters":{
        "ResultParameter":[
          {
            "Key":"InitiatorAccountCurrentBalance",
            "Value":"{ Amount={BasicAmount=46713.00, MinimumAmount=4671300, CurrencyCode=KES}}"
          },
          {
            "Key":"DebitAccountCurrentBalance",
            "Value":"{Amount={BasicAmount=46713.00, MinimumAmount=4671300, CurrencyCode=KES}}"
          },
          {
            "Key":"Amount",
            "Value":10
          },
          {
            "Key":"DebitPartyAffectedAccountBalance",
            "Value":"Working Account|KES|46713.00|46713.00|0.00|0.00"
          },
          {
            "Key":"TransCompletedTime",
            "Value":**************
          },
          {
            "Key":"DebitPartyCharges",
            "Value":"Business Pay Bill Charge|KES|77.00"
          },
          {
            "Key":"ReceiverPartyPublicName",
            "Value":"603094 - Safaricom3117"
          },
          {
            "Key":"Currency",
            "Value":"KES"
          }
        ]
      },
      "ReferenceData":{
        "ReferenceItem":[
          {
            "Key":"BillReferenceNumber",
            "Value":"aaa"
          },
          {
            "Key":"QueueTimeoutURL",
            "Value":"https://internalsandbox.safaricom.co.ke/mpesa/b2bresults/v1/submit"
          },
          {
            "Key":"Occasion"
          }
        ]
      }
    }
  }
  */
    }
    public function c2bregisterurl(Request $request)
    {
        $ShortCode = $request->ShortCode;
        $ConfirmationURL = $request->ConfirmationURL;
        $ValidationURL = $request->ValidationURL;
        $ResponseType = $request->ResponseType;
        $url = 'https://sandbox.safaricom.co.ke/mpesa/c2b/v1/registerurl';

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header
        $curl_post_data = array(
            'ShortCode' => $ShortCode,
            'ResponseType' => $ResponseType,
            'ConfirmationURL' => $ConfirmationURL,
            'ValidationURL' => $ValidationURL
        );

        $data_string = json_encode($curl_post_data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        return ($curl_response);
        // echo $curl_response;
        //{"Envelope":{"encodingStyle":"http:\/\/schemas.xmlsoap.org\/soap\/encoding\/","Body":{"Fault":{"faultcode":"soap:Server",
          //..........."faultstring":"Execution of ServiceCallout SCO-AddURLVault failed. Reason: ResponseCode 409 is treated as error","faultactor":{},"detail":{"source":{"errorcode":"steps.servicecallout.ExecutionFailed"}}}}}}
        //{"requestId": "16474-190635023-1","errorCode": "************", "errorMessage": "Service is temporarily not available.Please try again later."}
        //{ "requestId":"42412-6741487-1", "errorCode": "************", "errorMessage": "Service is temporarily not available.Please try again later." }

        // June 8th  
        //{ "requestId":"90151-27907298-1", "errorCode": "400.003.02", "errorMessage": "Bad Request - Invalid ValidationURL" }
        //{ "requestId":"9065-28371695-1", "errorCode": "400.003.02", "errorMessage": "Bad Request - Invalid ConfirmationURL" }

        //{ "ConversationID": "", "OriginatorCoversationID": "", "ResponseDescription": "success" }
    }

    public function c2bsimulate(Request $request)
    {
        $ShortCode = $request->ShortCode;
        $mobile = $request->mobile;
        $amount = $request->amount;
        $BillRefNumber = $request->BillRefNumber;
        $url = 'https://sandbox.safaricom.co.ke/mpesa/c2b/v1/simulate';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header
        $curl_post_data = array(
            'ShortCode' => $ShortCode, //	6 digit M-Pesa Till Number or PayBill Number
            'CommandID' => 'CustomerPayBillOnline',
            'Amount' => $amount,
            'Msisdn' => $mobile, //MSISDN (phone number) sending the transaction, start with country code without the plus(+) sign
            'BillRefNumber' =>  $BillRefNumber //Bill Reference Number (Optional).
        );
        $data_string = json_encode($curl_post_data);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        log::info($curl_response);
        return $curl_response;
        //{ "ConversationID": "AG_20201002_0000515f2083e1dff883", "OriginatorCoversationID": "9783-178365560-1", "ResponseDescription": "Accept the service request successfully." }
        //{ "requestId":"90156-27924361-1", "errorCode": "************", "errorMessage": "" }
        //{"Envelope":{"encodingStyle":"http:\/\/schemas.xmlsoap.org\/soap\/encoding\/","Body":{"Fault":{"faultcode":"soap:Server","faultstring":"Unresolved xpath string(\/SOAP-ENV:Envelope\/SOAP-ENV:Body\/ns0:response\/ns0:ResponseCode\/text()) in policy EV-SimulateResponse.","faultactor":{},"detail":{"source":{"errorcode":"steps.extractvariables.InvalidXPath"}}}}}}
    }

    public function c2bvalidate()
    {
        $x = file_get_contents('php://input');
        Log::info("incoming c2b payment validation callback");
        Log::info($x);
        /*// Validation Response
  {
    "TransactionType":"",
    "TransID":"LGR219G3EY",
    "TransTime":"**************",
    "TransAmount":"10.00",
    "BusinessShortCode":"600134",
    "BillRefNumber":"xyz",
    "InvoiceNumber":"",
    "OrgAccountBalance":"",
    "ThirdPartyTransID":"",
    "MSISDN":"************",
    "FirstName":"John",
    "MiddleName":"Doe",
    "LastName":""
  }
*/
    }
    public function c2bconfirm()
    {
        $x = file_get_contents('php://input');
        Log::info("incoming c2b payment confirmation callback");
        Log::info($x);

  //Confirmation Respose
  /*
  {
    "TransactionType":"",
    "TransID":"LGR219G3EY",
    "TransTime":"**************",
    "TransAmount":"10.00",
    "BusinessShortCode":"600134",
    "BillRefNumber":"xyz",
    "InvoiceNumber":"",
    "OrgAccountBalance":"49197.00",
    "ThirdPartyTransID":"**********",
    "MSISDN":"************",
    "FirstName":"John",
    "MiddleName":"",
    "LastName":""
  }*/
    }
    public function accountBalance(Request $request)
    {
      Log::info($request->all()) ;
        $url = 'https://sandbox.safaricom.co.ke/mpesa/accountbalance/v1/query';
        $SecurityCredential = "Safaricom3021#"; // base64_encode($encrypted);
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ' . $this->getCredentials())); //setting custom header
        $curl_post_data = array(
            //Fill in the request parameters with valid values 
            'Initiator' =>  $request->Initiator, //This is the credential/username used to authenticate the transaction request.
            'SecurityCredential' => $SecurityCredential, //Base64 encoded string of the M-Pesa short code and password, which is encrypted using M-Pesa public key and validates the transaction on M-Pesa Core system.
            'CommandID' => $request->AccountBalance, //	A unique command passed to the M-Pesa system.
            'PartyA' =>  $request->PartyA, //The shortcode of the organisation receiving the transaction.
            'IdentifierType' => '4', //	Type of the organisation receiving the transaction.
            'Remarks' => $request->Remarks, //Comments that are sent along with the transaction.
            'QueueTimeOutURL' =>  $request->QueueTimeOutURL, //The timeout end-point that receives a timeout message.
            'ResultURL' =>  $request->ResultURL //The end-point that receives a successful transaction.
        );

        $data_string = json_encode($curl_post_data);

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);

        $curl_response = curl_exec($curl);
        print_r($curl_response);

    }
    public function accountBalanceCallback()
    {
        /*"Result":{
      "ResultType":0,
      "ResultCode":0,
      "ResultDesc":"The service request has b een accepted successfully.",
      "OriginatorConversationID":"19464-802673-1",
      "ConversationID":"AG_20170728_0000589b6252f7f25488",
      "TransactionID":"LGS0000000",
      "ResultParameters":{
        "ResultParameter":[
          {
            "Key":"AccountBalance",
            "Value":"Working Account|KES|46713.00|46713.00|0.00|0.00&Float Account|KES|0.00|0.00|0.00|0.00&Utility Account|KES|49217.00|49217.00|0.00|0.00&Charges Paid Account|KES|-220.00|-220.00|0.00|0.00&Organization Settlement Account|KES|0.00|0.00|0.00|0.00"
          },
          {
            "Key":"BOCompletedTime",
            "Value":**************
          }
        ]
      },
      "ReferenceData":{
        "ReferenceItem":{
          "Key":"QueueTimeoutURL",
          "Value":"https://internalsandbox.safaricom.co.ke/mpesa/abresults/v1/submit"
        }
      }
    }
  }
        */
    }
    public function transactionStatus(Request $request)
    {
        $PartyA = $request->PartyA;
        $TransactionID = $request->TransactionID;
        $InitiatorName = $request->InitiatorName;
        $callback = $request->callback;
        $plaintext = $request->plaintext; // $plaintext = "YOUR_PASSWORD";
        $publicKey = file_get_contents(base_path() . '/mbeshacert.cer'); //$publicKey = "PATH_TO_CERTICATE_FILE";
        openssl_public_encrypt($plaintext, $encrypted, $publicKey, OPENSSL_PKCS1_PADDING);

        $SecurityCredential = "P3XgZU9Y1EHl0Q/T7uIeLQ51wR1vmndijqss59N1HEiJJgk1MaSyZIgmurSjnFRCZGFSAAmmdxeDV39Bjw25cWocBMbiT2T9bfkAbQ4UgSZZYvQ5aSDJT8kOZG0GFYMzzmRbdPe9cb9SKi/CtH8HpI1x7JG47U6TkRZScxEU3khUoYb/4BYwsBgPJNKm2zelAWKveaEdw1+0sevVKsazY2GKHot8pbH5M7jQpneAT67S7HbKdn92wITWL0Pup4ySQj36F4xLXbWS+1IEKjCV9ancXC5ZmnawqIpEDrj5/qD6mam38s8fnCigLG7ugL1wLP8DIHoPdJXKw/skP3tpiw=="; // base64_encode($encrypted);

        //checks the status of a B2B, B2C and C2B APIs transactions.
        $url = 'https://sandbox.safaricom.co.ke/mpesa/transactionstatus/v1/query';

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer '. $this->getCredentials())); //setting custom header


        $curl_post_data = array(
            //Fill in the request parameters with valid values
            'Initiator' => $InitiatorName,
            'SecurityCredential' => $SecurityCredential, //Base64 encoded string of the M-Pesa short code and password, which is encrypted using M-Pesa public key and validates the transaction on M-Pesa Core system.
            'CommandID' => 'TransactionStatusQuery',
            'TransactionID' => $TransactionID, //	Organization Receiving the funds.
            'PartyA' => $PartyA,
            'IdentifierType' => '1',
            'ResultURL' => $callback, //The path that stores information of transaction.
            'QueueTimeOutURL' => $callback,
            'Remarks' => 'Remark',
            'Occasion' => 'Occassion' //Optional
        );

        $data_string = json_encode($curl_post_data);

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);
        $curl_response = curl_exec($curl);
        print_r($curl_response);
        echo $curl_response;
    }
    public function transactionStatusCallback()
    {
        /*
        {
    "Result":{
      "ResultType":0,
      "ResultCode":0,
      "ResultDesc":"The service request has been accepted successfully.",
      "OriginatorConversationID":"10816-694520-2",
      "ConversationID":"AG_20170727_000059c52529a8e080bd",
      "TransactionID":"LGR0000000",
      "ResultParameters":{
        "ResultParameter":[
          {
            "Key":"ReceiptNo",
            "Value":"LGR919G2AV"
          },
          {
            "Key":"Conversation ID",
            "Value":"AG_20170727_00004492b1b6d0078fbe"
          },
          {
            "Key":"FinalisedTime",
            "Value":**************
          },
          {
            "Key":"Amount",
            "Value":10
          },
          {
            "Key":"TransactionStatus",
            "Value":"Completed"
          },
          {
            "Key":"ReasonType",
            "Value":"Salary Payment via API"
          },
          {
            "Key":"TransactionReason"
          },
          {
            "Key":"DebitPartyCharges",
            "Value":"Fee For B2C Payment|KES|33.00"
          },
          {
            "Key":"DebitAccountType",
            "Value":"Utility Account"
          },
          {
            "Key":"InitiatedTime",
            "Value":**************
          },
          {
            "Key":"Originator Conversation ID",
            "Value":"19455-773836-1"
          },
          {
            "Key":"CreditPartyName",
            "Value":"************ - John Doe"
          },
          {
            "Key":"DebitPartyName",
            "Value":"600134 - Safaricom157"
          }
        ]
      },
      "ReferenceData":{
        "ReferenceItem":{
          "Key":"Occasion",
          "Value":"aaaa"
        }
      }
    }
  }
*/
    }
    public function reversal()
    {
        //Reverses a B2B, B2C or C2B M-Pesa transaction.
        $url = 'https://sandbox.safaricom.co.ke/mpesa/reversal/v1/request';

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Authorization:Bearer ACCESS_TOKEN')); //setting custom header

        $curl_post_data = array(
            //Fill in the request parameters with valid values
            'CommandID' => ' ',
            'Initiator' => ' ',
            'SecurityCredential' => ' ',
            'CommandID' => 'TransactionReversal',
            'TransactionID' => ' ',
            'Amount' => ' ',
            'ReceiverParty' => ' ',
            'RecieverIdentifierType' => '4', //  1	MSISDN   2	Till Number  4	Shortcode
            'ResultURL' => 'https://ip_address:port/result_url',
            'QueueTimeOutURL' => 'https://ip_address:port/timeout_url',
            'Remarks' => ' ',
            'Occasion' => ' '
        );

        $data_string = json_encode($curl_post_data);

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data_string);

        $curl_response = curl_exec($curl);
        print_r($curl_response);

        echo $curl_response;
    }
    public function reversalCallback()
    {
        /*
{
    "Result":{
      "ResultType":0,
      "ResultCode":0,
      "ResultDesc":"The service request has been accepted successfully.",
      "OriginatorConversationID":"10819-695089-1",
      "ConversationID":"AG_20170727_00004efadacd98a01d15",
      "TransactionID":"LGR019G3J2",
      "ReferenceData":{
        "ReferenceItem":{
          "Key":"QueueTimeoutURL",
          "Value":"https://internalsandbox.safaricom.co.ke/mpesa/reversalresults/v1/submit"
        }
      }
    }
}
*/
    }

    /*
0	Success
1	Insufficient Funds
2	Less Than Minimum Transaction Value
3	More Than Maximum Transaction Value
4	Would Exceed Daily Transfer Limit
5	Would Exceed Minimum Balance
6	Unresolved Primary Party
7	Unresolved Receiver Party
8	Would Exceed Maxiumum Balance
11	Debit Account Invalid
12	Credit Account Invaliud
13	Unresolved Debit Account
14	Unresolved Credit Account
15	Duplicate Detected
17	Internal Failure
20	Unresolved Initiator
26	Traffic blocking condition in place


M-Pesa Response Codes (from client back to gateway)
Response codes are sent from the clients endpoints back to the gateway. This is done to acknowledge that the client has received the results.

Result Code	Description
0	Success (for C2B)
********	Success (For APIs that are not C2B)
1 or any other number	Rejecting the transaction


Test Credentials
To facilitate testing of sandbox apps, we have provided test credentials consisting of:

Test Shortcodes
Initiator Name
Security Credential
Test MSISDN
Lipa Na M-Pesa Online Shortcode
Lipa Na M-Pesa Online Passkey

The initiator name and security credentials are associated with Shortcode 1.

For M-Pesa APIs that have ‘PartyA’ in their request parameters, use Shortcode 1; Shortcode 2 will be used for ‘PartyB’.

Lipa Na M-Pesa Online Shortcode’ is a production shortcode

Lipa Na M-Pesa Online Passkey’ is used to create a password for use when making a Lipa Na M-Pesa Online Payment API call. \
The password is generated by base64 encoding the passkey, shortcode and timestamp, the resultant string from this encoding is the password.


Command ID	Description
TransactionReversal	Reversal for an erroneous C2B transaction.
SalaryPayment	Used to send money from an employer to employees e.g. salaries
BusinessPayment	Used to send money from business to customer e.g. refunds
PromotionPayment	Used to send money when promotions take place e.g. raffle winners
AccountBalance	Used to check the balance in a paybill/buy goods account (includes utility, MMF, Merchant, Charges paid account).
CustomerPayBillOnline	Used to simulate a transaction taking place in the case of C2B Simulate Transaction or to initiate a transaction on behalf of the customer (STK Push).
TransactionStatusQuery	Used to query the details of a transaction.
CheckIdentity	Similar to STK push, uses M-Pesa PIN as a service.
BusinessPayBill	Sending funds from one paybill to another paybill
BusinessBuyGoods	sending funds from buy goods to another buy goods.
DisburseFundsToBusiness	Transfer of funds from utility to MMF account.
BusinessToBusinessTransfer	Transferring funds from one paybills MMF to another paybills MMF account.
BusinessTransferFromMMFToUtility	Transferring funds from paybills MMF to another paybills utility account.
*/
}
