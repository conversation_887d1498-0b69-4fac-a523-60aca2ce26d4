<?php

namespace App\Http\Controllers;

use App\Models\SmsCopy;
use Illuminate\Http\Request;
use App\Sms;
use Carbon\Carbon;
use Log;
class ReportController extends Controller
{
    public function getSmsChartData($pid)
    {
        // Define the start and end dates for each month
        $startOfYear = Carbon::now()->startOfYear();
        $endOfYear = Carbon::now()->endOfYear();
        $projectId = base64_decode($pid);
        Log::info($pid);

        $data = SmsCopy::where('project_id', $projectId)->whereBetween('created_at', [$startOfYear, $endOfYear])
            ->selectRaw('MONTH(created_at) as month, status, COUNT(*) as count')
            ->groupBy('month', 'status')
            ->get()
            ->groupBy('status');

        Log::info('fetched sms chart data '.$data->count());

        // Initialize an array with default values
        $chartData = [
            'series' => [
                ['name' => 'Success', 'data' => array_fill(0, 12, 0)],
                ['name' => 'Submitted/sent', 'data' => array_fill(0, 12, 0)],
                ['name' => 'Opted Out', 'data' => array_fill(0, 12, 0)],
                ['name' => 'Error', 'data' => array_fill(0, 12, 0)],
            ],
            'xaxis' => [
                'categories' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            ],
        ];

        // Fill the chart data with actual counts
        foreach ($data as $status => $monthlyData) {
            foreach ($monthlyData as $record) {
                $monthIndex = $record->month - 1; // Convert to zero-based index
                switch ($status) {
                    case 'success':
                        $chartData['series'][0]['data'][$monthIndex] = $record->count;
                        break;
                    case 'sent':
                        $chartData['series'][1]['data'][$monthIndex] = $record->count;
                        break;
                    case 'unknown_error':
                        $chartData['series'][2]['data'][$monthIndex] = $record->count;
                        break;
                    case 'other':
                        $chartData['series'][3]['data'][$monthIndex] = $record->count;
                        break;
                }
            }
        }

        return response()->json($chartData);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
