<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreInteractiveSmsRequest;
use App\Http\Requests\UpdateInteractiveSmsRequest;
use App\Models\InteractiveSms;

class InteractiveSmsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreInteractiveSmsRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreInteractiveSmsRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\InteractiveSms  $interactiveSms
     * @return \Illuminate\Http\Response
     */
    public function show(InteractiveSms $interactiveSms)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\InteractiveSms  $interactiveSms
     * @return \Illuminate\Http\Response
     */
    public function edit(InteractiveSms $interactiveSms)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateInteractiveSmsRequest  $request
     * @param  \App\Models\InteractiveSms  $interactiveSms
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateInteractiveSmsRequest $request, InteractiveSms $interactiveSms)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\InteractiveSms  $interactiveSms
     * @return \Illuminate\Http\Response
     */
    public function destroy(InteractiveSms $interactiveSms)
    {
        //
    }
}
