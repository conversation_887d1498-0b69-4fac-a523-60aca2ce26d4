<?php

namespace App\Http\Controllers;

use App\Waccount;
use App\Wbusiness;
use App\Wtemplate;
use App\Wprofilepic;
use App\Wnumber;
use App\Wwebhook;
use App\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use App\Mail\AppNotification;
use Illuminate\Support\Facades\Mail;

class WaccountController extends Controller
{
    public function checkAllowedType($file)
    {
        $allowedfileExtension = ['jpg', 'jpeg', 'png'];
        $filename = $file->getClientOriginalName();
        $extension = strtolower($file->getClientOriginalExtension());
        $check = in_array($extension, $allowedfileExtension);
        $size = $file('file')->getSize();    //limit file size
        if ($check) {
            return true;
        } else {
            return false;
        }
    }
    private function cleanNumber($phone)
    {
        // return  preg_replace("/[^0-9+]/", "", (preg_replace("/(?<!^)\+/", "", $phone)));
        return  preg_replace("/[^0-9]/", "", $phone);
    }
    private function e164Formatter254($phone)
    {
        $forced_numbber = substr($phone, -9);
        return '254' . $forced_numbber;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $id)
    {
        $project = Project::find($id);
        $this->authorize('update', $project);
        $wbusiness = $wnumber = $wprofile = $wwebhook = $wtemplates = null;
        $waccount = Waccount::where('project_id', $project->id)/*->whereNotNull('uid')->whereNotNull('token')->latest()*/->first();
        if ($waccount !== null) {
            $wbusiness = Wbusiness::where('project_id', $project->id)->where('waccount_id', $waccount->id)->first();
            $wnumber = Wnumber::where('project_id', $project->id)->where('waccount_id', $waccount->id)/*->whereNotNull('uid')*/->latest()->first();
            $wprofile = Wprofilepic::where('project_id', $project->id)->where('waccount_id', $waccount->id)->whereNotNull('uid')->first();
            $wwebhook = Wwebhook::where('project_id', $project->id)->where('waccount_id', $waccount->id)->first();
            $wtemplates = Wtemplate::where('project_id', $project->id)->where('waccount_id', $waccount->id)->get();
        }
        return view('whatsapp.settings', compact('project', 'waccount', 'wbusiness', 'wprofile', 'wwebhook', 'wnumber', 'wtemplates'));
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'string|max:255',
            'wnumber' => 'nullable|string',
            'wprofilepic' => 'file|max:1048|mimes:jpeg,png',
        ]);
        // $file = $request->file('wprofilepic');
        //  $isAllowed = $this->checkAllowedType($file);
        // $filePath = $file->getPathName();
        //  $file_to_send =  file_get_contents($filePath);
        //file_get_contents($file->getPath());
        //'email' => 'string|email|max:255|unique:users']);
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        //check if 
        $http_verb =   Waccount::where('project_id', $project->id)/*->where('name', $request->name)->whereNotNull('uid')->whereNotNull('token')*/->exists() ? 'PATCH' : 'POST';
        //create local account
        $account =   $http_verb == 'PATCH' ?  Waccount::where('project_id', $project->id)/*->where('name', $request->name)->whereNotNull('uid')->whereNotNull('token')*/->first() : new Waccount();

        $account->project_id = $request->project_id;
        //$account->uid = null;   ...dont create, it is updated by successful response
        $account->name = $request->name;
        $account->code = uniqid();
        $account->user_id = Auth::user()->id;
        $account->status = 'new';
        $account->token = null;
        $account->is_parent = false;
        $account->account_type = null;
        $account->credit_balance = 0;
        $account->sozuri_credit_balance = 0;
        $account->auto_recharge = null;
        $account->active = false;
        $account->archived = false;
        $account->created_by = Auth::user()->id;
        $account->updated_by = Auth::user()->id;
        $account->save();
        Log::info('account locally saved');
        /*      
        $username = config('app.karix_account_uid');
        $password = config('app.karix_account_token');
        $base_uri = config('app.karix_base_uri'); //'https://api.karix.io/';
        $path = $http_verb == 'PATCH' ? 'account/' . $account->uid . '/' : 'account';
        $credentials = base64_encode($username . ':' . $password);
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request($http_verb,  $path, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . $credentials,
                //'X-Authorization' => 'Bearer ' . $this->token,
                //'X-Requested-With' => 'XMLHttpRequest'
                'api-version' =>  "2.0"
            ],
            'json' => [
                "name" => $account->name,
                "status" => 'enabled',
            ]
        ]);
        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('account ' . $http_verb .  $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $data = json_decode($stringBody, true);
        $account->uid =  $data['data']['uid']; //very important and can be referenced by other incoming requests
        $account->status = $data['data']['status'];
        $account->token = $data['data']['token'];
        $account->account_type = $data['data']['account_type'];
        $account->credit_balance = $data['data']['credit_balance'];
        $account->auto_recharge = $data['data']['auto_recharge'];
        $account->updated_at = Carbon::now(); //$data['data']['updated_time'];
        $account->is_parent = $data['data']['is_parent'];
        */
        $account->save();
        Log::info('creating webhook');
        $account_webhook_id =    $request->filled('wwebhook') ?  $this->createWwebhook($project->id, $account->name, $request->wwebhook, $account->id) : null;
        Log::info('created webhook');
        Log::info('creating number');
        //create webhook
        $wnumber = $this->cleanNumber($request->wnumber);
        $account_number = $request->filled('wnumber') ? $this->createWnumber($project->id, $wnumber, $account->id, $account_webhook_id, $request->uid, $request->code) : null; //$project_id,$name,$url, $waccount_id,$wnumber_id ) {
        Log::info('created number');
        //upload wprofile
        if (0 && $request->hasFile('wprofilepic') && $account_number !== null) { //this never runs
            $file = $request->file('wprofilepic');
            $isAllowed = $this->checkAllowedType($file);
            if (!$isAllowed) {
                $request->session()->flash('message', 'You appear to have uploaded unallowed types. Use PNG or JPG only');
                //return back();
            } else {
                $account_profilepic = $request->filled('wprofilepic') ?  $this->createWprofilepic($project->id, $account->id,  $account_number->number, $file) : null;
            }
        }
        $request->session()->flash('message', 'Whatsapp Account Saved. We will contact you with further details');
        $to = [['email' => "<EMAIL>", 'name' => "Sozuri Support"]];
        $cc = [
            ['email' => '<EMAIL>', 'name' => "Sozuri Admin Lawr3"],
            ['email' => "<EMAIL>", 'name' => "Sozuri Admin Davido"],
            ['email' => "<EMAIL>", 'name' => "Sozuri Marketing"],
            ['email' => "<EMAIL>", 'name' => "Sozuri BUsiness"],
        ];
        $msg = "Whatsapp details updated for account" . $wnumber;
        Mail::to($to)->cc($cc)->send(new AppNotification($msg, ""));
        return redirect('/projects/' . $project->id . '/chat');
    }
    public function createWwebhook($project_id, $name, $url, $waccount_id)
    {
        //validate url and name
        $project = Project::find($project_id);
        $this->authorize('update', $project);
        $http_verb =   Wwebhook::where('project_id', $project->id)->where('waccount_id', $waccount_id)->exists() ? 'PATCH' : 'POST';
        $account = Waccount::where('id', $waccount_id)->first(); //expected misbehavior
        $webhook =    $http_verb == 'PATCH' ?  Wwebhook::where('project_id', $project->id)->first() : new Wwebhook();
        $webhook->project_id = $project_id;
        //$webhook->uid ;
        $webhook->name = $name;
        $webhook->url =  $url;
        $webhook->code = uniqid();
        $webhook->waccount_id = $waccount_id;
        $webhook->wnumber_id = null;
        $webhook->status = 'new';
        $webhook->active = false;
        $webhook->archived = false;
        $webhook->created_by = Auth::user()->id;
        $webhook->updated_by = Auth::user()->id;
        $webhook->save();
        Log::info('webhook saved');
        return $webhook->id;
    }
    public function createWbusiness(Request $request)
    {
        if ($request->wnumber == null) {
            return back()->withErrors('Saving Business information requires an ACTIVE Whatsapp number. Contact us to get your number ACTIVE.');
        }
        $request->validate(['project_id' => 'required|numeric', 'wnumber' => 'required', 'about_text' => 'required', 'waccount_id' => 'required']);
        $waccount = Waccount::where('id', $request->waccount_id)->first();
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        $business =   Wbusiness::where('project_id', $project->id)->exists() ?  Wbusiness::where('project_id', $project->id)->first() : new Wbusiness();
        $business->project_id = $project->id;
        $business->uid = null;
        $business->name = $request->name;
        $business->email = $request->email;
        $business->code = uniqid();
        $business->address = $request->address;
        $business->description = $request->description;
        $business->vertical = $request->category;
        $business->category = $request->category;
        $business->waccount_id = $request->waccount_id;
        $business->wnumber_id = Wnumber::where('number', $request->wnumber)->first()->id;
        $business->wprofilepic_id = null;
        $business->websites = $request->websites;
        $business->about_text = $request->about_text ? $this->createAboutText($waccount, $request->wnumber, $request->about_text) : 'Hey There I am Using Whatsapp Biz';
        $business->opening_hours = null;
        $business->optin_keywords = null;
        $business->optout_keywords = $request->optout_keywords;
        $business->touchpoint_url = null;
        $business->supported_countries = null;
        $business->autoresponse_start =  null;
        $business->autoresponse_idle =  null;
        $business->autoresponse_toend =  null;
        $business->autoresponse_end = null;
        $business->auto_assign = null;
        $business->company_cert = null;
        $business->tax_cert = null;
        $business->national_id = null;
        $business->application = null;;
        $business->facebook_id = $request->facebook_id;
        $business->status = 'new';
        $business->rejected_reason = null;
        $business->active = false;
        $business->archived = false;
        $business->created_by = Auth::user()->id;
        $business->updated_by = Auth::user()->id;
        $business->save();
        $username = config('app.karix_account_uid');
        $password = config('app.karix_account_token');
        $base_uri = config('app.karix_base_uri'); //'https://api.karix.io/';
        $wnumber = preg_replace('[\+]', '', $request->wnumber);
        $path = 'whatsapp/profile/business/' . $wnumber . '/';
        $credentials = base64_encode($username . ':' . $password);
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('PATCH',  $path, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . $credentials,
                //'X-Authorization' => 'Bearer ' . $this->token,
                //'X-Requested-With' => 'XMLHttpRequest'
                'api-version' =>  "2.0"
            ],
            'json' => [
                "email" => $business->email,
                "address" => $business->address,
                "description" => $business->description,
                "vertical" => $business->vertical,
                "websites" => [$business->websites],
            ]
        ]);
        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('account business info ' .  $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $request->session()->flash('message', 'Whatsapp Business Updated successfully');
        $to = [['email' => "<EMAIL>", 'name' => "Sozuri Admins"]];
        $cc = [
            ['email' => '<EMAIL>', 'name' => "Sozuri Admin Lawr3"],
            ['email' => "<EMAIL>", 'name' => "Sozuri Admin Davido"],
        ];
        $message = "Whatsapp business info updated for account" . $business->name;
        Mail::to($to)->cc($cc)->send(new AppNotification($message, ""));
        return redirect('/projects/' . $project->id . '/chat');
    }
    //we will create numbers manually, why?
    public function createWnumber($project_id, $wnumber, $waccount_id, $webhook_id, $uid = null, $code)
    {
        $project = Project::find($project_id);
        $this->authorize('update', $project);
        $http_verb =   Wnumber::where('project_id', $project->id)->exists() ? 'PATCH' : 'POST';
        $number_exists =   Wnumber::where('number', $wnumber)->exists();
        $number_belongs_to_project =   Wnumber::where(['number'=> $wnumber, 'project_id' => $project_id])->exists();
        if($number_exists && !$number_belongs_to_project) {
            return null; //similar number has been added by a different project
        }

        // if( $number_exists) {         return  $wnumber;   }
        //$number_type =   $number_exists ? Wnumber::where('project_id', $project_id)->value('number_type') : false ;
        $number_type =   $number_exists ? Wnumber::where('number', $wnumber)->value('number_type') : false;
        $account = Waccount::find($waccount_id)->first(); //expected misbehavior
        $number =  $number_exists ?  Wnumber::where('number', $wnumber)->first() : new Wnumber();
        $number->project_id = $project_id;
        $number->uid = $uid;
        $number->number = $wnumber;
        $number->alias =  $wnumber;
        $number->code = $code;
        $number->waccount_id = $waccount_id;
        $number->wwebhook_id = $webhook_id;
        $number->status = 'new';
        $number->number_type = $number_exists && $number_type ? $number_type : "new";
        $number->country = 'KE';
        $number->service = 'whatsapp';
        $number->rented_at = null;
        $number->rental_rate = null;
        $number->setup_rate = null;
        $number->sozuri_rate = null;
        $number->inbound_message_rate = null;
        $number->platform_auto_recharge = null;
        $number->sozuri_auto_recharge = null;
        $number->platform_credit_balance = null;
        $number->sozuri_credit_balance = null;
        $number->active = false;
        $number->archived = false;
        $number->created_by = Auth::user()->id;
        $number->updated_by = Auth::user()->id;
        $number->save();
        return  $wnumber;
    }

    public function updateSandboxNumber(Request $request)
    {
        $request->validate(['number' => 'required|string', 'accessToken' => 'required|string', 'acknowledge' => 'required']);

        if (!Auth::user()->isGlobalAdmin) {
            Auth::logout();
            redirect('/');
        }
        $project_id = Project::where('name', 'primary_project')->value('id');

        $http_verb =   Waccount::where('project_id', $project_id)->exists() ? 'PATCH' : 'POST';
        $account =   $http_verb == 'PATCH' ?  Waccount::where('project_id', $project_id)->first() : new Waccount();
        $account->project_id = $project_id;
        $account->name = $request->name;
        $account->code = $request->code;
        $account->user_id = Auth::user()->id;
        $account->status = 'active';
        $account->token = null;
        $account->is_parent = false;
        $account->account_type = null;
        $account->credit_balance = 0;
        $account->sozuri_credit_balance = 0;
        $account->auto_recharge = null;
        $account->active = false;
        $account->archived = false;
        $account->created_by = Auth::user()->id;
        $account->updated_by = Auth::user()->id;
        $account->save();
        Log::info('sanbox account locally saved');

        $wnumber = $this->cleanNumber(($request->number));
        $number =  Wnumber::where('number_type', 'sandbox')->exists() ?  Wnumber::where('number_type', 'sandbox')->first() : new Wnumber();
        $number->project_id = $project_id;
        $number->uid = $request->accessToken;
        $number->number = $wnumber;
        $number->alias =  $wnumber;
        $number->code = $request->code;
        $number->waccount_id = $account->id;
        $number->wwebhook_id = null;
        $number->status = 'active'; //new
        $number->number_type = "sandbox";
        $number->country = 'KE';
        $number->service = 'whatsapp';
        $number->rented_at = null;
        $number->rental_rate = null;
        $number->setup_rate = null;
        $number->sozuri_rate = null;
        $number->inbound_message_rate = null;
        $number->platform_auto_recharge = null;
        $number->sozuri_auto_recharge = null;
        $number->platform_credit_balance = null;
        $number->sozuri_credit_balance = null;
        $number->active = false;
        $number->archived = false;
        $number->updated_by = Auth::user()->id;
        $number->save();
        return back()->with('message', 'Sandbox number '.$number->number.':'.$request->accessToken.' updated successfully');
    }
    //we will create numbers manually, why?
    public function getWnumber($project_id, $wnumber, $waccount_id, $webhook_uid, $webhook_id)
    {
        $project = Project::find($project_id);
        $this->authorize('update', $project);
        $http_verb =   Wnumber::where('project_id', $project->id)->whereNotNull('uid')->exists() ? 'PATCH' : 'POST';
        $account = Waccount::find($waccount_id)->first();
        $number =   Wnumber::where('project_id', $project_id)->whereNotNull('uid')->exists() ?  Wnumber::where('project_id', $project_id)->whereNotNull('uid')->first() : new Wnumber();
        $number->project_id = $project_id;
        $number->uid = null;
        $number->number = $wnumber;
        $number->alias =  $wnumber;
        $number->code = uniqid();
        $number->waccount_id = $waccount_id;
        $number->wwebhook_id = $webhook_id;
        $number->status = 'new';
        $number->number_type = 'mobile';
        $number->country = 'KE';
        $number->service = 'whatsapp';
        $number->rented_at = null;
        $number->rental_rate = null;
        $number->setup_rate = null;
        $number->sozuri_rate = null;
        $number->inbound_message_rate = null;
        $number->platform_auto_recharge = null;
        $number->sozuri_auto_recharge = null;
        $number->platform_credit_balance = null;
        $number->sozuri_credit_balance = null;
        $number->active = false;
        $number->archived = false;
        $number->created_by = Auth::user()->id;
        $number->updated_by = Auth::user()->id;
        $number->save();
        //we are submitting the number manually so this api cll wont be necessary
        $username =  $account->uid;
        $password = $account->token;
        $base_uri = 'https://api.karix.io/';
        $path = ($http_verb == 'PATCH') ? 'number/' . $number->uid . '/' : 'number';
        $credentials = base64_encode($username . ':' . $password);
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request($http_verb,  $path, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . $credentials,
                //'X-Authorization' => 'Bearer ' . $this->token,
                //'X-Requested-With' => 'XMLHttpRequest'
                'api-version' =>  "2.0"
            ],
            'json' => [
                "number" => $number->number,
                "webhook_uid" => $webhook_uid,
                "alias" => $number->alias,
            ]
        ]);
        /*
    
                [2022-01-30 05:53:02] local.ERROR: Client error: `POST https://api.karix.io/number` resulted in a `404 Not Found` response:
        {"error":{"message":"Not found."},"meta":{"request_uuid":"7146c0af-921b-46e0-ae83-b2585a754638"}}
        
        {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 404): Client error: `POST https://api.karix.io/number` resulted in a `404 Not Found` response:
        {\"error\":{\"message\":\"Not found.\"},\"meta\":{\"request_uuid\":\"7146c0af-921b-46e0-ae83-b2585a754638\"}}
        
        */
        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('whatsapp number ' . $http_verb .  $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        //$number->uid =  $data['data']['uid'];
        $number->credits_charged +=  $data['data']['credits_charged']; //keep increment
        $number->available_credits = $data['data']['available_credits'];
        $number->status = 'active';
        $number->alias = $data['data']['alias'];
        $number->number = $data['data']['number_details']['number'];
        $number->number_type = $data['data']['number_details']['number_type'];
        $number->country = $data['data']['number_details']['region']['country'];
        $number->service = $data['data']['number_details']['service']['sms'];
        $number->inbound_message_rate = $data['data']['number_details']['rate']['inbound_sms'];
        $number->rental_rate = $data['data']['number_details']['rate']['rental'];
        $number->setup_rate = $data['data']['number_details']['rate']['setup'];
        $number->updated_at = Carbon::now(); //$data['data'['number_details']]['updated_time'];
        $number->rented_at = Carbon::now(); //$data['data']['date_rented'];
        $number->save();
        return $number;
    }
    public function createWtemplate(Request $request)
    {
        $request->validate(['waccount_id' => 'required', 'project_id' => 'required', 'name' => 'required|regex:/[0-9a-zA_Z_]/|unique:wtemplates', 'text' => 'required|string', 'category' => 'required|string']);
        $w_account = Waccount::where('project_id', $request->project_id)->where('id', $request->waccount_id)->exists() ? Waccount::where('project_id', $request->project_id)->where('id', $request->waccount_id)->first() : null;
        if ($w_account == null) {
            return back()->withErrors('First create an account.');
        }
        $project = Project::find($request->project_id);
        $this->authorize('update', $project);
        //can only contain lowercase letters, numbers and underscores [a-bA-Z0-9 \_]
        $template = new Wtemplate();
        $template->project_id = $project->id;
        $template->uid = null;
        $template->name = strtolower(preg_replace('/[^a-zA-Z0-9_]/', '', $request->name));
        $template->url =  null;
        $template->code = uniqid();
        $template->language_code = 'en';
        $template->attachment =  null;
        $template->text =  $request->text;
        $template->category = $request->category;
        $template->waccount_id = $request->waccount_id;
        $template->wnumber_id = $request->wnumber_id;
        $template->status = 'new';
        $template->rejected_reason = null;
        $template->active = false;
        $template->archived = false;
        $template->created_by = Auth::user()->id;
        $template->updated_by = Auth::user()->id;
        $template->save();
        $request->session()->flash('message', 'Whatsapp Template submitted successfully');
        $to = [['email' => "<EMAIL>", 'name' => "Sozuri Support"]];
        $cc = [
            ['email' => '<EMAIL>', 'name' => "Sozuri Admin Lawr3"],
            ['email' => "<EMAIL>", 'name' => "Sozuri Admin Davido"],
            ['email' => "<EMAIL>", 'name' => "Sozuri Marketing"],
            ['email' => "<EMAIL>", 'name' => "Sozuri BUsiness"],
        ];
        $msg = "Whatsapp template created for project " . $project->name . " for WB number ";
        Mail::to($to)->cc($cc)->send(new AppNotification($msg, ""));
        return redirect('/projects/' . $project->id . '/wsettings');
    }
    public function createWprofilepic($project_id, $waccount_id, $wnumber, $file)
    {
        $account = Waccount::find($waccount_id)->first(); //expected misbehavior
        $project = Project::find($project_id);
        $this->authorize('update', $project);
        $profilepic =   Wprofilepic::where('project_id', $project->id)->exists() ?  Wprofilepic::where('project_id', $project->id)->first() : new Wprofilepic();
        //$profilepic =   new Wprofilepic(); //::where('project_id', $project->id)->where('waccount_id',$waccount_id)->exists() ?  Wprofilepic::where('project_id', $project->id)->first() : new Wprofilepic();
        $profilepic->project_id = $project_id;
        $profilepic->uid = null;
        $profilepic->number = $wnumber;
        $profilepic->url =  null;
        $profilepic->code = uniqid();
        $profilepic->waccount_id = $waccount_id;
        $profilepic->wnumber_id = null;
        $profilepic->status = 'new';
        $profilepic->rejected_reason = null;
        $profilepic->active = false;
        $profilepic->archived = false;
        $profilepic->created_by = Auth::user()->id;
        $profilepic->updated_by = Auth::user()->id;
        $profilepic->save();
        $username = config('app.karix_account_uid');
        $password = config('app.karix_account_token');
        $base_uri = config('app.karix_base_uri'); //'https://api.karix.io/';
        $wnumber = preg_replace('[\+]', '', $wnumber);
        $path = 'whatsapp/profile/photo/' . $wnumber . '/';
        $credentials = base64_encode($username . ':' . $password);
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('PUT',  $path, [
            'headers' => [
                //'Content-Type' => 'multipart/form-data',
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . $credentials,
                //'X-Authorization' => 'Bearer ' . $this->token,
                //'X-Requested-With' => 'XMLHttpRequest'
                'api-version' =>  "2.0"
            ],
            'multipart' => [
                [
                    "file" =>  file_get_contents($file->getPath()),
                    // $file, 
                ]
            ]
        ]);
        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('profilepic' .  $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $profilepic->number = $data['data']['number'];
        $profilepic->url = $data['data']['url'];
        $profilepic->status = 'uploaded';
        $profilepic->save();
        return $profilepic;
    }
    public function createAboutText($waccount, $wnumber, $text)
    {
        //validate url and name
        //$http_verb =   Wwebhook::where('project_id', $project->id)->exists() ? 'PATCH' : 'POST';
        $account = $waccount;
        $username = config('app.karix_account_uid');
        $password = config('app.karix_account_token');
        $base_uri = config('app.karix_base_uri'); //'https://api.karix.io/';
        $wnumber = preg_replace('[\+]', '', $wnumber);
        $path = 'whatsapp/profile/about/' . $wnumber . '/';
        $credentials = base64_encode($username . ':' . $password);
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('PATCH',  $path, [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Authorization' => 'Basic ' . $credentials,
                //'X-Authorization' => 'Bearer ' . $this->token,
                //'X-Requested-With' => 'XMLHttpRequest'
                'api-version' =>  "2.0"
            ],
            'json' => [
                "text" => $text,
            ]
        ]);
        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('account PATCH' .  $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $updated_number =  $data['data']['number'];
        $updated_text = $data['data']['text'];
        return $updated_text;
    }
    public function getAccounts(Request $request) {

        $accounts = Waccount::latest()->get();
        return view('admin.whatsapp-accounts', compact('accounts'));
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Waccount  $waccount
     * @return \Illuminate\Http\Response
     */
    public function show(Waccount $waccount)
    {
        //
    }
    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Waccount  $waccount
     * @return \Illuminate\Http\Response
     */
    public function edit(Waccount $waccount)
    {
        //
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Waccount  $waccount
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Waccount $waccount)
    {
        //
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Waccount  $waccount
     * @return \Illuminate\Http\Response
     */
    public function destroy(Waccount $waccount)
    {
        //
    }
}
