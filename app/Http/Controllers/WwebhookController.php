<?php

namespace App\Http\Controllers;

use App\Wwebhook;
use Illuminate\Http\Request;

class WwebhookController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Wwebhook  $wwebhook
     * @return \Illuminate\Http\Response
     */
    public function show(Wwebhook $wwebhook)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Wwebhook  $wwebhook
     * @return \Illuminate\Http\Response
     */
    public function edit(Wwebhook $wwebhook)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Wwebhook  $wwebhook
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Wwebhook $wwebhook)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Wwebhook  $wwebhook
     * @return \Illuminate\Http\Response
     */
    public function destroy(Wwebhook $wwebhook)
    {
        //
    }
}
