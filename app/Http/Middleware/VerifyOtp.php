<?php

namespace App\Http\Middleware;
use Illuminate\Http\Request;
use Closure;

class verifyOtp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        //if the user O<PERSON> is not verified, and they have set multifactor auth, then redirect to otp page
        if (!$request->user()->otpVerified  && $request->user()->multiFactor ) {
            return redirect('generateOtp');
        }
        return $next($request);
    }
}
