<?php
namespace App\Console\Commands;

use App\Sms;
use App\Models\SmsMonthlyCopy;
use App\Models\InteractiveSms;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use DB;

class CopyInteractiveSms extends Command
{
    protected $signature = 'interactivesms:copy';

    protected $description = 'Copy SMS records from SMS table to SMS copy table';

    public function handle()
    {
        $timeAgo = now()->subMinutes(5);
        $lastCopiedSmsId = InteractiveSms::max('sms_id') ?? 0;

        Log::info('copying interactive for timeAgo: ' . $timeAgo);
        $smsRecords = Sms::where('created_at', '>=', $timeAgo)
            ->where('id', '>', $lastCopiedSmsId)
            ->get();
        Log::info($smsRecords->count());
        foreach ($smsRecords as $msg) {
            /*InteractiveSms::create([
                'from' => $sms->from,
                'to' => $sms->to,
                'message' => $sms->message
            ]);*/
            if ((is_numeric($msg->from) && strlen($msg->from) < 7) || (is_numeric($msg->to) && strlen($msg->to) < 7)) {
                Log::info("inserting one interactive msg");
                $newSms = $msg->replicate()->fill([
                    'created_at' => $msg->created_at,
                    'updated_at' => $msg->updated_at
                ]);
                Log::info($msg->id);
                $newSms->sms_id =  $msg->id;
                $newSms->setTable('interactive_sms');
                $newSms->save();
                Log::info("saved one interactive msg");
            }
        }

        $this->info('SMS records copied successfully.');
    }
}