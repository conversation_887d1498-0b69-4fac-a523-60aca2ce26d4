<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Project;
use App\Models\SmsMonthlyCopy;
use App\Mail\CustomerMonthlySmsReport;
use Log;


class SendCustomerMonthlySmsReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sms:monthly-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send monthly SMS report to project owners';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */

    public function handle()
    {
        Log::info('Sending monthly SMS reports...');
        $projects = Project::all();
       $month = now()->subMonth()->month; // Get the previous month

        Log::info('Month '.$month);
        
        foreach ($projects as $project) {
            $smsData = SmsMonthlyCopy::where('project_id', $project->id)
                ->whereMonth('created_at', $month)
                ->select(DB::raw("CASE 
                    WHEN status = 'unknown_error' THEN 'SenderName Blacklisted' 
                    ELSE status END as status, telco, SUM(message_part) as sms"))
                ->groupBy('status', 'telco')
                ->get();

            $groupedData = [];
            
            foreach ($smsData as $data) {
                if (in_array($data->status, ['sent', 'success'])) {
                    $groupedData[$data->telco]['success'] = ($groupedData[$data->telco]['success'] ?? 0) + $data->sms;
                } else {
                    $groupedData[$data->telco][$data->status] = $data->sms;
                }
            }

            $email = $project->user->email;
            Log::info($groupedData);
            Log::info($project);
            Log::info($email);
            $bcc = [['email' => "<EMAIL>", 'name' => "Sozuri"],];
            Mail::to($email)->bcc($bcc)->send(new CustomerMonthlySmsReport($groupedData, $project));

            
        }

        Log::info('Monthly SMS reports sent successfully!');
    }
}

