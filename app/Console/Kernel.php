<?php

namespace App\Console;

use App\Mail\DailyStatus;
use App\Mail\WeeklyUsage;
use App\Mail\LowCredit;
use App\Mail\LowBalance;
use App\Collaboration;
use App\Mail\Collabo;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Project;
use App\Payment;
use App\User;
use Illuminate\Database\Schema\Builder;
use Illuminate\Support\Facades\DB;
use App\Contact;
use App\ContactList;
use App\Jobs\UploadContacts;
use Illuminate\Support\Facades\Log;
use App\Jobs\ProcessMessage;
use App\Jobs\ProcessSendairtelbulk;
use App\Jobs\ProcessSendtelkombulk;
use App\Mail\AppNotification;
use GuzzleHttp\Client;
use App\Jobs\PostLedgers;
use App\Sms;
use Exception;
use Throwable;
use App\Campaign;
use function Ramsey\Uuid\v1;
use Illuminate\Support\Facades\Artisan;
use App\Models\SmsMonthlyCopy;
use App\Console\Commands\CopyInteractiveSms;
class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
        'interactivesms:copy' => CopyInteractiveSms::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        ini_set('memory_limit', '1000M');
        ini_set('max_execution_time', 360); //default=30.You can not change this setting with ini_set() when running in safe mode
        $schedule->call(function () {
            $object = DB::table('sms')->select('status', DB::raw('count(*) as total'))->whereDate('created_at', date('Y-m-d'))->groupBy('status')->get();
            $msg = json_encode($object);
            $to = [['email' => "<EMAIL>", 'name' => "Sozuri Admin"]];
            $cc = [
                ['email' => '<EMAIL>', 'name' => "Sozuri Admin2"],
                //['email' => "<EMAIL>", 'name' => "Sozuri Admin Davido"],
            ];
            Mail::to($to)->cc($cc)->send(new AppNotification($msg, ""));
            Log::info('Sending 3 hr status email');
            
        })->cron('00 01 * * *')->runInBackground();


        $schedule->call(function () {
            try{
                $filePath = storage_path('logs/attempts.log');
                shell_exec("echo -n > " . $filePath);
                Log::info('Deleted Attempts');
            } catch (Exception $e) {
                Log::info($e->getMessage());
                Log::info('Failed Deleting Attempts');
            }
            
        })->cron('00 00 * * *')->runInBackground();

        $schedule->call(function () {
            Log::info('start checking duplicate contacts');
            ini_set('memory_limit', '10000M');
            ini_set('max_execution_time', 3600);
            ini_set('default_socket_timeout', '-1');
            $contactLists = DB::table('contact_lists')->get();
            foreach($contactLists as $contactlist) {
                $contactlistContacts = DB::table('contacts')->where('tag',$contactlist->id)->get();
                   // Create an array to keep track of unique mobile numbers
            $uniqueMobileNumbers = [];
            // Iterate through the contactlistContacts
            foreach ($contactlistContacts as $contact) {
                $mobile = $contact->mobile;
                // If the mobile number is not in the uniqueMobileNumbers array, add it
                if (!in_array($mobile, $uniqueMobileNumbers)) {
                    $uniqueMobileNumbers[] = $mobile;
                } else {
                    // If the mobile number is already in the uniqueMobileNumbers array, delete the duplicate record
                    DB::table('contacts')->where(['id' => $contact->id, 'tag' => $contactlist->id, 'mobile' => $mobile, ])->delete();
                    Log::info('deleted a duplicate contact '.$mobile.' from list '. $contactlist->name);
                }
            }
        }
        Log::info('done deleting duplicate contacts');

        })->cron('30 22 * * *');



        $schedule->call(function () {
            Log::info('just felt inspired');
        })->cron('* * * * *');


        $schedule->call(function () {
            ini_set('memory_limit', '10000M');
            ini_set('max_execution_time', 3600);
            ini_set('default_socket_timeout', '-1');
            Log::info("starting accouting ledger update");

            $project_sms = DB::table('sms')->whereDate('created_at', Carbon::yesterday())->select('project_id',  DB::raw("SUM(price) as prices"), DB::raw("SUM(cost) as costs"), DB::raw("SUM(message_part) as parts"))->groupBy('project_id')->get();
            Log::info($project_sms);
            foreach ($project_sms as $sms) {
                $project = Project::where('id', $sms->project_id)->get()->first();
                PostLedgers::dispatch($project->account_type == "postpay" ? "receivable" : "advance payment", $project->name, "sales", "debit", $sms->prices, 16.00, "inclusive");
                PostLedgers::dispatch("sale",  $project->name, "sales", "credit", $sms->prices ?: 0, 16.00, "inclusive");
                PostLedgers::dispatch("cost of goods",  $project->name, "sales", "debit", $sms->costs ?: 0, 16.00, "inclusive");
                PostLedgers::dispatch("units",  $project->name, "sales", "credit", $sms->costs ?: 0, 16.00, "inclusive");
                Log::info("accounts posted for project $project->name \n");
            }
        })->cron('05 00 * * *');


        $schedule->call(function () {
            Log::info('requesting new token');
            $base_uri = config('app.sdp_base_uri');
            $sdp_username = config('app.sdp_username');
            $sdp_password = config('app.sdp_password');
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => true]);
            $response = $client->request('POST', 'auth/login', [
                'headers' => [
                    'Content-Type'     => 'application/json',
                    'Accept'     => 'application/json',
                    'X-Requested-With'     => 'XMLHttpRequest',
                ],
                'json' => ['username' => $sdp_username, 'password' =>  $sdp_password]
            ]);
            //$code = $response->getStatusCode(); // 200
            // $reason = $response->getReasonPhrase(); // OK
            $body = $response->getBody();
            $stringBody = (string) $body; // Explicitly cast the body to a string
            Log::info($stringBody);
            $data = json_decode($stringBody, true);
            $tokenFile = fopen(config('app.token_file') . "/token.txt", "w"); //or die("Unable to open file!");
            $txt = $data['token'];
            fwrite($tokenFile, $txt);
            fclose($tokenFile);
            Log::info('token updated');
        //})->everyFiveMinutes();
        })->cron('* * * * *');

        $schedule->call(function () {
            $time = 'uptime-pingsoz: ' . date('Y-m-d h:i:s');
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://sozuri.net/api/v1/messaging',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_POSTFIELDS => "{\"project\": \"Devs\",
                \"from\": \"Sozuri\",
                \"campaign\": \"PING\",
                \"channel\": \"sms\",
                \"apiKey\": \"zpVkxu8rYD3UOQBI0ph6nIFXkWMkbHMXUhtmVXSRHO1aPrM78StB6PPz0GwQ\", 
                \"message\": \"$time\",
                \"type\": \"promotional\",
                \"to\": \"0725164293,0787226456\"}",
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            Log::info($response);
            Log::info('UPTIME PING');
        })->everyFourHours()->runInBackground(); //Run the task every week on Monday at 8:00


        $schedule->call(function () {
            $projects = DB::table('payments')->where('status', 'success')->select('project_id as project',  DB::raw("SUM(amount) as amount"))->groupBy('project', 'amount')->get();
            foreach ($projects as $project) {
                $total_monthly_payments =  $project->amount;
                //->whereBetween('created_at', [Carbon::today()->startOfMonth(), Carbon::today()->endOfMonth()])
                DB::tabble('royalties')->insert([
                    'project_id' => $project,
                    'user_id' => DB::table('projects')->where('id', $project)->value('user_id'),
                    'royalty_type' => "monthly use points",
                    'expenditure' => $total_monthly_payments,
                    'monetized_amount' => 0,
                    'points' => 0,
                    'detail' => "monthly usge points",
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            }
        })->yearly(); //run monthly
        // ->emailOutputTo('<EMAIL>')
        // ->emailOutputOnFailure;

        //backups
        $schedule->call(function () {

            ini_set('memory_limit', '40000M');
            ini_set('max_execution_time', 3600);
            ini_set('default_socket_timeout', '-1');

            try {
                $dumpFilePath = base_path(date("Y-m-d_H") . '.sql');
                $dbHost = env('DB_HOST');
                $dbUsername = env('DB_USERNAME');
                $dbPassword = env('DB_PASSWORD');
                $dbName = env('DB_DATABASE');
                $mysqldumpPath = '/usr/bin/mysqldump';
        
                // Command to generate the SQL dump
                $dumpCommand = "$mysqldumpPath -h $dbHost -u $dbUsername -p$dbPassword $dbName --result-file=$dumpFilePath";
                Log::info('Before executing dump command: ' . $dumpCommand);
        
                // Execute the command and capture output and return code
                exec($dumpCommand . ' 2>&1', $output, $returnVar);
                
                // Log the output and return code
                Log::info('Dump command output: ' . implode("\n", $output));
                Log::info('Dump command return code: ' . $returnVar);
        
                if ($returnVar !== 0) {
                    Log::error('Error during database dump, return code: ' . $returnVar);
                } else {
                    Log::info('Database dump created successfully: ' . $dumpFilePath);
                }
            } catch (Exception $e) {
                Log::info($e->getMessage());
            }
  //      })->cron('10 1 * * *');
    })->yearly();

          
            $schedule->call(function () {
                ini_set('memory_limit', '40000M');
                ini_set('max_execution_time', 3600);
                ini_set('default_socket_timeout', '-1');
                define("SPACES_DIRECTORY", 'sozuri/backups/');
                Log::info('sending backup');
                $backupFile = date("Y-m-d_H") . '.sql';
                $backupFilePath = base_path($backupFile);
                Log::info($backupFilePath);
                try {
                    if (file_exists($backupFilePath)) {
                        $fileContents = file_get_contents($backupFilePath);
                        Storage::disk('spaces')->put(SPACES_DIRECTORY . $backupFile, $fileContents);
                        Log::info("Backup sent");
            
                        // Delete the file after successful upload
                        if (unlink($backupFilePath)) {
                            Log::info("Backup file deleted: " . $backupFilePath);
                        } else {
                            Log::error("Failed to delete backup file: " . $backupFilePath);
                        }
                    } else {
                        Log::error("Backup file does not exist: " . $backupFilePath);
                    }
                } catch (Exception $e) {
                    Log::error('Error during backup send: ' . $e->getMessage());
                }
            })->cron('20 1 * * *');


        $schedule->call(function () {
            ini_set('memory_limit', '20000M');
            ini_set('max_execution_time', 9600); //set_time_limit(int $seconds): bool
            ini_set('default_socket_timeout', '-1');
            Log::info("begin archiving to monthly ");
            $count = 0;
            $sms = Sms::whereDate('created_at', '<', now())->get();
            foreach ($sms as $msg) {
                try {
                    $newSms = $msg->replicate()->fill([
                        'created_at' => $msg->created_at,
                        'updated_at' => $msg->updated_at
                    ]);

                    //save in month table
                    $newSms->setTable('sms_monthly_copy');
                    $newSms->save();

                    $count++;
                } catch (Throwable $e) {
                    Log::info('A class that inherits Exception or ErrorException caught '. $e->getMessage());
                }
            }
            Log::info("finish month archiving $count");
        })->cron('02 0 * * *'); //


        

        $schedule->call(function () {
            \Log::info('Starting daily delivery data update process.');
        
            // Loop through all projects
            $projects = \App\Project::with('campaigns')->get();
            \Log::info('Fetched all projects.', ['total_projects' => $projects->count()]);
        
            foreach ($projects as $project) {
                \Log::info('Processing project.', ['project_id' => $project->id]);
        
                // Loop through all campaigns in the project
                foreach ($project->campaigns as $campaign) {
                    \Log::info('Processing campaign.', ['campaign_id' => $campaign->id]);
        
                    // Get current delivery data from the campaign
                    $existingDelivery = $campaign->delivery ? json_decode($campaign->delivery, true) : [];
                    \Log::info('Fetched existing delivery data.', ['existing_delivery' => $existingDelivery]);
        
                    // Fetch delivery statuses from the Sms model for this campaign
                    $newDeliveryData = \App\Sms::where('project_id', $project->id)
                        ->where('campaign_id', $campaign->id)
                        ->selectRaw('status, COUNT(*) as total_messages')
                        ->groupBy('status')
                        ->get()
                        ->toArray();
                    \Log::info('Fetched new delivery data.', ['new_delivery_data' => $newDeliveryData]);
        
                    // Merge and update the delivery data
                    $statuses = array_column($existingDelivery, 'status');
                    foreach ($newDeliveryData as $newData) {
                        $status = $newData['status'];
                        $newCount = $newData['total_messages'];
        
                        // Check if the status exists in the existing data
                        $existingIndex = array_search($status, $statuses);
        
                        if ($existingIndex !== false) {
                            // Update the existing total_messages count
                            $existingDelivery[$existingIndex]['total_messages'] += $newCount;
                            \Log::info('Updated existing delivery status.', [
                                'status' => $status,
                                'new_total_messages' => $existingDelivery[$existingIndex]['total_messages']
                            ]);
                        } else {
                            // Add new status to the delivery data
                            $existingDelivery[] = $newData;
                            \Log::info('Added new delivery status.', [
                                'status' => $status,
                                'total_messages' => $newCount
                            ]);
                        }
                    }
        
                    $updatedDelivery = $existingDelivery;
        
                    // Save the merged delivery data back to the campaign
                    $campaign->update([
                        'delivery' => json_encode($updatedDelivery),
                    ]);
                    \Log::info('Updated campaign delivery data.', [
                        'campaign_id' => $campaign->id,
                        'updated_delivery' => $updatedDelivery
                    ]);
                }
            }
        
            \Log::info('Completed daily delivery data update process.');
        })->cron('01 0 * * *'); //
        
        

        $schedule->call(function () {
            ini_set('memory_limit', '20000M');
            ini_set('max_execution_time', 9600); //set_time_limit(int $seconds): bool
            ini_set('default_socket_timeout', '-1');
            Log::info("begin archiving to all messages");
            $count = 0;
            $sms = Sms::whereDate('created_at', '<', now())->get();
            foreach ($sms as $msg) {
                try {
                    $newSms = $msg->replicate()->fill([
                        'created_at' => $msg->created_at,
                        'updated_at' => $msg->updated_at
                    ]);

                    //save in all table
                    $newSms->setTable('sms_copy');
                    $newSms->save();

                    $msg->delete();
                    $count++;
                } catch (Throwable $e) {
                    Log::info('A class that inherits Exception or ErrorException caught '. $e->getMessage());
                }
            }
            Log::info("finish all archiving $count");
        })->cron('20 0 * * *'); //

     
        //delete past month sms month every fifth of month

        $schedule->call(function () {
            try {
                ini_set('memory_limit', '20000M');
                ini_set('max_execution_time', 9600);
                ini_set('default_socket_timeout', '-1');
                Log::info("Begin archiving");
                $currentYear = date('Y');
                $thisMonth = (int)date('m');
                            try {
                                $batchSize = 1000;
                                $deletedCount = 0;
                                
                                do {
                                    $deleted = DB::table('sms_monthly_copy')
                                        ->whereYear('created_at', $currentYear)
                                        ->whereMonth('created_at', '<', $thisMonth)
                                        ->orderBy('created_at')
                                        ->limit($batchSize)
                                        ->delete();
                                    $deletedCount += $deleted;
                                } while ($deleted > 0 );
                                Log::info("Finish deleting records");
                            } catch (Throwable $e) {
                                Log::info('Exception caught while deleting a record'.$e->getMessage());
                            }
                Log::info("Finish deleting $deletedCount monthly records");
            } catch (Throwable $e) {
                Log::info('Exception caught in the scheduled monthly task'.$e->getMessage());
            }
            Log::info("finish all archiving of monthly records");

        })->cron('0 0 5 * *'); //



        //////
        /*
        $schedule->call(function () {
            
            ini_set('memory_limit', '20000M');
            ini_set('max_execution_time', 9600);
            ini_set('default_socket_timeout', '-1');
            try {
                $campaigns = Campaign::all();
                
                foreach ($campaigns as $campaign) {
                    Log::info("Processing campaign: {$campaign->name}");
                    
                    try {
                        SmsMonthlyCopy::whereNull('campaign_name')
                            ->where('campaign_id', $campaign->id)
                            ->chunkById(4000, function ($messages) use ($campaign) {
                                try {
                                    $ids = $messages->pluck('id');
                                    SmsMonthlyCopy::whereIn('id', $ids)
                                        ->update(['campaign_name' => $campaign->name]);
                                } catch (\Exception $e) {
                                    Log::error("Error processing chunk for campaign {$campaign->name}: " . $e->getMessage());
                                }
                            });
                        
                        Log::info("Updated messages for campaign: {$campaign->name}");
                    } catch (\Exception $e) {
                        Log::error("Error processing campaign {$campaign->name}: " . $e->getMessage());
                    }
                }
                
                Log::info('Campaign names updated successfully.');
                return 0;
            } catch (\Exception $e) {
                Log::error("Fatal error in campaign update job: " . $e->getMessage());
                return 1;
            }
        })->cron('28 03 * * *'); // Runs at 3:20 AM daily


        $schedule->call(function () {
            try {
                \Log::info("Delivery status update job started.");
                $projects = \App\Project::with('campaigns')->get();
        
                foreach ($projects as $project) {
                    try {
                        \Log::info("Processing project: {$project->id} - {$project->name}");
        
                        foreach ($project->campaigns as $campaign) {
                            try {
                                \Log::info("Processing campaign: {$campaign->id} - {$campaign->name}");
        
                                $deliveryData = \App\Models\SmsMonthlyCopy::where(['project_id' => $project->id, 'campaign_id' => $campaign->id])
                                    ->selectRaw('status, COUNT(*) as total_messages')
                                    ->groupBy('status')
                                    ->get()
                                    ->toArray();
        
                                $campaign->update([
                                    'delivery' => $deliveryData,
                                ]);
        
                                \Log::info("Successfully updated delivery data for campaign: {$campaign->id}");
                            } catch (\Exception $e) {
                                \Log::error("Error updating delivery data for campaign: {$campaign->id}. Error: {$e->getMessage()}");
                            }
                        }
                    } catch (\Exception $e) {
                        \Log::error("Error processing project: {$project->id}. Error: {$e->getMessage()}");
                    }
                }
        
                \Log::info("Delivery status update job completed.");
            } catch (\Exception $e) {
                \Log::error("Error in delivery status update job. Error: {$e->getMessage()}");
            }
        })->dailyAt('10:16');
        */



        /////


        $schedule->call(function () {
            try {
                ini_set('memory_limit', '20000M');
                ini_set('max_execution_time', 9600);
                ini_set('default_socket_timeout', '-1');
                Log::info("Begin copy to interactive");
                $count = 0;
                $sms =  SmsMonthlyCopy::
                whereRaw('`from` REGEXP "^[0-9]+$"')
                ->orWhereRaw('`to` REGEXP "^[0-9]+$"')
                ->whereYear('created_at', Carbon::now()->year)
                ->get();

                foreach ($sms as $msg) {
                    try {
                        $newSms = $msg->replicate()->fill([
                            'created_at' => $msg->created_at,
                            'updated_at' => $msg->updated_at
                        ]);
                        //save in interactive table
                        $newSms->setTable('interactive_sms');
                        $newSms->save();
                        Log::info("saved one interactive msg");
                        $count++;
                    } catch (Throwable $e) {
                        Log::info('A class that inherits Exception or ErrorException caught when saving interactive '. $e->getMessage());
                    }
                }
                Log::info("finish interactive copy $count");
            } catch (Throwable $e) {
                Log::info('Exception caught in the scheduled interactive copy task'.$e->getMessage());
            }
        })->yearly(); //

        $schedule->command('interactivesms:copy')->everyMinute();


        $schedule->call(function () {
            //DB::table('payments')->where('balance', '<', 4)->where('status', 'success')->update(['status' => 'depleted']);
        })->everyThirtyMinutes(); //


        //clear queues more often to reduce memory leaks for long running jobs
        $schedule->exec('/usr/bin/php /var/www/talkzuri/artisan queue:restart')->everyFifteenMinutes();
        //combine with max jobs and max life in sec to also gracefully restart long running jobs.
        //php artisan queue:work --max-jobs=1000 --max-time=3600 
        //supervisor will restart them

        //update project balances in the morning
        $schedule->call(function () {
            ini_set('memory_limit', '10000M');
            ini_set('max_execution_time', 3600); //set_time_limit(int $seconds): bool
            ini_set('default_socket_timeout', '-1');
            $projects = Project::get();
            Log::info('started saving projects balances');
            foreach ($projects as $project) {
                $balanceExists = DB::table('payments')->where('project_id', $project->id)->where('status', 'success')->exists();
                $bal = $balanceExists ? DB::table('payments')->where('project_id', $project->id)->where('status', 'success')->sum('balance') : 0;
                if($bal > 3) {
                    $project->shortcode = $bal;
                    $project->save();
                } else {
                    $project->shortcode = 0;
                    $project->save();
                }
            }
            Log::info('saved projects balances');
        })->dailyAt('00:10')->runInBackground(); //

        //send daily update email to admins
        $schedule->call(function () {
            $to = [['email' => "<EMAIL>", 'name' => "Sozuri Admins"]];
            $cc = [
                ['email' => '<EMAIL>', 'name' => "Sozuri Admin Lawr3"],
                ['email' => "<EMAIL>", 'name' => "Sozuri Admin Davido"]
            ];
            Mail::to($to)->cc($cc)->send(new DailyStatus());
            //})->dailyAt('07:00')->runInBackground(); //
        })->dailyAt('00:15')->runInBackground();


        //schedule sms cron. 
        $schedule->call(function () {
            $time_start = microtime(true);
            ini_set('memory_limit', '20000M');
            // ini_set('max_input_time', '1800');
            ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
            //set_time_limit(800);//same as above
            ini_set('default_socket_timeout', '-1');
            Log::info('starting scheduler check');
            $max_time_interval_into_future = Carbon::now()->addMinutes(15)->format('Y-m-d H:i:s');
            // $to_send_collection = DB::table('scheduledsms')->where('send_at', '<', $max_time_interval_into_future)->get();

            $chunkSize = 4000; // Number of records to process per chunk

            DB::table('scheduledsms')
                ->where('send_at', '<', $max_time_interval_into_future)
                ->orderBy('send_at')
                ->chunk($chunkSize, function ($records) {
                    // Process each chunk of records

                    $to_send = $records->toArray();
                    if (count($to_send) < 1) {
                        Log::info("no schduled messages");
                        exit('no sms found');
                    }
                    $telcos =  array_column($to_send, 'telco');
                    $oas = $froms = array_column($to_send, 'from');
                    $msisdns = array_column($to_send, 'to');
                    $uniqueIds = array_column($to_send, 'message_id');
                    $packageIds = array_column($to_send, 'package_id');
                    $bulk_unique_id = array_column($to_send, 'bulk_id');
                    $types = array_column($to_send, 'type');
                    $costs = array_column($to_send, 'cost');
                    $prices = array_column($to_send, 'price');
                    $message_parts = array_column($to_send, 'message_part');
                    $tos = array_column($to_send, 'to');
                    $statuses =  array_column($to_send, 'status');
                    $status_codes = array_column($to_send, 'status_code');
                    $project_ids = array_column($to_send, 'project_id');
                    $credits = array_column($to_send, 'credits');
                    $messages = array_column($to_send, 'message');
                    $created_ats = array_column($to_send, 'created_at');;
                    $send_ats = array_column($to_send, 'send_at');
                    $campaign_ids = array_column($to_send, 'campaign_id');
                    $campaignNames = array_column($to_send, 'campaign_name');
                    $message_ids = array_column($to_send, 'message_id');
                    $tem_wzIds = $message_ids;
                    $channels = array_column($to_send, 'channel');
                    $userNames = array_fill(0, count($channels), config('app.sdp_portal_username'));
                    $actionResponseURLs = array_fill(0,  count($channels), config('app.sdp_response_uri'));
                    $bulk_ids =  array_column($to_send, 'bulk_id');
                    $links = array_column($to_send, 'uri');
                    $raw_smses = array_map(function ($userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from,  $created_at, $send_at,  $campaign_id, $telco, $message_id, $to, $status, $status_code, $bulk_id, $message_part, $type, $link) {
                        return array_combine(
                            ['userName', 'channel', 'packageId', 'oa', 'msisdn', 'message', 'uniqueId', 'actionResponseURL', 'credits', 'project_id', 'from', 'created_at', 'send_at', 'campaign_id', 'telco', 'messageId', 'to', 'status', 'statusCode', 'bulkId', 'messagePart', 'type', 'link_id'],
                            [$userName, $channel, $packageId, $oa, $msisdn, $message, $uniqueId, $actionResponseURL, $cost, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link]
                        );
                    }, $userNames, $channels, $packageIds, $oas, $msisdns, $messages, $uniqueIds, $actionResponseURLs, $credits, $project_ids, $froms,  $created_ats, $send_ats, $campaign_ids, $telcos, $message_ids, $tos, $statuses, $status_codes, $bulk_ids, $message_parts,  $types, $links);
                    
                    $raw_db_smses = array_map(function ($channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName) {
                        return array_combine(
                            [
                                'channel', 'package_id', 'message', 'description', 'price', 'project_id', 'from',
                                'created_at', 'send_at', 'campaign_id', 'telco', 'message_id',
                                'to', 'status', 'status_code', 'bulk_id', 'message_part', 'type', 'uri', 'cost', 'campaign_name'
                            ],
                            [$channel, $packageId, $message, $description, $credit, $project_id, $from, $created_at, $send_at, $campaign_id, $telco, $message_id,  $to, $status, $status_code, $bulk_id, $message_part, $type, $link, $cost, $campaignName]
                        );
                    }, $channels, $packageIds, $messages, $statuses, $prices, $project_ids, $froms, $created_ats, $send_ats,  $campaign_ids, $telcos, $message_ids, $tos, $statuses, $status_codes, $bulk_ids, $message_parts,  $types, $links, $costs, $campaignNames);
                    
                         
                    $inst = new \App\Http\Controllers\SmsController(); //@telkomNumbers();

                    //$short_smses_response = array_map(\App\Http\Controllers\SmsController::$response_shortener, $raw_smses);
                    $safaricom_payload =  $inst->safaricomNumbers($raw_smses);
                    $airtel_payload =   $inst->airtelNumbers($raw_smses);
                    $telkom_payload =  $inst->telkomNumbers($raw_smses);
                    $inst->dispatchMessages($safaricom_payload, $airtel_payload, $telkom_payload, $raw_smses, $raw_db_smses, Carbon::now());
                    // do {
                    //        DB::table('scheduledsms')->where('send_at', '<', $max_time_interval_into_future)->take(2000)->delete();
                    //    } while(DB::table('scheduledsms')->where('send_at', '<', $max_time_interval_into_future)->count() > 1);

                    // Delete processed records from the database
                    $recordIds = $records->pluck('id')->toArray();
                    DB::table('scheduledsms')->whereIn('id', $recordIds)->delete();
                });




            //Mail user owning the project  ->send mail to default queue
            //$emailTo = User::where('id', Project::find($project_ids[0])->first()->user_id)->value('email');
            //  if ($emailTo) {
            //      $bcc = "<EMAIL>";
            //     $message = "Hi, Your scheduled SMS messages have been sent";
            //    $subject = "Schedules SMS have been Sent";
            //     Mail::to($emailTo)->bcc($bcc)->queue(new AppNotification($message, $subject));
            //}

            Log::info('sent and deleted all scheduled log sms');
        })->everyFifteenMinutes();





        $schedule->call(function () {
            ini_set('memory_limit', '10000M');
            // ini_set('max_input_time', '1800');
            ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
            //set_time_limit(800);//same as above
            ini_set('default_socket_timeout', '-1');
            $projects = DB::table('projects')->get();
            foreach ($projects as $project) {
                $contact_list_id = ContactList::where('project_id', $project->id)->where('name', 'default')->exists() ?
                    ContactList::where('project_id', $project->id)->where('name', 'default')->first()->id :
                    ContactList::insertGetId(['project_id' => $project->id, 'name' => 'default']);
                $sms_recipients = array_unique(DB::table('sms')->where('project_id', $project->id)->pluck('to')->toArray());
                $contacts = array_unique(DB::table('contacts')->where('project_id', $project->id)->pluck('mobile')->toArray());
                $missing_contacts = array_values(array_diff($sms_recipients, $contacts));
                $contacts_result = array_map(function ($number) {
                    return array_combine(
                        ['mobile'],
                        [$number]
                    );
                }, $missing_contacts); //repeat for each element of missing contacts to do... array combine 'mobile' key with $number ie. element

                $split_array = array_chunk($contacts_result, 1000);
                foreach ($split_array as $contacts_results) {
                    UploadContacts::dispatch($contacts_results, $contact_list_id, $project->id)->onQueue('low'); //->delay(Carbon::parse($request->sendAt));
                }
            }
        })->yearly();

        /*
        $schedule->call(function () {
            foreach (User::whereHas('projects')->get() as $user) {
                $to = [['email' => $user->email, 'name' => "Sozuri customer"]];
                $bcc = [['email' => "<EMAIL>", 'name' => "Sozuri customer"],];
                Mail::to($to)->bcc($bcc)->send(new WeeklyUsage($user));
            }
        })->yearly(1, '8:00')->runInBackground(); //Run the task every week on Monday at 8:00

        //match invalid contacts.....
            $schedule->call(function () {
            $users = DB::table('contacts')->whereNotBetween('mobile', [100000000, 999999999])->count();
            })->yearly(1, '8:00')->runInBackground(); //Run the task every week on Monday at 8:00

        */

        $schedule->call(function () {
            foreach (Project::get() as $project) {
                $project_bal = Payment::where('project_id', $project->id)->where('status', 'success')->sum('balance');
                if ($project_bal < $project->min_balance) {
                    $to = [['email' => $project->min_balance_email, 'name' => "Sozuri customer"]];
                    $bcc = [['email' => "<EMAIL>", 'name' => "Sozuri customer"],];
                    Mail::to($to)->bcc($bcc)->send(new LowCredit($project));
                }
            }
        })->weeklyOn(1, '8:00')->runInBackground(); //Run the task every hour

        $schedule->call(function () {
            foreach (Project::get() as $project) {
                $project_bal = Payment::where('project_id', $project->id)->where('status', 'success')->sum('balance');
                if ($project_bal < 10) {
                    $project_owner_email = User::where('id', $project->user_id)->value('email');
                    $to = [['email' => $project_owner_email, 'name' => "Sozuri customer"]];
                    $bcc = [['email' => "<EMAIL>", 'name' => "Sozuri customer"],];
                    Mail::to($to)->bcc($bcc)->send(new LowBalance($project));
                }
            }
        })->weeklyOn(1, '8:00')->runInBackground(); //Run the task every hour

        $schedule->call(function () {
            foreach (Collaboration::where(['isActive' => 0, 'status' => 0])->get() as $collaboration) {
                $to = [['email' => $collaboration->email, 'name' => "Sozuri customer"]];
                $bcc = [['email' => "<EMAIL>", 'name' => "Sozuri collabo invite"],];
                Mail::to($to)->bcc($bcc)->send(new Collabo($collaboration));
            }
        })->yearly()->runInBackground(); //Run the task every hour

//monthly sms report to customers
//$schedule->command('sms:monthly-report')->monthlyOn(1, '4:00');
//$schedule->command('sms:monthly-report')->monthlyOn(1, '6:00');//last working



        /* $schedule->exec('node /home/<USER>/script.js')->daily();
            $schedule->exec('mysql /home/<USER>/script.js')->daily();
            ->emailOutputTo('<EMAIL>')
            ->emailOutputOnFailure
        $to = [
            [
                'email' =>  $request->email,
                'name' => "Invitation to Collaborate",
            ]
        ];
        $bcc = [
            [
                'email' => "<EMAIL>",
                'name' => "Collaboration Invitation",
            ]
        ];
        Mail::to($to)->bcc($bcc)->send(new Collabo($collaboration));
        return back()->with('status', 'Invitation Successful');

        $schedule->call(function () {
                foreach (Project::get() as $project) {

            $missing_numbers = DB::table('sms AS t1')->where('project_id',$project->id)
                    ->select('t1.to')
                    ->leftJoin('contacts AS t2','t2.mobile','=','t1.to')
                    ->whereNull('t2.mobile')->get();

                    DB::table('contacts')->insertOrIgnore([
                        ['id' => 1, 'email' => '<EMAIL>'],
                        ['id' => 2, 'email' => '<EMAIL>'],
                    ]);

                    }
            
                $to = [['email' => $project->min_balance_email, 'name' => "Sozuri customer"]];
                $bcc = [['email' => "<EMAIL>", 'name' => "Sozuri customer"],];
                Mail::to($to)->bcc($bcc)->send(new LowBalance($project));
                
            }
        })->dailyAt('03:00')->runInBackground(); //Run the task every hour
        */
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
    /**
     * Get the timezone that should be used by default for scheduled events.
     *
     * @return \DateTimeZone|string|null
     */
    protected function scheduleTimezone()
    {
        return 'Africa/Nairobi';
    }
}
