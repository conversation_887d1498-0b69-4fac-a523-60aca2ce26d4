<?php

namespace App\Providers;

use Illuminate\Support\Facades\Auth;
use App\Campaign;
use App\Policies\CampaignPolicy;

use App\ContactList;
use App\Policies\ContactListPolicy;

use App\Contact;
use App\Policies\ContactPolicy;

use App\Deposit;
use App\Policies\DepositPolicy;

use App\Help;
use App\Policies\HelpPolicy;

use App\Project;
use App\Policies\ProjectPolicy;

use App\Sms;
use App\Policies\SmsPolicy;

use App\User;
use App\Policies\UserPolicy;

use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        //'App\Model' => 'App\Policies\ModelPolicy',
        Campaign::class => CampaignPolicy::class,
        ContactList::class => ContactListPolicy::class,
        Contact::class => ContactPolicy::class,
        Help::class => HelpPolicy::class,
        Project::class => ProjectPolicy::class,
        Sms::class => SmsPolicy::class,
        User::class => UserPolicy::class,
        Topup::class => TopupPolicy::class,
        Collaboration::class => CollaborationPolicy::class,

    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        Auth::viaRequest('sms_token', function ($request) {
            return User::where('api_token', hash('sha256', $request->api_token ? $request->api_token : $request->bearerToken()))->first();
            //return User::where('api_token', hash('sha256', $request->api_token ? $request->api_token : $request->bearerToken()))->first();
            //return User::first();
        });

        Auth::viaRequest('project_token', function ($request) {
           // $project = Project::where('api_token', '=', ($request->api_token ? $request->api_token : $request->bearerToken()))->where('name', 'like', $request->project.'%')->first();
                $project = Project::where(['api_token' => ($request->apiKey ? $request->apiKey : $request->bearerToken()),'name' =>  $request->project ])->first();
                return $project;
            //return User::where('api_token', hash('sha256', $request->api_token ? $request->api_token : $request->bearerToken()))->first();
            //return User::first();
        });




        if ($this->app->environment('production')) {
            \URL::forceScheme('https');
        }
    }
}
