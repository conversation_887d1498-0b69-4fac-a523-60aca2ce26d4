<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Collaboration extends Model
{
    //
    //protected $fillable = ['email','project_id','collabo_code'];

        /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];


    public function project(){
       return  $this->belongsTo('App\Project');
    }
    public function user(){
        return  $this->belongsTo('App\User');
     }
}
