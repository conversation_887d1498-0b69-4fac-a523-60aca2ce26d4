<?php

namespace App\Listeners;

use App\Events\SingleSmsSentEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SingleSmsSentEventListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  SingleSmsSentEvent  $event
     * @return void
     */
    public function handle(SingleSmsSentEvent $event)
    {
        //
    }
}
