<?php

namespace App\Exports;

use App\User;
use App\Marital;
//use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

use App\Sms;
use Illuminate\Support\Facades\DB;

class SmsExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable;

    public function __construct(string $start, string $end, String $project_id)
    {
        $this->start = $start;
        $this->end = $end;
        $this->project_id = $project_id;
    }

    public function query()
    {
        return Sms::query()->where('created_at','>=', $this->start)->where('created_at','<', $this->end);
        //return DB::table('sms')->query()->where('project_id',$this->project_id)->where('created_at','>', $this->start)->where('created_at','<', $this->end);

    }


    /*
    * @var User $user
    */
    public function map($sms): array
    {
        return [
            $sms->from,
            $sms->to,
            $sms->message,
            $sms->status,
            $sms->description,
            $sms->telco,
            $sms->updated_at,
        ];        
    }
    public function headings(): array
    {
        return [
            'sender Id',
            'to',
            'message',
            'status',
            'description',
            'telco',
            'delivery at',
        ];
    }
}
