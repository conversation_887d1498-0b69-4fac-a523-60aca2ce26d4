<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Carbon;
use App\Contact;
use App\ContactList;

use Illuminate\Support\Facades\DB;

class ContactsExport implements FromArray, WithHeadings, WithMapping
{
    use Exportable;
    public $contactListId;
    public $projectId;

    public function __construct(String $projectId, String $contactListId)
    {
        $this->projectId = $projectId;
        $this->contactListId = $contactListId;
    }

    public function array(): array
    {
        ini_set('memory_limit', '18G');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        ini_set('upload_max_filesize', '64M');     
        ini_set('post_max_size', '64M');
        //return Sms::query()->where('created_at','>', $this->start)->where('created_at','<', $this->end); Y-m-d
        return DB::table('contacts')->where('project_id',$this->projectId)->where('tag', $this->contactListId)->latest()->take(999998)->get()->toArray();
    }


    /*
    * @var Contact $contact
    */
    public function map($contact): array
    {
        return [
            $contact->mobile,
            $contact->fname,
            $contact->mname,
            $contact->lname,
            $contact->email,
            $contact->type,
            $contact->job,
            $contact->company,
            $contact->city,
            $contact->created_at,
        ];
    }
    
    public function headings(): array
    {
        return [
            'mobile',
            'fname',
            'mname',
            'lname',
            'email',
            'type',
            'job',
            'company',
            'city',
            'created_at',
        ];
    }
}


