<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Illuminate\Support\Carbon;

use Illuminate\Support\Facades\DB;

class SmsExport implements FromArray, WithHeadings, WithMapping
{
    use Exportable;
    public $start;
    public $end;
    public $project_id;

    public function __construct(string $start, string $end, String $project_id)
    {
        $this->start = $start;
        $this->end = $end;
        $this->project_id = $project_id;
    }

    public function array(): array
    {
        ini_set('memory_limit', '18G');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
        ini_set('upload_max_filesize', '64M');     
        ini_set('post_max_size', '64M');
        //return Sms::query()->where('created_at','>', $this->start)->where('created_at','<', $this->end); Y-m-d
        $table_name = Carbon::parse($this->start)->isToday() ? 'sms' : 'sms_copy';
        return DB::table($table_name)->where('project_id',$this->project_id)->whereDate('created_at','>=', $this->start)->whereDate('created_at','<=', $this->end)->latest()->take(999995)->get()->toArray();
    }


    /*
    * @var User $user
    */
    public function map($sms): array
    {
        return [
            $sms->from,
            $sms->to,
            $sms->message,
            $sms->status,
            $sms->description,
            $sms->price,
            $sms->telco,
            $sms->direction,
            $sms->message_id,
            $sms->created_at,
            $sms->updated_at,
            $sms->clicked_at,
        ];
    }
    
    public function headings(): array
    {
        return [
            'sender Id',
            'to',
            'message',
            'status',
            'description',
            'cost',
            'telco',
            'direction',
            'message Id',
            'sent at',
            'delivery at',
            'clicked at',
        ];
    }
}

