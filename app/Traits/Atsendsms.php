<?php
namespace App\Traits;
use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use App\Project;
use Illuminate\Support\Carbon;
/**
* class MSG91 to send SMS on Mobile Numbers.
* <AUTHOR>
*/
trait Atsendsms {

   /* private $API_KEY = 'API_KEY';
    private $SENDER_ID = "VERIFY";
    private $ROUTE_NO = 4;
    */


    /**
     * @param int $contactId
     * @param Carbon $executeAt
     * @return boolean
     */

    public function sendAtSms($sms, $recipients  ) {

        //NOW THIS IS TH EPART EJHETE WE NEED TO REFACTOR.....
        //KEEP IN MIND THAT THIS IS HE CONECTIO WITH OUR PROVIDER, SO ALSO CONSIDER DDUCTING MAINCOMPANY CEDITS HERE
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://api.africastalking.com/']);
        $sender =  config('app.atFrom'); //$AT_FROM; //env('AT_FROM');
        $apiKey =  config('app.atKey'); //$AT_API_KEY; //env('AT_API_KEY');
        $username =   config('app.atUsername'); //$AT_USERNAME; //env('AT_USERNAME');
       // $sms  = $request->message;

        $response = $client->request('POST', '/version1/messaging', [
            'headers' => [
                'Content-Type'     => 'application/x-www-form-urlencoded',
                'Accept'     => 'application/json',
                'apiKey'     => $apiKey, 
            ],
            //'auth' => env('AT_USERNAME'),
            'form_params' => [
                'username' => $username,
                'from'     => $sender,  //string
                'to'     => implode(',', $recipients), //string comma separated list of recipients
                'message'     => $sms, //string
                'bulkSMSMode'     => 1,  //integer
                //'enqueue'     => 1,  //integer
                //'keyword'     => '',  //string. keyword to be used for a premium service.
                //'linkId'     => '', //string. used for premium services to send OnDemand messages. We forward the linkId to your application when the user sends a message to your service
                //'retryDurationInHours'     => '', //string. hours your subscription message should be retried in case it’s not delivered to the subscriber.
            ]
        ]);

        return $response;

    }

    public function sendSingleSms($sms, $recipients, $project_id){


     //NOW THIS IS TH EPART EJHETE WE NEED TO REFACTOR.....
        //KEEP IN MIND THAT THIS IS HE CONECTIO WITH OUR PROVIDER, SO ALSO CONSIDER DDUCTING MAINCOMPANY CEDITS HERE
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://api.africastalking.com/']);
        $sender =  config('app.atFrom'); //$AT_FROM; //env('AT_FROM');
        $apiKey =  config('app.atKey'); //$AT_API_KEY; //env('AT_API_KEY');
        $username =   config('app.atUsername'); //$AT_USERNAME; //env('AT_USERNAME');
       // $sms  = $request->message;

        $response = $client->request('POST', '/version1/messaging', [
            'headers' => [
                'Content-Type'     => 'application/x-www-form-urlencoded',
                'Accept'     => 'application/json',
                'apiKey'     => $apiKey,
            ],
            //'auth' => env('AT_USERNAME'),
            'form_params' => [
                'username' => $username,
                'from'     => $sender,  //string
                'to'     => implode(',', $recipients), //string comma separated list of recipients
                'message'     => $sms, //string
                'bulkSMSMode'     => 1,  //integer
                //'enqueue'     => 1,  //integer
                //'keyword'     => '',  //string. keyword to be used for a premium service.
                //'linkId'     => '', //string. used for premium services to send OnDemand messages. We forward the linkId to your application when the user sends a message to your service
                //'retryDurationInHours'     => '', //string. hours your subscription message should be retried in case it’s not delivered to the subscriber.
            ]
        ]);





        $sender = config('app.atFrom'); //$AT_FROM; //env('AT_FROM');

        $cleanCost = function ($cost) {
            $clean_cost = preg_replace("/[^0-9\.]/", "", $cost);
            return $clean_cost;
        };

        $project = Project::find($this->project_id);

        //process response whic is actually gonna come later on whe the queued jpob is procesed
        $body = $response->getBody();
        // Implicitly cast the body to a string and echo it
        // Explicitly cast the body to a string
        $stringBody = (string) $body;
        // Read 10 bytes from the body
        $tenBytes = $body->read(10);
        // Read the remaining contents of the body as a string
        $remainingBytes = $body->getContents();
        $data = json_decode($stringBody, true);
        // Update Key
        $costs = array_map($cleanCost, array_column($data['SMSMessageData']['Recipients'], 'cost'));
        $messageIds = array_column($data['SMSMessageData']['Recipients'], 'messageId');
        $messageParts = array_column($data['SMSMessageData']['Recipients'], 'messageParts');
        $numbers = array_column($data['SMSMessageData']['Recipients'], 'number');
        $statuses = array_column($data['SMSMessageData']['Recipients'], 'status');
        $statusCodes = array_column($data['SMSMessageData']['Recipients'], 'statusCode');
        $project_ids = array_fill(0, count($messageIds), $project_id);
        $messages = array_fill(0, count($messageIds), $this->sms);
        $froms = array_fill(0, count($messageIds), $sender);
        $details = array_fill(0, count($messageIds), $data['SMSMessageData']['Message']);
        $created_ats = array_fill(0, count($messageIds), Carbon::now());
        $campaignIds = array_fill(0, count($messageIds), $this->campaign_id);
        $tem_wzIds = array_fill(0, count($messageIds), 'tz');

        $make_wzId = function () {
            return strtoupper(uniqid('tz'));
        };
        $wzIds = array_map($make_wzId, $tem_wzIds);  //important to give each message a unique id

        $project->update(['credits' => ($project->credits - array_sum($messageParts))]);  //message parts are the real count of sms sent!

        $sms_result = array_map(function ($wzId, $cost, $messageId, $messagePart, $number, $status, $statusCode, $project_id, $message, $from, $detail, $created_at, $campaignId) {
            return array_combine(
                ['wzId', 'cost', 'messageId', 'messagePart', 'number', 'status', 'statusCode', 'project_id', 'message', 'from', 'detail', 'created_at', 'campaign_id'],
                [$wzId, $cost, $messageId, $messagePart, $number, $status, $statusCode, $project_id, $message, $from, $detail, $created_at, $campaignId]
            );
        }, $wzIds, $costs, $messageIds, $messageParts, $numbers, $statuses, $statusCodes, $project_ids, $messages, $froms, $details, $created_ats, $campaignIds);

        DB::table('sms')->insert($sms_result);
        //Sms::create($sms_result);

        $sms_api_result = array_map(function ($wzId, $messagePart, $number, $status,  $message) {
            return array_combine(
                ['tzId', 'sms_credits', 'to', 'status',  'message'],
                [$wzId, $messagePart, $number, $status, $message]
            );
        },  $wzIds, $messageParts, $numbers, $statuses, $messages);


        return response()->json(["Result" => $sms_api_result ? $sms_api_result : "empty"]);


    }








    public function sendBulkSms($message, $recipients){
            $sender = 'BeterGames';
            //$message = "hello Augu";
            /*$recipient = DB::table('users')
                ->where('isVerified', '=','1')
                ->get();*/
            $url =  'https://mysms.nsano.com/api/v1/sms/bulk';

            $ch = curl_init( $url );
            # Setup request to send json via POST.
            $payload = json_encode( array("sender"=> $sender, "recipients"=>$recipients, "message"=>$message) );
            curl_setopt( $ch, CURLOPT_POSTFIELDS, $payload );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json','Accept: application/json','X-SMS-Apikey:97553896e0c67843c873ca09577e5755'));
            # Return response instead of printing.
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            # Send request.
            $result = curl_exec($ch);

            //var_dump($result);
            DB::table('sms')->insert(
                //['mobile' => json_encode($recipients, true), 'message' => $message, 'sp' => 'Nsano', 'created_at' => date('Y-m-d H:i:s'), 'response' =>json_encode($result, true)]
                ['mobile' => sizeof($recipients), 'message' => $message, 'sp' => 'Nsano', 'created_at' => date('Y-m-d H:i:s'), 'response' =>json_encode($result, true)]

            );
            curl_close($ch);

    }
    public function sendScheduleSms($message, $recipients, $time ){
        $sender = 'BeterGames';
        //$message = "hello Augu";
        /*$recipient = DB::table('users')
            ->where('isVerified', '=','1')
            ->get();*/
        //$time = time();
        //$time = time() + (7 * 24 * 60 * 60);
        //date_default_timezone_set("Europe/Helsinki");    //set the timezone
        //echo "Europe/Helsinki:".time();
        $url =  'https://mysms.nsano.com/api/v1/sms/schedule';
        $ch = curl_init( $url );
        # Setup request to send json via POST.
        $payload = json_encode( array("sender"=> $sender, "recipients"=>$recipient, "message"=>$message, "run_at"=>$time ) );
        curl_setopt( $ch, CURLOPT_POSTFIELDS, $payload );
        curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json','Accept: application/json','X-SMS-Apikey:97553896e0c67843c873ca09577e5755'));
        # Return response instead of printing.
        curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
        # Send request.
        $result = curl_exec($ch);

        //var_dump($result);
        DB::table('sms')->insert(
            ['mobile' => $recipients, 'message' => $message, 'sp' => 'Nsano', 'created_at' => date('Y-m-d H:i:s'), 'response' =>json_encode($result, true)]
        );
        curl_close($ch);

    }



    }
