<?php

namespace App\Traits;

use Exception;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;
use App\Project;
use Illuminate\Support\Carbon;
use Twilio\Rest\Client;
//require_once '/path/to/vendor/autoload.php';

/**
 * class MSG91 to send SMS on Mobile Numbers.
 * <AUTHOR>
 */
trait Twsendwhatsapp
{

    /* private $API_KEY = 'API_KEY';
    private $SENDER_ID = "VERIFY";
    private $ROUTE_NO = 4;
    */


    /**
     * @param int $contactId
     * @param Carbon $executeAt
     * @return boolean
     */

    public function sendTwMessage($message, $recipients)
    {

        //NOW THIS IS TH EPART EJHETE WE NEED TO REFACTOR.....
        //KEEP IN MIND THAT THIS IS HE CONECTIO WITH OUR PROVIDER, SO ALSO CONSIDER DDUCTING MAINCOMPANY CEDITS HERE


        // Find your Account Sid and Auth Token at twilio.com/console
        // DANGER! This is insecure. See http://twil.io/secure
        $sid    =  config('app.twSid'); //"AC31ddd3c7832c900cb4d946c1fdf2bf85";
        $token  = config('app.twAuthToken'); //"your_auth_token";
        $from  = config('app.twWhNum'); //"your_auth_token";
 
        $twilio = new Client($sid, $token);

        $message = $twilio->messages
            ->create(
                "whatsapp:" . $recipients, // to  implode(',', $recipients)
                [
                    "from" => "whatsapp:".$from, //+***********
                    "body" => $message
                ]
            );
        //  print($message->sid);


        return $message;

        /* {
  "account_sid": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "api_version": "2010-04-01",
  "body": "Hello, there!",
  "date_created": "Thu, 30 Jul 2015 20:12:31 +0000",
  "date_sent": "Thu, 30 Jul 2015 20:12:33 +0000",
  "date_updated": "Thu, 30 Jul 2015 20:12:33 +0000",
  "direction": "outbound-api",
  "error_code": null,
  "error_message": null,
  "from": "whatsapp:+***********",
  "messaging_service_sid": "MGXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "num_media": "0",
  "num_segments": "1",
  "price": null,
  "price_unit": null,
  "sid": "MMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "status": "sent",
  "subresource_uris": {
    "media": "/2010-04-01/Accounts/ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/Messages/SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/Media.json"
  },
  "to": "whatsapp:+***********",
  "uri": "/2010-04-01/Accounts/ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/Messages/SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX.json"
}
         */
    }


    public function sendDiMessage($message, $recipients)
    {
/*
        curl -i -X POST \
   -H "Origin:https://rc-dashboard.360dialog.io" \
   -H "Content-Type:application/json" \
   -d \
'{
  "username": "firstname.lastname",
  "password": "PASSWORD"
}
 ' \
 'https://app.messagepipe.io/v1/token'


 {
    "access_token": "eyJ0eXAiOiJ...5ARX5c4Q",
    "refresh_token": "FA2Uz3IM4qerSjCkzkpYmgsSi_2hkKvv6jFkcBa6_MERw",
    "token_type": "Bearer",
    "expires_in": 86400
    }




    Sending a WhatsApp free text message


    curl -i -X POST \
   -H "Origin:https://rc-dashboard.360dialog.io" \
   -H "Authorization:Bearer ACCESS_TOKEN_FROM_ABOVE" \
   -H "Content-Type:application/json" \
   -d \
'{
  "to": [
    "***********"
  ],
  "type": "text",
  "preview_url": "false",
  "content": {"body": "Test-Message"}
}' \
 'https://app.messagepipe.io/v1/apps/244/whatsapp'






 Sending a WhatsApp template-based message
 Quite easy. type=template, id=<the messagepipe_template_id>, localizable_params=<your params>

 curl -i -X POST \
   -H "Origin:https://rc-dashboard.360dialog.io" \
   -H "Authorization:Bearer ACCESS_TOKEN_FROM_ABOVE" \
   -H "Content-Type:application/json" \
   -d \
'{
  "to": [
    "***********"
  ],
  "type": "template",
  "ttl_days": 10,
  "content": {
    "id": "r4QcRDT",
        "language": {
            "policy": "deterministic",
            "code": "de"
        },
        "localizable_params": [{
            "default": "Best agent via param"
        }]
  }
}' \
 'https://app.messagepipe.io/v1/apps/244/whatsapp'







 Sending a message with an image
Just send the image via URL and set preview_url to 'true'. Note that the content-section contains a link instead of a body.

curl -i -X POST \
   -H "Origin:https://rc-dashboard.360dialog.io" \
   -H "Authorization:Bearer ACCESS_TOKEN_FROM_ABOVE" \
   -H "Content-Type:application/json" \
   -d \
'{
    "to": [
        "***********"
    ],
    "type": "image",
    "preview_url": true,
    "content": {
        "link": "https://images.unsplash.com/photo-1583138320337-a47316bc2b86?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=934&q=80"
    }
}' \
 'https://app.messagepipe.io/v1/apps/244/whatsapp'










 Sending a message with file attachments
 first.

 and consider the type which can be image, audio, video, document.

curl -i -X POST \
   -H "Origin:https://rc-dashboard.360dialog.io" \
   -H "Authorization:Bearer ACCESS_TOKEN_FROM_ABOVE" \
   -H "Content-Type:application/json" \
   -d \
'{
    "to": [
        "***********"
    ],
    "type": "image",
    "content": {
        "filename": "test.jpg",
        "file_id": "3919d493-d908-4f44-8418-9242212feeaf",
        "mime_type": "image/jpg",
        "body": "This is a JPG-image"
    }
}' \
 'https://app.messagepipe.io/v1/apps/244/whatsapp'








        //NOW THIS IS TH EPART EJHETE WE NEED TO REFACTOR.....
        //KEEP IN MIND THAT THIS IS HE CONECTIO WITH OUR PROVIDER, SO ALSO CONSIDER DDUCTING MAINCOMPANY CEDITS HERE


        // Find your Account Sid and Auth Token at twilio.com/console
        // DANGER! This is insecure. See http://twil.io/secure
        $sid    =  config('app.twSid'); //"AC31ddd3c7832c900cb4d946c1fdf2bf85";
        $token  = config('app.twAuthToken'); //"your_auth_token";
        $from  = config('app.twWhNum'); //"your_auth_token";

        $twilio = new Client($sid, $token);

        $message = $twilio->messages
            ->create(
                "whatsapp:" . $recipients, // to  implode(',', $recipients)
                [
                    "from" => "whatsapp:+***********",
                    "body" => $message
                ]
            );
        //  print($message->sid);

*/
    }





    public function sendSingleSms($sms, $recipients, $project_id)
    {


        //NOW THIS IS TH EPART EJHETE WE NEED TO REFACTOR.....
        //KEEP IN MIND THAT THIS IS HE CONECTIO WITH OUR PROVIDER, SO ALSO CONSIDER DDUCTING MAINCOMPANY CEDITS HERE
        $client = new \GuzzleHttp\Client(['base_uri' => 'https://api.africastalking.com/']);
        $sender =  config('app.atFrom'); //$AT_FROM; //env('AT_FROM');
        $apiKey =  config('app.atKey'); //$AT_API_KEY; //env('AT_API_KEY');
        $username =   config('app.atUsername'); //$AT_USERNAME; //env('AT_USERNAME');
        // $sms  = $request->message;

        $response = $client->request('POST', '/version1/messaging', [
            'headers' => [
                'Content-Type'     => 'application/x-www-form-urlencoded',
                'Accept'     => 'application/json',
                'apiKey'     => $apiKey,
            ],
            //'auth' => env('AT_USERNAME'),
            'form_params' => [
                'username' => $username,
                'from'     => $sender,  //string
                'to'     => implode(',', $recipients), //string comma separated list of recipients
                'message'     => $sms, //string
                'bulkSMSMode'     => 1,  //integer
                //'enqueue'     => 1,  //integer
                //'keyword'     => '',  //string. keyword to be used for a premium service.
                //'linkId'     => '', //string. used for premium services to send OnDemand messages. We forward the linkId to your application when the user sends a message to your service
                //'retryDurationInHours'     => '', //string. hours your subscription message should be retried in case it’s not delivered to the subscriber.
            ]
        ]);










        // Update the path below to your autoload.php,
        // see https://getcomposer.org/doc/01-basic-usage.md

        /*
EXAMPLE JSON API RESPONSE
{
  "account_sid": "ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "api_version": "2010-04-01",
  "body": "Hello there!",
  "date_created": "Thu, 30 Jul 2015 20:12:31 +0000",
  "date_sent": "Thu, 30 Jul 2015 20:12:33 +0000",
  "date_updated": "Thu, 30 Jul 2015 20:12:33 +0000",
  "direction": "outbound-api",
  "error_code": null,
  "error_message": null,
  "from": "whatsapp:+***********",
  "messaging_service_sid": "MGXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "num_media": "0",
  "num_segments": "1",
  "price": null,
  "price_unit": null,
  "sid": "MMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "status": "sent",
  "subresource_uris": {
    "media": "/2010-04-01/Accounts/ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/Messages/SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/Media.json"
  },
  "to": "whatsapp:+***********",
  "uri": "/2010-04-01/Accounts/ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX/Messages/SMXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX.json"
}


*/










        $sender = config('app.atFrom'); //$AT_FROM; //env('AT_FROM');

        $cleanCost = function ($cost) {
            $clean_cost = preg_replace("/[^0-9\.]/", "", $cost);
            return $clean_cost;
        };

        $project = Project::find($this->project_id);

        //process response whic is actually gonna come later on whe the queued jpob is procesed
        $body = $response->getBody();
        // Implicitly cast the body to a string and echo it
        // Explicitly cast the body to a string
        $stringBody = (string) $body;
        // Read 10 bytes from the body
        $tenBytes = $body->read(10);
        // Read the remaining contents of the body as a string
        $remainingBytes = $body->getContents();
        $data = json_decode($stringBody, true);
        // Update Key
        $costs = array_map($cleanCost, array_column($data['SMSMessageData']['Recipients'], 'cost'));
        $messageIds = array_column($data['SMSMessageData']['Recipients'], 'messageId');
        $messageParts = array_column($data['SMSMessageData']['Recipients'], 'messageParts');
        $numbers = array_column($data['SMSMessageData']['Recipients'], 'number');
        $statuses = array_column($data['SMSMessageData']['Recipients'], 'status');
        $statusCodes = array_column($data['SMSMessageData']['Recipients'], 'statusCode');
        $project_ids = array_fill(0, count($messageIds), $project_id);
        $messages = array_fill(0, count($messageIds), $this->sms);
        $froms = array_fill(0, count($messageIds), $sender);
        $details = array_fill(0, count($messageIds), $data['SMSMessageData']['Message']);
        $created_ats = array_fill(0, count($messageIds), Carbon::now());
        $campaignIds = array_fill(0, count($messageIds), $this->campaign_id);
        $tem_wzIds = array_fill(0, count($messageIds), 'tz');

        $make_wzId = function () {
            return strtoupper(uniqid('tz'));
        };
        $wzIds = array_map($make_wzId, $tem_wzIds);  //important to give each message a unique id

        $project->update(['credits' => ($project->credits - array_sum($messageParts))]);  //message parts are the real count of sms sent!

        $sms_result = array_map(function ($wzId, $cost, $messageId, $messagePart, $number, $status, $statusCode, $project_id, $message, $from, $detail, $created_at, $campaignId) {
            return array_combine(
                ['wzId', 'cost', 'messageId', 'messagePart', 'number', 'status', 'statusCode', 'project_id', 'message', 'from', 'detail', 'created_at', 'campaign_id'],
                [$wzId, $cost, $messageId, $messagePart, $number, $status, $statusCode, $project_id, $message, $from, $detail, $created_at, $campaignId]
            );
        }, $wzIds, $costs, $messageIds, $messageParts, $numbers, $statuses, $statusCodes, $project_ids, $messages, $froms, $details, $created_ats, $campaignIds);

        DB::table('sms')->insert($sms_result);
        //Sms::create($sms_result);

        $sms_api_result = array_map(function ($wzId, $messagePart, $number, $status,  $message) {
            return array_combine(
                ['tzId', 'sms_credits', 'to', 'status',  'message'],
                [$wzId, $messagePart, $number, $status, $message]
            );
        },  $wzIds, $messageParts, $numbers, $statuses, $messages);


        return response()->json(["Result" => $sms_api_result ? $sms_api_result : "empty"]);
    }
}
