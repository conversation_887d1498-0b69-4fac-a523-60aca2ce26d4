<?php
namespace App\Traits;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Payment;
use Carbon\Carbon;
use App\Paymentmethod;
use App\Paymentstatus;
use App\Enrollment;
use App\Plan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

/**
 * class MSG91 to send SMS on Mobile Numbers.
 * <AUTHOR>
 */
trait Ravepay
{
    /* private $API_KEY = 'API_KEY';
    private $SENDER_ID = "VERIFY";
    private $ROUTE_NO = 4;
    */
    public function initiatePay($customer_email,$enrollment_plan_id = null,$customer_phone, $amount, $currency, $project_id)
    {
        $curl = curl_init();
       // $customer_email = "<EMAIL>";
        //$amount = 10;
        $currency = "KES";

        $txref = uniqid('Sozr'); // ensure you generate unique references per transaction.
        $PBFPubKey =   config('app.RAVE_PUBLIC_KEY'); //env('RAVE_PUBLIC_KEY'); // get your public key from the dashboard.
        $redirect_url =  config('app.RAVE_CALLBACK_URL'); //"http://homestead.talkzuri.com/rave/handle";//env('RAVE_CALLBACK_URL');
        $payment_plan = "plan id"; // this is only required for recurring payments.
        $pay_button_text = "Pay Now";
        $custom_title = "Sozuri Top up";
        $custom_description = "Sozuri Secure Top up";
        $payment_options = "mpesa";
        $custom_logo = config('app.RAVE_LOGO_URL'); //"https://sozuri.net/ave/assets/img/logo/logo-2.svg";

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.ravepay.co/flwv3-pug/getpaidx/api/v2/hosted/pay",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode([
                'amount' => $amount,
                'customer_email' => $customer_email,
                'customer_phone' => $customer_phone,
                'currency' => $currency,
                'country' => "KE",
                'txref' => $txref,
                'PBFPubKey' => $PBFPubKey,
                'redirect_url' => $redirect_url,
                'payment_plan' => $payment_plan,
                'pay_button_text' => $pay_button_text,
                'custom_title' => $custom_title,
                'custom_description' => $custom_description,
                'payment_options' => $payment_options,
                'custom_logo' => $custom_logo,
            ]),
            CURLOPT_HTTPHEADER => [
                "content-type: application/json",
                "cache-control: no-cache"
            ],
        ));

        $response = curl_exec($curl);
        Log::info(json_encode($response));

        //"{"status":"success","message":"Hosted Link","data":{"link":"https://ravemodal-dev.herokuapp.com/hosted/pay/30fa7b93fdcee3b50b2d"}}"
        $err = curl_error($curl);
        if ($err) {
            // there was an error contacting the rave API
            die('Curl returned error: ' . $err);
        }

        $transaction = json_decode($response);
        if (!$transaction->data && !$transaction->data->link) {
            // there was an error from the API
            print_r('API returned error: ' . $transaction->message);
        }
        // uncomment out this line if you want to redirect the user to the payment page
        //print_r($transaction->data->message);
        $payment = new payment();
       // $payment->transaction_id =  $transaction_id;
        $payment->txRef =  $txref;//unique internal ID
        $payment->project_id =  $project_id;
        $payment->tx_customer_phone =  $customer_phone ;
        $payment->tx_customer_email  =  $customer_email ;
        $payment->tx_status  =  'new' ;
        $payment->status  =  'pend' ;
        $payment->unit_cost = 0;
        $payment->credit = "units";
        $payment->debit = "cash";
        $payment->amount  =  $amount ;
        $payment->currency  =  $currency ;
        $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'card' . '%')->exists() ?
        Paymentmethod::where('name', 'like', 'card'.'%')->value('id') :
        Paymentmethod::insertGetId(['name' => 'card','detail' => 'rave', 'created_by' => Auth::id(), ]);
        $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'new' . '%')->exists() ?
        Paymentstatus::where('name', 'like', 'new'.'%')->value('id') :
        Paymentstatus::insertGetId(['name' => 'new','detail' => 'new', 'created_by' => Auth::id(), ]);
        $payment->detail  =  empty($enrollment_plan_id) ? 'recharge':'enrollment';//enrollment
        $payment->save();

        if(! is_null($enrollment_plan_id) && $enrollment_plan_id > 0 ){
            //if enrollment for this project exists, update it
         $enrollment = new Enrollment();
         $enrollment->user_id = Auth::id();
         $enrollment->project_id = $project_id;
         $enrollment->plan_id = $enrollment_plan_id;
         $enrollment->payment_id = $payment->id;
         $enrollment->start = Carbon::now();
         $enrollment->end = Carbon::now()->addDays(45); //take for 30days
         $enrollment->credits = Plan::find($enrollment_plan_id)->credits;//to award
         $enrollment->status = 'new';
         $enrollment->isActive = false;
         $enrollment->created_by = Auth::id();
         $enrollment->updated_by = Auth::id();
         $enrollment->save();

         DB::table('enrollment_payment')->insert([
             'enrollment_id' => $enrollment->id,
             'user_id' => Auth::id(),
             'amount' => $amount,
             'payment_id' => $payment->id,
             'plan_id' => $enrollment_plan_id,
             'project_id'=> $project_id
         ]);
         }

        // redirect to page so User can pay
        // uncomment this line to allow the user redirect to the payment page
        header('Location: ' . $transaction->data->link);
    }

    public function handleCallback($request, $signature)
    {
        //collect detals
        //process
        //persist
        // Retrieve the request's body
        $body = $request; //@file_get_contents("php://input");
        Log::info($body);

        Log::info(json_encode($request));
        //on cancle
        //resp  {"cancelled":true}
        if ($body['cancelled'] == "true" || $body['cancelled'] == true) {
            return null;  //return a value that the main function can parse
       }
       $resp = json_decode($body['resp'] );

        $status = $resp->tx->status ;
        $txRef =  $resp->tx->txRef;
        $tx =  $resp->tx ;

        /* It is a good idea to log all events received. Add code here to log the signature and body to db or file*/
        if (!$signature) {
            // only a post with rave signature header gets our attention
            // exit();
        }
        // Store the same signature on your server as an env variable and check against what was sent in the headers
        $local_signature = getenv('SECRET_HASH');
        // confirm the event's signature
        if ($signature !== $local_signature) {
            // silently forget this ever happened
          //  exit();
        }

        http_response_code(200); // PHP 5.4 or greater
        // parse event (which is json string) as object
        // Give value to your customer but don't give any output
        // Remember that this is a call from rave's servers and
        // Your customer is not seeing the response here at all
        ///$response = json_decode($body);
        if ($status == 'successful') {
            # code...
            // TIP: you may still verify the transaction
            // before giving value.
            //was it an enrollment, update it
            //...

            $payment = Payment::where('txRef','=', $txRef )->where('tx_status','!=','completed')->exists() ?
            Payment::where('txRef','=', $txRef )->where('tx_status','!=','completed')->first() : null;
            //if payment ref doesnt exist, skip
            if(!$payment){ exit(); }

//update enrollment
            if(Enrollment::where('payment_id', $payment->id)->exists()) {
                $enrollment = Enrollment::where('payment_id', $payment->id)->first();
            $enrollment->status = 'completed';
            $enrollment->isActive = true;
            $enrollment->save();
            }


            $body = $request; //@file_get_contents("php://input");
            $resp = json_decode($body['resp'] );
            $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'completed' . '%')->exists() ?
            Paymentstatus::where('name', 'like', 'completed'.'%')->value('id') :
            Paymentstatus::insertGetId(['name' => 'completed','detail' => 'completed', 'created_by' => Auth::id(), ]);

            $payment->name = $tx->raveRef ;//$resp->name;
            $payment->status = $status ;
            $payment->tx_status = "completed";

            //$resp_data_data_txRef = $resp->data->data->txRef;
            $payment->amount  = $tx->amount ; //$resp->data->data->amount;

            //$payment->respcode = $resp->respcode;
            //$payment->resp_respmsg = $resp->respmsg;
            $payment->flwRef = $tx->flwRef ; //$body['flwref'];
            $payment->txRef =  $tx->txRef ; //$body['txRef'];
            $payment->body =  $body;


             $payment->tx_id =  $tx->id ;
             $payment->tx_orderRef =  $tx->orderRef ;
             $payment->tx_flwRef =  $tx->flwRef ;
             $payment->tx_redirectUrl =  $tx->redirectUrl ;
             $payment->tx_device_fingerprint =  $tx->device_fingerprint ;
             $payment->tx_settlement_token =  $tx->settlement_token ;
             $payment->tx_cycle =  $tx->cycle ;
             $payment->tx_amount =  $tx->amount ;
             $payment->tx_charged_amount =  $tx->charged_amount ;
             $payment->tx_appfee =  $tx->appfee ;
             $payment->tx_merchantfee =  $tx->merchantfee ;
             $payment->tx_merchantbearsfee =  $tx->merchantbearsfee ;
             $payment->tx_chargeResponseCode =  $tx->chargeResponseCode ;
             $payment->tx_raveRef =  $tx->raveRef ;
             $payment->tx_chargeResponseMessage =  $tx->chargeResponseMessage ;
             $payment->tx_authModelUsed =  $tx->authModelUsed ;
             $payment->tx_currency =  $tx->currency ;
             $payment->tx_IP =  $tx->IP ;
             $payment->tx_narration =  $tx->narration ;
             $payment->tx_modalauditid =  $tx->modalauditid ;
             $payment->tx_vbvrespmessage =  $tx->vbvrespmessage ;
             $payment->tx_authurl =  $tx->authurl ;
             $payment->tx_vbvrespcode =  $tx->vbvrespcode ;
             $payment->tx_acctvalrespmsg =  $tx->acctvalrespmsg ;
             $payment->tx_acctvalrespcode =  $tx->acctvalrespcode ;
             $payment->tx_paymentType =  $tx->paymentType ;
             $payment->tx_paymentPlan =  $tx->paymentPlan ;
             $payment->tx_paymentPage =  $tx->paymentPage ;
             $payment->tx_paymentId =  $tx->paymentId ;
             $payment->tx_fraud_status =  $tx->fraud_status ;
             $payment->tx_charge_type =  $tx->charge_type ;
             $payment->tx_is_live =  $tx->is_live ;
             $payment->tx_retry_attempt =  $tx->retry_attempt ;
             $payment->tx_getpaidBatchId =  $tx->getpaidBatchId ;
             $payment->tx_createdAt =  Carbon::parse($tx->createdAt) ;
             $payment->tx_updatedAt =  Carbon::parse($tx->updatedAt) ;
             $payment->tx_deletedAt =  Carbon::parse($tx->deletedAt) ;
             $payment->tx_customerId =  $tx->customerId ;
             $payment->tx_AccountId =  $tx->AccountId ;
             $payment->tx_customer_id  =  $tx->customer->id ;
             $payment->tx_customer_phone =  $tx->customer->phone ;
             $payment->tx_customer_fullName  =  $tx->customer->fullName ;
             $payment->tx_customer_customertoken  =  $tx->customer->customertoken ;
             $payment->tx_customer_email  =  $tx->customer->email ;
             $payment->tx_customer_createdAt  =  Carbon::parse($tx->customer->createdAt) ;
             $payment->tx_customer_updatedAt  =  Carbon::parse($tx->customer->updatedAt) ;
             $payment->tx_customer_deletedAt  =  Carbon::parse($tx->customer->deletedAt );
             $payment->tx_customer_AccountId  =  $tx->customer->AccountId ;
            // $payment->tx_chargeToken_user_token  =  $tx->chargeToken->user_token ;
            // $payment->tx_chargeToken_embed_token  =  $tx->chargeToken->embed_token ;
             //$payment->airtime_flag  =  $tx->airtime_flag ;
             $payment->updated_at = Carbon::now();
             $payment->save();

             return $payment;

        }
        exit();
    }
    public function sendScheduleSms($message, $recipients, $time)
    {
        $sender = 'BeterGames';
        //$message = "hello Augu";
        /*$recipient = DB::table('users')
            ->where('isVerified', '=','1')
            ->get();*/
        //$time = time();
        //$time = time() + (7 * 24 * 60 * 60);
        //date_default_timezone_set("Europe/Helsinki");    //set the timezone
        //echo "Europe/Helsinki:".time();
        $url =  'https://mysms.nsano.com/api/v1/sms/schedule';
        $ch = curl_init($url);
        # Setup request to send json via POST.
        $payload = json_encode(array("sender" => $sender, "recipients" => $recipient, "message" => $message, "run_at" => $time));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'Accept: application/json', 'X-SMS-Apikey:97553896e0c67843c873ca09577e5755'));
        # Return response instead of printing.
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        # Send request.
        $result = curl_exec($ch);

        //var_dump($result);
        DB::table('sms')->insert(
            ['mobile' => $recipients, 'message' => $message, 'sp' => 'Nsano', 'created_at' => date('Y-m-d H:i:s'), 'response' => json_encode($result, true)]
        );
        curl_close($ch);
    }
}
