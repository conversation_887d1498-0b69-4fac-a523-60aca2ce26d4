<?php

namespace App\Traits;

use Exception;
use Illuminate\Support\Facades\DB;
use App\Payment;
use Carbon\Carbon;
use App\Paymentmethod;
use App\Paymentstatus;
use App\Enrollment;
use App\Plan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

/**
 * class MSG91 to send SMS on Mobile Numbers.
 * <AUTHOR>
 */
trait Flutterwavepay
{
    /* private $API_KEY = 'API_KEY';
    private $SENDER_ID = "VERIFY";
    private $ROUTE_NO = 4;
    */
    public function initiateFlutterPay($customer_email,$customer_name, $enrollment_plan_id = null, $customer_phone, $amount, $currency, $project_id)
    {
        $curl = curl_init();
        //$customer_email = "<EMAIL>";
        //$amount = 10;
        $currency = "KES";
        $txref = uniqid('Sozfr'); // ensure you generate unique references per transaction.
        $PBFPubKey =   config('app.FLUTTERWAVE_PUBLIC_KEY'); //env('RAVE_PUBLIC_KEY'); // get your public key from the dashboard.
        $PBFSecKey =   config('app.FLUTTERWAVE_SECRET_KEY'); //env('RAVE_PUBLIC_KEY');
        $redirect_url =  config('app.FLUTTERWAVE_CALLBACK_URL'); //"http://homestead.talkzuri.com/rave/handle";//env('RAVE_CALLBACK_URL');
        $custom_logo = config('app.RAVE_LOGO_URL'); //"https://sozuri.net/ave/assets/img/logo/logo-2.svg";
        $payment_plan = "plan id"; // this is only required for recurring payments.
        $pay_button_text = "Pay Now";
        $custom_title = "Sozuri Top up";
        $custom_description = "Sozuri Secure Top up";
        $payment_options = "mpesa,card";

        curl_setopt_array($curl, array(
            CURLOPT_URL => "https://api.flutterwave.com/v3/payments",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode([
                'tx_ref' => $txref,
                'amount' => $amount,
                'currency' => $currency,
                'redirect_url' => $redirect_url,
                'payment_options' => $payment_options,
                'meta' => [
                    "consumer_id" => 23,
                    "consumer_mac" => "92a3-912ba-1192a"
                ],
                'customer' => [
                    "email" => $customer_email,
                    "phonenumber" => $customer_phone,
                    "name" => $customer_name
                ],
                'customizations' => [
                    "title" => $custom_title,
                    "description" => $custom_description,
                    "logo" => $custom_logo
                ]
            ]),
            CURLOPT_HTTPHEADER => [
                "content-type: application/json",
                "cache-control: no-cache",
                "Authorization: Bearer " . $PBFSecKey
            ],
        ));

        $response = curl_exec($curl);
        Log::info(json_encode($response));

        //"{"status":"success","message":"Hosted Link","data":{"link":"https://ravemodal-dev.herokuapp.com/hosted/pay/30fa7b93fdcee3b50b2d"}}"
        $err = curl_error($curl);

        if ($err) { // there was an error contacting the rave API
            die('Curl returned error: ' . $err);
        }
        $transaction = json_decode($response);
        print_r('API returned error: ' . $transaction->message);

        if (!$transaction->data && !$transaction->data->link) {
            // there was an error from the API
            print_r('API returned error: ' . $transaction->message);
        }
        // uncomment out this line if you want to redirect the user to the payment page
        //print_r($transaction->data->message);
        $payment = new payment();
        // $payment->transaction_id =  $transaction_id;
        $payment->txRef =  $txref; //unique internal ID
        $payment->project_id =  $project_id;
        $payment->tx_customer_phone =  $customer_phone;
        $payment->tx_customer_email  =  $customer_email;
        $payment->tx_status  =  'new';
        $payment->status  =  'pend';
        $payment->unit_cost = 0;
        $payment->credit = "units";
        $payment->debit = "cash";
        $payment->amount  =  $amount;
        $payment->currency  =  $currency;
        $payment->paymentmethod_id = Paymentmethod::where('name', 'like', 'rave' . '%')->exists() ?
            Paymentmethod::where('name', 'like', 'rave' . '%')->value('id') :
            Paymentmethod::insertGetId(['name' => 'rave', 'detail' => 'rave', 'created_by' => Auth::id(),]);
        $payment->paymentstatus_id = Paymentstatus::where('name', 'like', 'new' . '%')->exists() ?
            Paymentstatus::where('name', 'like', 'new' . '%')->value('id') :
            Paymentstatus::insertGetId(['name' => 'new', 'detail' => 'new', 'created_by' => Auth::id(),]);
        $payment->detail  =  empty($enrollment_plan_id) ? 'recharge' : 'enrollment'; //enrollment
        $payment->save();
        //if enrollment for this project exists, update it
        if (!is_null($enrollment_plan_id) && $enrollment_plan_id > 0) {
            $enrollment = new Enrollment();
            $enrollment->user_id = Auth::id();
            $enrollment->project_id = $project_id;
            $enrollment->plan_id = $enrollment_plan_id;
            $enrollment->payment_id = $payment->id;
            $enrollment->start = Carbon::now();
            $enrollment->end = Carbon::now()->addDays(45); //take for 30days
            $enrollment->credits = Plan::find($enrollment_plan_id)->credits; //to award
            $enrollment->status = 'new';
            $enrollment->isActive = false;
            $enrollment->created_by = Auth::id();
            $enrollment->updated_by = Auth::id();
            $enrollment->save();
            DB::table('enrollment_payment')->insert([ 
                'enrollment_id' => $enrollment->id,
                'user_id' => Auth::id(),
                'amount' => $amount,
                'payment_id' => $payment->id,
                'plan_id' => $enrollment_plan_id,
                'project_id' => $project_id
            ]);
        }
        header('Location: ' . $transaction->data->link);
    }
}
