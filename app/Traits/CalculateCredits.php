<?php

namespace App\Traits;


/**
 * class to calculate recharge units 
 * <AUTHOR>
 */
trait CalculateCredits
{

    public function calculateSafaricomCredits($amount)
    {
        $DIAMOND = config('app.DIAMOND');
        $RUBY = config('app.RUBY');
        $BRONZE = config('app.BRONZE');

      if ($amount > $DIAMOND) {
            return  0.38;
        } elseif ($amount >  $RUBY) {
            return  0.58;
        } elseif ($amount > $BRONZE) { 
            return   0.68;
        } else {
            return 0.78;
        }
    }

    public function calculateAirtelCredits($amount)
    {
        $DIAMOND = config('app.airtelDIAMOND');
        $RUBY = config('app.airtelRUBY');
        $BRONZE = config('app.airtelBRONZE');

     if ($amount >= $DIAMOND) {
            return  0.48;
        } elseif ($amount >  $RUBY) {
            return   0.68;
        } elseif ($amount > $BRONZE) {
            return   0.78;
        } else {
            return   0.88;
        }
    }
    public function calculateTelkomCredits($amount)
    {
        $DIAMOND = config('app.telkomDIAMOND');
        $RUBY = config('app.telkomRUBY');
        $BRONZE = config('app.telkomBRONZE');

    if ($amount >= $DIAMOND) {
            return  0.70;
        } elseif ($amount >  $RUBY) {
            return   0.80;
        } elseif ($amount > $BRONZE) {
            return   0.90;
        } else {
            return   1.00;
        }
    }

    public function calculateInternationalCredits($amount)
    {
            return   0.78;
    }

    public function calculateOtherCredits($amount)
    {
            return   0.78;
    }
}
