<?php

namespace App;
use Illuminate\Contracts\Auth\MustVerifyEmail;

use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Notifications\CustomResetPasswordNotification;

class User extends Authenticatable/* implements MustVerifyEmail*/
{
    use Notifiable, HasFactory;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    //protected $fillable = [ 'name', 'email', 'password',];
        //
        protected $guarded = ['id', 'otp2','isOfficer','isManager','isAdmin','isGlobalAdmin'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'otp', 'otp2'
    ];

    public function projects(){
        return $this->hasMany('App\Project');
    }

    public function roles(){
        return $this->belongsToMany('App\Role');
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        // Pass the token to the custom notification
        $this->notify(new CustomResetPasswordNotification($token));
    }


}
