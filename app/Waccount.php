<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Waccount extends Model
{
    //
    protected $guarded= ['id'];

    public function wbusinesses(){
        return $this->hasMany('App\Wbusiness');
    }
    public function wmessages(){
        return $this->belongsTo('App\Wmessage');
    }
    public function wnumbers(){
        return $this->belongsTo('App\Wnumber');
    }
    public function wtemplates(){
        return $this->belongsTo('App\Wtemplate');
    }
    public function wwebhooks(){
        return $this->belongsTo('App\Wwebhook');
    }
    public function wprofilepic(){
        return $this->belongsTo('App\Wprofilepic');
    }
}
