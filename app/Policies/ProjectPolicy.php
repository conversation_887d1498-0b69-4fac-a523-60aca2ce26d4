<?php

namespace App\Policies;

use App\User;
use App\Project;
use App\Collaboration;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProjectPolicy
{
    use HandlesAuthorization;


    /**
     * Determine whether the user can view any plans.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //your projects
        return true;

    }
    /**
     * Determine whether the user can view the project.
     *
     * @param  \App\User  $user
     * @param  \App\Project  $project
     * @return bool
     */
    public function view(User $user, Project $project)
    {
        //user should be 

            return true;


    }

    /**
     * Determine whether the user can create projects.
     *
     * @param  \App\User  $user
     * @return bool
     */
    public function create(User $user)
    {
        return true;



        //return  $user->isManager === 1 || $user->isAdmin === 1;
        //user must belong/own a project and with necessary rights or be a collaborator of a project with coect role to access

    }

    /**
     * Determine whether the user can update the project.
     *
     * @param  \App\User  $user
     * @param  \App\Project  $project
     * @return bool
     */
    public function update(User $user, Project $project)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $project->id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the project.
     *
     * @param  \App\User  $user
     * @param  \App\Project  $project
     * @return bool
     */
    public function delete(User $user, Project $project)
    {
        $projects =  User::find($user->id)->projects()->get();
        if ( $projects->where('id', $project->project_id)->count()  ===  1) {
            return true;
        }
        return false;
    }
}
