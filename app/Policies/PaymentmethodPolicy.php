<?php

namespace App\Policies;

use App\Paymentmethod;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class PaymentmethodPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any paymentmethods.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
            return $user->isGlobalAdmin == 1 ? true :false;

    }

    /**
     * Determine whether the user can view the paymentmethod.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentmethod  $paymentmethod
     * @return mixed
     */
    public function view(User $user, Paymentmethod $paymentmethod)
    {
        //
    }

    /**
     * Determine whether the user can create paymentmethods.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the paymentmethod.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentmethod  $paymentmethod
     * @return mixed
     */
    public function update(User $user, Paymentmethod $paymentmethod)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the paymentmethod.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentmethod  $paymentmethod
     * @return mixed
     */
    public function delete(User $user, Paymentmethod $paymentmethod)
    {
        //
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the paymentmethod.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentmethod  $paymentmethod
     * @return mixed
     */
    public function restore(User $user, Paymentmethod $paymentmethod)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the paymentmethod.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentmethod  $paymentmethod
     * @return mixed
     */
    public function forceDelete(User $user, Paymentmethod $paymentmethod)
    {
        //
    }
}
