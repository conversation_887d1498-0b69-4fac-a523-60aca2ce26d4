<?php

namespace App\Policies;

use App\Transactiontype;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class TransactiontypePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any transactiontypes.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user, $id)
    {
        return $user->isGlobalAdmin == 1 ? true :false;
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the transactiontype.
     *
     * @param  \App\User  $user
     * @param  \App\Transactiontype  $transactiontype
     * @return mixed
     */
    public function view(User $user, Transactiontype $transactiontype)
    {
        //
    }

    /**
     * Determine whether the user can create transactiontypes.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        return $user->isGlobalAdmin == 1 ? true :false;
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the transactiontype.
     *
     * @param  \App\User  $user
     * @param  \App\Transactiontype  $transactiontype
     * @return mixed
     */
    public function update(User $user, Transactiontype $transactiontype)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the transactiontype.
     *
     * @param  \App\User  $user
     * @param  \App\Transactiontype  $transactiontype
     * @return mixed
     */
    public function delete(User $user, Transactiontype $transactiontype)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the transactiontype.
     *
     * @param  \App\User  $user
     * @param  \App\Transactiontype  $transactiontype
     * @return mixed
     */
    public function restore(User $user, Transactiontype $transactiontype)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the transactiontype.
     *
     * @param  \App\User  $user
     * @param  \App\Transactiontype  $transactiontype
     * @return mixed
     */
    public function forceDelete(User $user, Transactiontype $transactiontype)
    {
        //
    }
}
