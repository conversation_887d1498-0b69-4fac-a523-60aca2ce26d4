<?php

namespace App\Policies;

use App\User;
use App\Contact;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class ContactPolicy
{
    use HandlesAuthorization;
    /**
     * Determine whether the user can view any issues.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user,$id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can view the contact.
     *
     * @param  \App\User  $user
     * @param  \App\Contact  $contact
     * @return bool
     */
    public function view(User $user, Contact $contact)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $contact->project_id)->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can create contacts.
     *
     * @param  \App\User  $user
     * @return bool
     */
    public function create(User $user,$id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the contact.
     *
     * @param  \App\User  $user
     * @param  \App\Contact  $contact
     * @return bool
     */
    public function update(User $user, Contact $contact)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $contact->project_id)->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;

    }
    

    /**
     * Determine whether the user can delete the contact.
     *
     * @param  \App\User  $user
     * @param  \App\Contact  $contact
     * @return bool
     */
    public function delete(User $user, Contact $contact)
    {
       // $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        //$collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ;
        //if both are empty
        if ( $projects->where('id', $contact->project_id)->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return true;

    }
}
