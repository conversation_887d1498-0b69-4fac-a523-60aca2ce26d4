<?php

namespace App\Policies;

use App\User;
use App\Linknotice;
use App\Project;
use App\Collaboration;
use Illuminate\Auth\Access\HandlesAuthorization;

class LinknoticePolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }
    public function viewAny(User $user,$id)
    {
        //
                //
                $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
                $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
                $own_projects = User::find($user->id)->projects()->get();
                $projects = $own_projects ->merge( $collabo_projects );
                if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
                    return true;
                }
                return false;
    }

    /**
     * Determine whether the user can view the linknotice.
     *
     * @param  \App\User  $user
     * @param  \App\Linknotice  $linknotice
     * @return mixed
     */
    public function view(User $user, Linknotice $linknotice)
    {
        //
    }

    /**
     * Determine whether the user can create premia.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can update the linknotice.
     *
     * @param  \App\User  $user
     * @param  \App\Linknotice  $linknotice
     * @return mixed
     */
    public function update(User $user, Linknotice $linknotice)
    {
        //
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the linknotice.
     *
     * @param  \App\User  $user
     * @param  \App\Linknotice  $linknotice
     * @return mixed
     */
    public function delete(User $user, Linknotice $linknotice)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the linknotice.
     *
     * @param  \App\User  $user
     * @param  \App\Linknotice  $linknotice
     * @return mixed
     */
    public function restore(User $user, Linknotice $linknotice)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the linknotice.
     *
     * @param  \App\User  $user
     * @param  \App\Linknotice  $linknotice
     * @return mixed
     */
    public function forceDelete(User $user, Linknotice $linknotice)
    {
        //
    }
}


