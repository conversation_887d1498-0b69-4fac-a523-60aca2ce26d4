<?php

namespace App\Policies;

use App\Paymentstatus;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class PaymentstatusPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any paymentstatuses.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the paymentstatus.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentstatus  $paymentstatus
     * @return mixed
     */
    public function view(User $user, Paymentstatus $paymentstatus)
    {
        //
    }

    /**
     * Determine whether the user can create paymentstatuses.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the paymentstatus.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentstatus  $paymentstatus
     * @return mixed
     */
    public function update(User $user, Paymentstatus $paymentstatus)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the paymentstatus.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentstatus  $paymentstatus
     * @return mixed
     */
    public function delete(User $user, Paymentstatus $paymentstatus)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the paymentstatus.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentstatus  $paymentstatus
     * @return mixed
     */
    public function restore(User $user, Paymentstatus $paymentstatus)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the paymentstatus.
     *
     * @param  \App\User  $user
     * @param  \App\Paymentstatus  $paymentstatus
     * @return mixed
     */
    public function forceDelete(User $user, Paymentstatus $paymentstatus)
    {
        //
    }
}
