<?php

namespace App\Policies;

use App\User;
use App\Keyword;
use App\Project;
use App\Collaboration;
use Illuminate\Auth\Access\HandlesAuthorization;

class KeywordPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }
    public function viewAny(User $user,$id)
    {
        //
                //
                $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
                $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
                $own_projects = User::find($user->id)->projects()->get();
                $projects = $own_projects ->merge( $collabo_projects );
                if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
                    return true;
                }
                return false;
    }

    /**
     * Determine whether the user can view the keyword.
     *
     * @param  \App\User  $user
     * @param  \App\Keyword  $keyword
     * @return mixed
     */
    public function view(User $user, Keyword $keyword)
    {
        //
    }

    /**
     * Determine whether the user can create premia.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can update the keyword.
     *
     * @param  \App\User  $user
     * @param  \App\Keyword  $keyword
     * @return mixed
     */
    public function update(User $user, Keyword $keyword)
    {
        //
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the keyword.
     *
     * @param  \App\User  $user
     * @param  \App\Keyword  $keyword
     * @return mixed
     */
    public function delete(User $user, Keyword $keyword)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the keyword.
     *
     * @param  \App\User  $user
     * @param  \App\Keyword  $keyword
     * @return mixed
     */
    public function restore(User $user, Keyword $keyword)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the keyword.
     *
     * @param  \App\User  $user
     * @param  \App\Keyword  $keyword
     * @return mixed
     */
    public function forceDelete(User $user, Keyword $keyword)
    {
        //
    }
}
