<?php

namespace App\Policies;

use App\User;
use App\Collaboration;
use App\Project;
use App\Wappmessage;
use Illuminate\Auth\Access\HandlesAuthorization;

class WappmessagePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any wmessages.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user, $id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
            return true;
        }
        return false;
        }

    /**
     * Determine whether the user can view the wmessage.
     *
     * @param  \App\User  $user
     * @param  \App\Wappmessage  $wmessage
     * @return mixed
     */
    public function view(User $user, Wappmessage $wmessage)
    {
        //
    }

    /**
     * Determine whether the user can create wmessages.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the wmessage.
     *
     * @param  \App\User  $user
     * @param  \App\Wappmessage  $wmessage
     * @return mixed
     */
    public function update(User $user, Wappmessage $wmessage)
    {
        //
        return false;
    }

    /**
     * Determine whether the user can delete the wmessage.
     *
     * @param  \App\User  $user
     * @param  \App\Wappmessage  $wmessage
     * @return mixed
     */
    public function delete(User $user, Wappmessage $wmessage)
    {
        //

        return false;
    }

    /**
     * Determine whether the user can restore the wmessage.
     *
     * @param  \App\User  $user
     * @param  \App\Wappmessage  $wmessage
     * @return mixed
     */
    public function restore(User $user, Wappmessage $wmessage)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the wmessage.
     *
     * @param  \App\User  $user
     * @param  \App\Wappmessage  $wmessage
     * @return mixed
     */
    public function forceDelete(User $user, Wappmessage $wmessage)
    {
        //
    }
}
