<?php

namespace App\Policies;

use App\Queueautomation;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class QueueautomationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any queueautomations.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
                //
                $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
                $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
                $own_projects = User::find($user->id)->projects()->get();
                $projects = $own_projects ->merge( $collabo_projects );
                if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
                    return true;
                }
                return false;
    }

    /**
     * Determine whether the user can view the queueautomation.
     *
     * @param  \App\User  $user
     * @param  \App\Queueautomation  $queueautomation
     * @return mixed
     */
    public function view(User $user, Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Determine whether the user can create queueautomations.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user)
    {
        //
    }

    /**
     * Determine whether the user can update the queueautomation.
     *
     * @param  \App\User  $user
     * @param  \App\Queueautomation  $queueautomation
     * @return mixed
     */
    public function update(User $user, Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Determine whether the user can delete the queueautomation.
     *
     * @param  \App\User  $user
     * @param  \App\Queueautomation  $queueautomation
     * @return mixed
     */
    public function delete(User $user, Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Determine whether the user can restore the queueautomation.
     *
     * @param  \App\User  $user
     * @param  \App\Queueautomation  $queueautomation
     * @return mixed
     */
    public function restore(User $user, Queueautomation $queueautomation)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the queueautomation.
     *
     * @param  \App\User  $user
     * @param  \App\Queueautomation  $queueautomation
     * @return mixed
     */
    public function forceDelete(User $user, Queueautomation $queueautomation)
    {
        //
    }
}
