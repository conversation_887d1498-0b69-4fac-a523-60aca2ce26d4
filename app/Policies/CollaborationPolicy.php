<?php

namespace App\Policies;

use App\Collaboration;
use App\User;
use App\Project;

use Illuminate\Auth\Access\HandlesAuthorization;

class CollaborationPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any collaborations.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user,$id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->where('role','admin')->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the collaboration.
     *
     * @param  \App\User  $user
     * @param  \App\Collaboration  $collaboration
     * @return mixed
     */
    public function view(User $user, Collaboration $collaboration)
    {
        //
    }

    /**
     * Determine whether the user can create collaborations.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->where('role','admin')->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the collaboration.
     *
     * @param  \App\User  $user
     * @param  \App\Collaboration  $collaboration
     * @return mixed
     */
    public function update(User $user, Collaboration $collaboration)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->where('role','admin')->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $collaboration->project_id )->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can delete the collaboration.
     *
     * @param  \App\User  $user
     * @param  \App\Collaboration  $collaboration
     * @return mixed
     */
    public function delete(User $user, Collaboration $collaboration)
    {
        //
        $projects = User::find($user->id)->projects()->get();
        if ( $projects->where('id', $collaboration->project_id )->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the collaboration.
     *
     * @param  \App\User  $user
     * @param  \App\Collaboration  $collaboration
     * @return mixed
     */
    public function restore(User $user, Collaboration $collaboration)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the collaboration.
     *
     * @param  \App\User  $user
     * @param  \App\Collaboration  $collaboration
     * @return mixed
     */
    public function forceDelete(User $user, Collaboration $collaboration)
    {
        //
    }
}
