<?php

namespace App\Policies;

use App\Smscallback;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class SmscallbackPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any smscallbacks.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user, $id)
    {
                $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
                $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
                $own_projects = User::find($user->id)->projects()->get();
                $projects = $own_projects ->merge( $collabo_projects );
                if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
                    return true;
                }
                return false;
    }

    /**
     * Determine whether the user can view the smscallback.
     *
     * @param  \App\User  $user
     * @param  \App\Smscallback  $smscallback
     * @return mixed
     */
    public function view(User $user, Smscallback $smscallback)
    {
        //
    }

    /**
     * Determine whether the user can create smscallbacks.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can update the smscallback.
     *
     * @param  \App\User  $user
     * @param  \App\Smscallback  $smscallback
     * @return mixed
     */
    public function update(User $user, Smscallback $smscallback)
    {
        //
         $projects =  User::find($user->id)->projects()->get();
        if ( $projects->where('id', $smscallback->project_id )->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the smscallback.
     *
     * @param  \App\User  $user
     * @param  \App\Smscallback  $smscallback
     * @return mixed
     */
    public function delete(User $user, Smscallback $smscallback)
    {
        $projects =  User::find($user->id)->projects()->get();
        if ( $projects->where('id', $smscallback->project_id )->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the smscallback.
     *
     * @param  \App\User  $user
     * @param  \App\Smscallback  $smscallback
     * @return mixed
     */
    public function restore(User $user, Smscallback $smscallback)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the smscallback.
     *
     * @param  \App\User  $user
     * @param  \App\Smscallback  $smscallback
     * @return mixed
     */
    public function forceDelete(User $user, Smscallback $smscallback)
    {
        //
    }
}
