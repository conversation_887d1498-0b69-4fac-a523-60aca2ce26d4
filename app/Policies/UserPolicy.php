<?php

namespace App\Policies;

use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any issues.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user, $id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive', '=', 1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects->merge($collabo_projects);
        if ($projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\User  $model
     * @return bool
     */
    public function view(User $user, User $model)
    {
        //

    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return bool
     */
    public function create(User $user, $id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive', '=', 1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects->merge($collabo_projects);
        if ($projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\User  $model
     * @return bool
     */
    public function update(User $user, User $model)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        } elseif ($user->id == $model->id) {
            return true;
        } else {
            return false;
        } //you are the same person
        //or you are admin
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\User  $model
     * @return bool
     */
    public function delete(User $user, User $model)
    {
        return false;
    }
}
