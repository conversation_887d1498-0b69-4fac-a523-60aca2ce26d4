<?php

namespace App\Policies;

use App\User;
use App\ContactList;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class ContactListPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any issues.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user,$id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
            return true;
        }
        return false;
    
    }
    /**
     * Determine whether the user can view the contactList.
     *
     * @param  \App\User  $user
     * @param  \App\ContactList  $contactList
     * @return bool
     */
    public function view(User $user, ContactList $contactList)
    {
        //
        return false;
    }

    /**
     * Determine whether the user can create contactLists.
     *
     * @param  \App\User  $user
     * @return bool
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the contactList.
     *
     * @param  \App\User  $user
     * @param  \App\ContactList  $contactList
     * @return bool
     */
    public function update(User $user, ContactList $contactList)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $contactList->project_id )->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can delete the contactList.
     *
     * @param  \App\User  $user
     * @param  \App\ContactList  $contactList
     * @return bool
     */
    public function delete(User $user, ContactList $contactList)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $contactList->project_id )->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;
    }
}
