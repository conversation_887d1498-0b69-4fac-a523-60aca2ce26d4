<?php

namespace App\Policies;

use App\Subresource;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubresourcePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any subresources.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the subresource.
     *
     * @param  \App\User  $user
     * @param  \App\Subresource  $subresource
     * @return mixed
     */
    public function view(User $user, Subresource $subresource)
    {
        //
    }

    /**
     * Determine whether the user can create subresources.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;    
    }

    /**
     * Determine whether the user can update the subresource.
     *
     * @param  \App\User  $user
     * @param  \App\Subresource  $subresource
     * @return mixed
     */
    public function update(User $user, Subresource $subresource)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $subresource->project_id )->count()  ===  1) { // do you own the project that owns the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the subresource.
     *
     * @param  \App\User  $user
     * @param  \App\Subresource  $subresource
     * @return mixed
     */
    public function delete(User $user, Subresource $subresource)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $collabo_projects->merge( $own_projects );
        //if both are empty
        if ( $projects->where('id', $subresource->project_id)->count()  ===  1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the subresource.
     *
     * @param  \App\User  $user
     * @param  \App\Subresource  $subresource
     * @return mixed
     */
    public function restore(User $user, Subresource $subresource)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the subresource.
     *
     * @param  \App\User  $user
     * @param  \App\Subresource  $subresource
     * @return mixed
     */
    public function forceDelete(User $user, Subresource $subresource)
    {
        //
    }
}
