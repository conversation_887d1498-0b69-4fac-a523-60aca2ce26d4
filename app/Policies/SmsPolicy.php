<?php

namespace App\Policies;

use App\User;
use App\Sms;
use App\Project;
use App\Collaboration;
use Illuminate\Auth\Access\HandlesAuthorization;

class SmsPolicy
{
    use HandlesAuthorization;

        /**
     * Determine whether the user can view any smscallbacks.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user, $id)
    {
        //
                //
                $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
                $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
                $own_projects = User::find($user->id)->projects()->get();
                $projects = $own_projects ->merge( $collabo_projects );
                if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
                    return true;
                }
                return false;
    }
    /**
     * Determine whether the user can view the sms.
     *
     * @param  \App\User  $user
     * @param  \App\Sms  $sms
     * @return bool
     */
    public function view(User $user, Sms $sms)
    {
        //
return false;
    }

    /**
     * Determine whether the user can create sms.
     *
     * @param  \App\User  $user
     * @return bool
     */
    public function create(User $user,$id)
    {
        //confirm project details such as trial or credits
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;

        //return $project->isClerk === 1 || $user->isOfficer === 1 || $user->isManager === 1 || $user->isAdmin === 1;
    }

    /**
     * Determine whether the user can update the sms.
     *
     * @param  \App\User  $user
     * @param  \App\Sms  $sms
     * @return bool
     */
    public function update(User $user, Sms $sms)
    {
        //sms are readonly. not modifiabl

        return false;
    }

    /**
     * Determine whether the user can delete the sms.
     *
     * @param  \App\User  $user
     * @param  \App\Sms  $sms
     * @return bool
     */
    public function delete(User $user, Sms $sms)
    {
        //
        return false;

    }
}
