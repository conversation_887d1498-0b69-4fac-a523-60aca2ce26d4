<?php

namespace App\Policies;

use App\User;
use App\Help;
use Illuminate\Auth\Access\HandlesAuthorization;

class HelpPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the help.
     *
     * @param  \App\User  $user
     * @param  \App\Help  $help
     * @return bool
     */
    public function view(User $user, Help $help)
    {
        return true;// $user->organization_id === $help->organization_id;

    }

    /**
     * Determine whether the user can create helps.
     *
     * @param  \App\User  $user
     * @return bool
     */
    public function create(?User $user)
    {
        return true;//$user->isClerk === 1 || $user->isOfficer === 1 || $user->isManager === 1 || $user->isAdmin === 1;
    }

    /**
     * Determine whether the user can update the help.
     *
     * @param  \App\User  $user
     * @param  \App\Help  $help
     * @return bool
     */
    public function update(User $user, Help $help)
    {
        //
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the help.
     *
     * @param  \App\User  $user
     * @param  \App\Help  $help
     * @return bool
     */
    public function delete(User $user, Help $help)
    {
        //
        return  false; //$user->isAdmin === 1;

    }
}
