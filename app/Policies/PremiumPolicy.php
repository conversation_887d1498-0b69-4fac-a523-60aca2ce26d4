<?php

namespace App\Policies;

use App\Premium;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class PremiumPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any premia.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user,$id)
    {
        //
                //
                $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
                $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
                $own_projects = User::find($user->id)->projects()->get();
                $projects = $own_projects ->merge( $collabo_projects );
                if ( $projects->where('id', $id)->count()  === 1) { //do you own th project viewing the resources
                    return true;
                }
                return false;
    }

    /**
     * Determine whether the user can view the premium.
     *
     * @param  \App\User  $user
     * @param  \App\Premium  $premium
     * @return mixed
     */
    public function view(User $user, Premium $premium)
    {
        //
    }

    /**
     * Determine whether the user can create premia.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;

    }

    /**
     * Determine whether the user can update the premium.
     *
     * @param  \App\User  $user
     * @param  \App\Premium  $premium
     * @return mixed
     */
    public function update(User $user, Premium $premium)
    {
        //
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the premium.
     *
     * @param  \App\User  $user
     * @param  \App\Premium  $premium
     * @return mixed
     */
    public function delete(User $user, Premium $premium)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the premium.
     *
     * @param  \App\User  $user
     * @param  \App\Premium  $premium
     * @return mixed
     */
    public function restore(User $user, Premium $premium)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the premium.
     *
     * @param  \App\User  $user
     * @param  \App\Premium  $premium
     * @return mixed
     */
    public function forceDelete(User $user, Premium $premium)
    {
        //
    }
}
