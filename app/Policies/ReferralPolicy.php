<?php

namespace App\Policies;

use App\Referral;
use App\User;
use App\Collaboration;
use App\Project;
use Illuminate\Auth\Access\HandlesAuthorization;

class ReferralPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any referrals.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        //
                //
                return $user->isGlobalAdmin == 1 ? true :false;
            }

    /**
     * Determine whether the user can view the referral.
     *
     * @param  \App\User  $user
     * @param  \App\Referral  $referral
     * @return mixed
     */
    public function view(User $user, Referral $referral)
    {
        //
    }

    /**
     * Determine whether the user can create referrals.
     *
     * @param  \App\User  $user
     * @return mixed
     */
    public function create(User $user,$id)
    {
        //
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the referral.
     *
     * @param  \App\User  $user
     * @param  \App\Referral  $referral
     * @return mixed
     */
    public function update(User $user, Referral $referral)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can delete the referral.
     *
     * @param  \App\User  $user
     * @param  \App\Referral  $referral
     * @return mixed
     */
    public function delete(User $user, Referral $referral)
    {
        if ($user->isGlobalAdmin === 1) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can restore the referral.
     *
     * @param  \App\User  $user
     * @param  \App\Referral  $referral
     * @return mixed
     */
    public function restore(User $user, Referral $referral)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the referral.
     *
     * @param  \App\User  $user
     * @param  \App\Referral  $referral
     * @return mixed
     */
    public function forceDelete(User $user, Referral $referral)
    {
        //
    }
}
