<?php

namespace App\Policies;

use App\Models\Topup;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use App\Collaboration;
use App\Project;
class TopupPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @param  \App\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Topup $topup)
    {
        //
    }

    /**
     * Determine whether the user can create models.
     *
     * @param  \App\User  $user
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user, $id)
    {
        $collabo_projects_ids = Collaboration::where('user_id', $user->id)->where('isActive','=',1)->where('role','admin')->pluck('project_id')->toArray();
        $collabo_projects = Project::whereIn('id',  $collabo_projects_ids)->get();
        $own_projects = User::find($user->id)->projects()->get();
        $projects = $own_projects ->merge( $collabo_projects );
        if ( $projects->where('id', $id)->count()  === 1) { //do you own th project creating the resource
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Topup $topup)
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Topup $topup)
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Topup $topup)
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @param  \App\User  $user
     * @param  \App\Models\Topup  $topup
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Topup $topup)
    {
        //
    }
}
