wm<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Whatsappnumber extends Model
{
    //
    protected $guarded = ['id'];

    public function waccount(){
        return $this->belongsTo('App\Waccount');
    }
    public function wmessages(){
        return $this->hasMany('App\Wmessage');
    }

    public function wtemplates(){
        return $this->hasMany('App\Wtemplate');
    }
    public function wwebhooks(){
        return $this->hasMany('App\Wwebhook');
    }
    public function wprofilepic(){
        return $this->hasMany('App\Wprofilepic');
    }
}
