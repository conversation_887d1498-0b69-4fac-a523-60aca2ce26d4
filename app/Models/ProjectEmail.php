<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectEmail extends Model
{
    use HasFactory;
    protected $fillable = [
        'from',
        'sender',
        'to',
        'recipient',
        'subject',
        'stripped_text',
        'stripped_html',
        'body_plain',
        'body_html',
        'message_id',
        'in_reply_to',
        'date',
        'references',
        'content_type',
        'attachment_count',
        'stripped_signature',
        'signature',
        'user_agent',
        'code',
        'project_id',

    ];
/*
    public function attachments()
    {
        return $this->hasMany(EmailAttachment::class);
    }

    public function saveAttachments($data)
    {
        if (!isset($data['attachment-1']) || !isset($data['attachment-2'])) {
            return;
        }

        $attachments = [$data['attachment-1'], $data['attachment-2']];
        foreach ($attachments as $attachment) {
            $fileName = $attachment->getClientOriginalName();
            $filePath = Storage::disk('local')->put('emails/' . $this->id, $attachment);
            $this->attachments()->create([
                'email_id' => $this->id,
                'filename' => $fileName,
                'path' => $filePath,
            ]);
        }
    }
*/
    public static function store($data)
    {
        $email = new ProjectEmail();
        $email->fill($data);
        try{
            //$email->project_id = $data['project_id'];
            $email->save();
           \Log::info('Email model stored successfully!');
        } catch (\Exception $e) {
            \Log::error('Error storing email model: ' . $e->getMessage());
        }
       // $email->saveAttachments($data);
        return $email;
    }
    

}
