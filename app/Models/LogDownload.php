<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LogDownload extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'project_id',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'downloaded_by',
        'downloaded_at',
        'download_status',
        'error_message'
    ];
}
