<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsMonthlyCopy extends Model
{
    use HasFactory;
    protected $table = 'sms_monthly_copy';
 protected $guarded = ['id'];
    public function project(){
        return $this->belongsTo('App\Project');
    }
    public function campaign(){
        return $this->belongsTo('App\Campaign');
    }
}
