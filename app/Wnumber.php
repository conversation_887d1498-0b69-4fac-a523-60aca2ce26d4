<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Wnumber extends Model
{
    protected $guarded= ['id'];

    public function wbusiness(){
        return $this->hasOne('App\Wbusiness');
    }
    public function wmessages(){
        return $this->hasmany('App\Wmessage');
    }

    public function wtemplates(){
        return $this->hasmany('App\Wtemplate');
    }
    public function wwebhook(){
        return $this->hasOne('App\Wwebhook');
    }
    public function wprofilepic(){
        return $this->hasOne('App\Wprofilepic');
    }
}
