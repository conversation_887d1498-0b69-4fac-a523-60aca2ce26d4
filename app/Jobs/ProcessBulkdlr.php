<?php
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use GuzzleHttp\Client;
use App\Project;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Throwable;

class ProcessBulkdlr implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $requestId;
    protected $traceID;
    protected  $names;
    protected  $values;
    protected  $array;
    protected  $Msisdn;
    protected  $correlatorId;
    protected  $Description;
    protected  $deliveryStatus;
    protected  $requestTimeStamp;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($requestId, $traceID, $names, $values, $array,$Msisdn,$correlatorId,$Description,$deliveryStatus, $requestTimeStamp)
    {
        $this->requestId = $requestId;
        $this->traceID = $traceID;
        $this->names = $names;
        $this->values = $values;
        $this->array = $array;
        $this->Msisdn = $Msisdn;
        $this->correlatorId = $correlatorId;
        $this->Description = $Description;
        $this->deliveryStatus = $deliveryStatus;
        $this->requestTimeStamp = $requestTimeStamp;

    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $deliveryStatus  = $this->deliveryStatus;
        if ($deliveryStatus == 0) {
            $codes = 'success';
        } elseif ($deliveryStatus == '1001') {
            $codes = 'network_failure';
        } elseif ( $deliveryStatus == 1 ||  $deliveryStatus == 5 || $deliveryStatus == 11 || $deliveryStatus == 21 ||
            $deliveryStatus == 31 || $deliveryStatus == 32 ||  $deliveryStatus == 34 || $deliveryStatus == 99 || $deliveryStatus == 13) {
            $codes = 'delivery_impossible';
        } elseif ($deliveryStatus == 6 ||  $deliveryStatus == 27) {
            $codes = 'absent_subscriber';
        } else {
            $codes = 'unknown_error'; //default error  code 21, incompatible terminal and SC facility not supported
        }
        //original bulk sms
        //$sms =  DB::table('sms')->where(['message_id' => $this->correlatorId, 'to' => $this->Msisdn])->exists() ?  
        //DB::table('sms')->where(['message_id' =>$this->correlatorId, 'to' => $this->Msisdn])->first() : null;
        //$sms !== null ? DB::table('sms')->where('message_id' =>$this->correlatorId)->update(['status' => $codes, 'description' => $this->Description, 'status_code' => $deliveryStatus, 'desc' => $this->traceID, 'updated_at' => $this->requestTimeStamp]) : '';


        $sms =  Sms::where(['message_id' => $this->correlatorId, 'to' => $this->Msisdn])->exists() ?  Sms::where(['message_id' =>$this->correlatorId, 'to' => $this->Msisdn])->first() : null;
        $sms !== null ? $sms->update(['status' => $codes, 'description' => $this->Description, 'status_code' => $deliveryStatus, 'desc' => $this->traceID, 'updated_at' => $this->requestTimeStamp]) : '';
        $project = $sms !== null ? Project::find($sms->project_id) : null;
        $callback = $project !== null && $project->smscallbacks()->count() > 0 ? $project->smscallbacks()->first()->deliveryCallbackUrl : null;
        $callback_array = explode(',', $callback); //https://smpp.sozuri.net/smpp_dlr/post

        foreach ($callback_array as $webhook) {
            if ($webhook != null && $sms !== null) {
                try {
                    $base_uri = $webhook; 
                    $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
                    $response = $client->request('POST', '', [
                        'headers' => [
                            'Content-Type'     => 'application/json',
                            'Accept'     => 'application/json',
                        ],
                        'json' => ['project' => $project->name, 'messageId' =>  $sms->message_id, 'channel' => 'sms', 'status' =>  $codes, 'network' =>  'safaricom', 'type' => 'bulkDelivery', 'timestamp' => time()] //add auth key from smscallbacks etc
                    ]);
                    $code = $response->getStatusCode();
                    $reason = $response->getReasonPhrase();
                    $body = $response->getBody();
                    $stringBody = (string) $body; 
                    $sms->link_notify_response_from_customer_body = $stringBody;
                    $sms->save();
                    Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
                    Log::info('stringbody from customer bulk dlr ' . $stringBody);
                } catch (Throwable $e) {
                    Log::error($e->getMessage());
                    Log::info("Could not send dlr callback for project: ". $project->name);
                }
            }
        }
        Log::info('safaricom dlr processed');
}
}