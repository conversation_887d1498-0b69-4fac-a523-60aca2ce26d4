<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\Sms;
use GuzzleHttp\Client;
use GuzzleHttp\Promise\Utils;
use App\Message;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;

class ProcessMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $sdp_smses;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sdp_smses)
    {
        $this->sdp_smses = $sdp_smses;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        ini_set('memory_limit', '10000M');
        // ini_set('max_input_time', '1800');
        ini_set('max_execution_time', 360); //default=30.You can not change this setting with ini_set() when running in safe mode
        //set_time_limit(800);//same as above
        ini_set('default_socket_timeout', '-1');
    
        $time_start = microtime(true);
        $tokenfile = fopen(config('app.token_file') . "/token.txt", "r");
        $token = fgets($tokenfile);
        Log::info('token: ' . $token);
        fclose($tokenfile);
        Log::info('token obtained in:' . (string)(microtime(true) - $time_start));
        $timestamp = time();
        $base_uri =  config('app.sdp_base_uri');
        $SourceAddress = config('app.sdp_source_address');
        $client = new Client(['base_uri' =>  $base_uri]);
        try{
        $response = $client->request('POST', 'public/CMS/bulksms', [
            //$promise = $client->requestAsync('POST', 'public/CMS/bulksms', [
            'headers' => [
                'Content-Type' => 'application/json', 'Accept' => 'application/json', 
                'X-Authorization' => 'Bearer ' . $token, 'SourceAddress' =>  $SourceAddress,'Connection' => 'keep-alive'
            ],
            'json' => [
                "timeStamp" => $timestamp,
                "dataSet" => $this->sdp_smses
            ],
            [
                'timeout' => 480, // Response timeout .....240
                'connect_timeout' => 480, // Connection timeout ...240
            ]
        ]);


        Log::info('request completed in:' . (string)(microtime(true) - $time_start));
        //$code = $response->getStatusCode(); // 200
        //$reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body;
        Log::info('Safaricom stringbody ' . $stringBody);
    } catch(Exception $e) {
        Log::info('Error:' . $e->getMessage());
    }
    }
}
