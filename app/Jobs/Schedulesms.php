<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\Sms;
use GuzzleHttp\Client;
use GuzzleHttp\Promise\Utils;
use App\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Exception;

class Schedulesms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_db_sms;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_db_sms)
    {
        $this->raw_db_sms = $raw_db_sms;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        ini_set('memory_limit', '32000M');
        // Increase execution time
        ini_set('max_execution_time', 3600);
        // Set socket timeout to indefinite
        ini_set('default_socket_timeout', '-1');
        $time_start = microtime(true);
        try {
            $this->insertMessages($this->raw_db_sms);
            Log::info(count($this->raw_db_sms) . ' messages scheduled');
            Log::info('2k scheduled requests saved in:' . (string) (microtime(true) - $time_start));

        } catch (Exception $e) {
            Log::info($e->getMessage());
            Log::error($e->getTraceAsString());
            Log::info('failed scheduling msgs');
        }

    }

    public function insertMessages(array $messages)
    {
        try {
            DB::table('scheduledsms')->insert($messages);
            Log::info('Inserted batch of ' . count($messages) . ' messages.');
            return true;
            /* DB::transaction(function() use ($messages) {
                     DB::table('scheduledsms')->insert($messages);
                     Log::info('Inserted batch of ' . count($messages) . ' messages.');
                 
             });
             return true;*/
        } catch (Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
    }
}
