<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use App\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class ProcessSms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_sms;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_sms)
    {
        //
        $this->raw_sms = $raw_sms;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Sms $sms)
    {
        Log::info($this->raw_sms);

        $base_uri = config('app.at_base_uri'); //'http://164.132.96.155'
        Log::info($base_uri);
        $raw_sms = $this->raw_sms;
        $client = new \GuzzleHttp\Client(['base_uri' =>  $base_uri]);
        $sender =  config('app.atFrom'); //$AT_FROM; //env('AT_FROM');
        $apiKey =  config('app.atKey'); //$AT_API_KEY; //env('AT_API_KEY');
        $username =   config('app.atUsername'); //$AT_USERNAME; //env('AT_USERNAME');
        $response = $client->request('POST', '/version1/messaging', [
            'headers' => [
                'Content-Type'     => 'application/x-www-form-urlencoded',
                'Accept'     => 'application/json',
                'apiKey'     => $apiKey,
            ],
            //'auth' => env('AT_USERNAME'),
            'form_params' => [
                'username' => $username,
                'from'     => $sender,  //string
                'to'     =>  $this->raw_sms['number'], //implode(',', $recipients), //string comma separated list of recipients
                'message'     =>   $this->raw_sms['message'], //string
                'bulkSMSMode'     => 1,  //integer
                //'enqueue'     => 1,  //integer
                //'keyword'     => '',  //string. keyword to be used for a premium service.
                //'linkId'     => '', //string. used for premium services to send OnDemand messages. We forward the linkId to your application when the user sends a message to your service
                //'retryDurationInHours'     => '', //string. hours your subscription message should be retried in case it’s not delivered to the subscriber.
            ]
        ]);

        $sender = config('app.atFrom'); //$AT_FROM; //env('AT_FROM');

        $body = $response->getBody();
        Log::info('body' . $body);

        $stringBody = (string) $body;
        //Log::info('stringbody' . $stringBody);
        $data = json_decode($stringBody, true);
        //$data = json_decode(json_encode($stringBody, true), true);

        Log::info($data);
        $sms = new Sms();
        $sms->cost = number_format((float) ($data['SMSMessageData']['Recipients'][0]['cost']), 2);
        $sms->messageId = $data['SMSMessageData']['Recipients'][0]['messageId'];
        $sms->messagePart =  array_key_exists('messageParts',  $data['SMSMessageData']['Recipients'][0]) ?  $data['SMSMessageData']['Recipients'][0]['messageParts'] : 0;
        $sms->number = $data['SMSMessageData']['Recipients'][0]['number'];
        $sms->status = $data['SMSMessageData']['Recipients'][0]['status'];
        $sms->statusCode = $data['SMSMessageData']['Recipients'][0]['statusCode'];
        $sms->project_id =   $this->raw_sms['project_id'];
        $sms->message =    $this->raw_sms['message'];
        $sms->from =   $this->raw_sms['from'];
        $sms->detail = $data['SMSMessageData']['Message'];
        $sms->created_at =    $this->raw_sms['created_at'];
        $sms->sent_at = Carbon::now();
        $sms->campaign_id = array_key_exists('campaignId',   $this->raw_sms) ?    $this->raw_sms['campaignId'] : null;
        $sms->wzId =   $this->raw_sms['tzId'];
        $sms->save();

        $message = new Message();
        $message->channel = 'sms';
        $message->project_id = $sms->project_id;
        $message->messageId = $sms->messageId;
        $message->messagePart =  $sms->messagePart;
        // $sms->tzmessagePart = $raw_sms['messagePart'];
        $message->status =  $sms->status;
        $message->statusCode =  $sms->statusCode;
        $message->message =   $sms->message;
        $message->from = $sms->from;
        $message->to =  $sms->number;
        $message->detail =  $sms->detail;
        $message->created_at =   $sms->created_at;
        $message->sent_at =  $sms->sent_at;
        $message->direction = "outbound-api";
        $message->campaign_id =  $sms->campaign_id;
        $message->tzId =  $sms->wzId;
        $message->save();
    }
}
