<?php

namespace App\Jobs;

use App\Sms;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\Atsendsms;
use Symfony\Component\HttpFoundation\Response;
use App\Project;
use Illuminate\Support\Facades\DB;

class ProcessSchedulesms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Atsendsms;
    protected $sms;
    protected $recipients;
    protected $project_id;
    protected $campaign_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sms, $recipients, $project_id, $campaign_id = null)
    {
        //anything passed into the constructor will be pased on to the hand;e ,ethioid
        $this->sms = $sms;
        $this->recipients = $recipients;
        $this->project_id = $project_id; 
        $this->campaign_id = $campaign_id;
    }
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 5;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Response $response) //you could load the sending trait here now!!!!!
    {
        //at this time, implement the trait
        //and queue
       // $response = $this->sendAtSms($this->sms,  $this->recipients);

        
        $response = $this->sendSingleSms($this->sms,  $this->recipients, $this->project_id);

    }
}
