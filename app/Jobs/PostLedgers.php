<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class PostLedgers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $account;
    protected $project;
    protected $journal;
    protected $entry_type;
    protected $amount;
    protected $tax_percent;
    protected $tax_type;
    protected $quantity;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $account, string $project, string $journal, string $entry_type, float $amount, float $tax_percent, string $tax_type)
    {
        $this->account =  $account;
        $this->project =  $project;
        $this->journal =  $journal;
        $this->entry_type =  $entry_type;
        $this->tax_percent =  $tax_percent;
        $this->tax_type =  $tax_type;
        $this->amount = $amount;
        $this->quantity = 1;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        if ($this->tax_type == "exclusive") {
            $min_subtotal = $this->amount;
            $subtotal = $min_subtotal  * $this->quantity;
            $min_tax_value  = $min_subtotal * ($this->tax_percent / 100);
            $tax_value  = $min_tax_value  * $this->quantity;
            $total =  ($min_subtotal + $min_tax_value)  * $this->quantity;;
        } elseif ($this->tax_type == "inclusive") {
            $min_subtotal =  ($this->amount / (($this->tax_percent / 100) + 1));
            $subtotal =  $min_subtotal * $this->quantity;;
            $min_tax_value   = $this->amount -   $min_subtotal;
            $tax_value  =  $min_tax_value  *  $this->quantity;;
            $total =  ($min_subtotal + $min_tax_value) * $this->quantity;
        } else {
            $min_subtotal =  $this->amount;
            $subtotal = ($this->amount) *  $this->quantity;
            $min_tax_value   = 0;
            $tax_value  = 0;
            $total =  $min_subtotal * $this->quantity;
        }

        $credit =  $this->entry_type == "credit" ? $total : 0;
        $debit =  $this->entry_type == "debit" ? $total : 0;

        if ($this->account == "units") {
            $account_type = "current asset";
        } elseif ($this->account == "payable") {
            $account_type = "current liability";
        } elseif ($this->account == "advance payment") {
            $account_type = "current liability";
        } elseif ($this->account == "cash") {
            $account_type = "current asset";
        } elseif ($this->account == "sale") {
            $account_type = "revenue";
        } elseif ($this->account == "cost of goods") {
            $account_type = "expense";
        } elseif ($this->account == "receivable") {
            $account_type = "current asset";
        } else{
            $account_type = "unknown";
        }
        //journal entry
        DB::table('ledgers')->insert([
            "name" =>  "",
            "date" =>  Carbon::now(),
            "code" =>  uniqid(),
            "matching_number" =>  "",
            "journal" =>  $this->journal,
            "journalentry_id" =>  "xx",
            "project" =>  $this->project,
            "product" =>  "",
            "account" =>  $this->account,
            "account_type" =>  $account_type,
            "tag" =>  "",
            "entry_type" =>  $this->entry_type,
            "debit" =>  $debit,
            "credit" =>  $credit,
            "balance" =>  abs($debit - $credit),
            "quantity" =>  $this->quantity,
            "partner" =>  "",
            "financialperiod" =>  "",
            "assettype" =>  "",
            "isArchived" =>  null,
            "active" =>  true,
            "due_date" =>  Carbon::now(),
            "date_posted" =>  Carbon::now(),
            "details" =>  "",
            "created_by" =>  1,
            "updated_by" =>  1,
            "created_at" =>  Carbon::now()

        ]);

        Log::info('actually posted journal records to database');
    }
}
