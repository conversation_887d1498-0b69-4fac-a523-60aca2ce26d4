<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProcessAnalyticcallback implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $mobile;
    protected $message_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($mobile, $message_id)
    {
        $this->mobile = $mobile;
        $this->message_id = $message_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('starting ANALYTICAL      check');
        $sms = DB::table('sms')->where('message_id', 'like', $this->message_id . '%')->where('to', 'like', '%' . $this->mobile)->get();
        Log::info($sms);
        if ($sms->count() < 1) {
            Log::info('message not found');
            exit;
        }
        DB::table('sms')->where('message_id', 'like', $this->message_id . '%')->where('to', 'like', '%' . $this->mobile)->update([
                "clicked_at" => Carbon::now(),
                "click_count" =>  1,
                "click_ip" =>  $_SERVER['REMOTE_ADDR'],
                "clicked_from" => $_SERVER['HTTP_USER_AGENT']
            ]);
            Log::info('ANALYTICAL message updated');

    }
}
