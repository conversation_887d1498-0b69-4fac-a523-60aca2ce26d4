<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Project;

class ProcessTopup implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $top_up_data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $top_up_data)
    {
        $this->top_up_data = $top_up_data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('starting topup');
        Log::info($this->top_up_data['amount']);
        $top_up_id = DB::table('topups')->insertGetId($this->top_up_data);
        try {
            $base_uri = config('app.airtime_base_uri');
            $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
            $response = $client->request('GET', '/topup/flexi', [
                'query' => [
                    "encryptionKey" => config('app.airtime_encryption_key'),
                    "terminalNumber" => config('app.airtime_terminal_number'),
                    "transactionKey" => config('app.airtime_transaction_key'),
                    "productCode" => $this->top_up_data['product_code'],
                    "referalNumber" =>  $this->top_up_data['referral_number'],
                    "amount" => ($this->top_up_data['amount'] * 100),
                    "requestUniqueId" => $this->top_up_data['request_unique_id'],
                    "fromANI" => config('app.airtime_from_ani'),
                    "email" => config('app.airtime_email'),
                    "systemServiceId" => $this->top_up_data['system_service_id'],
                ],
                [
                    'timeout' => 240, // Response timeout .....240
                    'connect_timeout' => 240, // Connection timeout ...240
                ]
            ]);

            Log::info('airtime http request completed');
            $rawBody = $response->getBody();
            Log::info($rawBody);
            $body = json_decode($rawBody, true);
            if ($body["ResponseCode"] == "000") {
                $status = 'success';
                $top_up_response_data = [
                    // 'session_id' => '', //fromcallback
                    'response_code' => $body["ResponseCode"],
                    'response_description' => $body["ResponseDescription"],
                    'confirmation_code' => $body["ConfirmationCode"],
                    'audit_no' =>  $body["AuditNo"],
                    'operator_request_id' => $body["OperatorRequestID"],
                    'provider_response' => $body["ProviderResponse"],
                    'authorize_key' => '',
                    'system_service_id' => '2', //topupproduct
                    'system_service_name' => 'Topup',
                    'product_id' => '',
                    'product_name' => '',
                    'product_description' => '',
                    'product_info' => $body["ProductInfo"],
                    'updated_at' => now(),
                    'status' => $status,
                ];
            } else {
                $status = 'failed';
                $top_up_response_data = [
                    // 'session_id' => '', //fromcallback
                    'response_code' => $body["ResponseCode"],
                    'response_description' => $body["ResponseDescription"],
                    'status' => $status,
                ];
            }
            DB::table('topups')->where('id', $top_up_id)->update($top_up_response_data);
            Log::info('airtime top up updated');
            $this->sendCallback($status, $top_up_response_data['response_description']);

        } catch (Exception $e) {
            $errors =  $e->getMessage();
            Log::info($errors);
        }
        exit;
    }


    public function sendCallback($status, $description)
    {
        $project = Project::find($this->top_up_data['project_id']);
        $callback = $project !== null && $project->smscallbacks()->count() > 0 ? $project->smscallbacks()->first()->name : null;
        $callback_array = explode(',', $callback);
        foreach ($callback_array as $webhook) {
            if ($webhook != null) {
                Log::info('sending airtime webhook:' . $webhook);
                $base_uri = $webhook;
                $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
                $response = $client->request('POST', '', [
                    'headers' => [
                        'Content-Type'     => 'application/json',
                        'Accept'     => 'application/json',
                    ],
                    'json' => ['number' => $this->top_up_data['referral_number'], 'amount' =>  "KES " . number_format($this->top_up_data['amount'],2), 'requestId' => $this->top_up_data['request_unique_id'], 'discount' => 'KES 0.00', 'status' =>  $status, 'type' => 'airtimeDelivery', 'description' => $description, 'timestamp' => time()]
                ]);
                $code = $response->getStatusCode();
                $reason = $response->getReasonPhrase(); 
                $body = $response->getBody();
                $stringBody = (string) $body;
                Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
                Log::info('stringbody from customer airtime dlr ' . $stringBody);
            }
        }
        Log::info('finished airtime webhook:');

    }
}
