<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use App\Subscriber;
use App\Message;
use Illuminate\Support\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ProcessSdp implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_subscribe_request;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_subscribe_request)
    {
        //
        $this->raw_subscribe_request = $raw_subscribe_request;
        //$token = $this->gettoken;

    }
    private function gettoken()
    {
        $base_uri = 'https://dtsvc.safaricom.com:8480/api/'; //config('app.at_base_uri'); //'http://**************'
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'auth/login', [
            'headers' => [
                'Content-Type'     => 'application/json',
                'Accept'     => 'application/json',
                'X-Requested-With'     => 'XMLHttpRequest',
            ],
            'json' => ['username' => 'Sozuri_apiuser', 'password' => '#EDC4rfv']
        ]);
        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        //echo $body;  // Implicitly cast the body to a string and echo it
        $stringBody = (string) $body; // Explicitly cast the body to a string
        Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('stringbody' . $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $token =  $data['token'];
        $msg =  $data['msg'];
        $refreshToken =  $data['refreshToken'];
        return $token;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Sms $sms)
    {
        Log::info($this->raw_subscribe_request);

        $base_uri = config('app.at_base_uri'); //'http://**************'
        Log::info($base_uri);
        $raw_subscribe_request = $this->raw_subscribe_request;

        $timestamp = time();
        $uniqueid = uniqid();
        $token = $this->gettoken();
        $base_uri = 'https://dtsvc.safaricom.com:8480/api/'; //config('app.at_base_uri'); //'http://**************'
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'public/SDP/activate', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Authorization' => 'Bearer ' . $token,
                //'X-Requested-With' => 'XMLHttpRequest'
                'SourceAddress' => '************'//
            ],
            'json' => [
                "requestId" => uniqid('SOZSUB'),
                "requestTimeStamp"=> date('Ymdhi'),
                "channel" => 'APIGW',
                "operation" => 'ACTIVATE',
                "requestParam" => [
                "data" => [
                    ["name" => "OfferCode", "value" => $this->raw_subscribe_request['OfferCode']], //001029900701  F/OR SUBSCRIPTION  001029900700 FOR ONDEMAND
                    ["name" => "Msisdn", "value" =>  $this->raw_subscribe_request['number'] ],
                    ["name" => "Language","value" =>  $this->raw_subscribe_request['Language'] ],
                    ["name" => "CpId", "value" => $this->raw_subscribe_request['299']],
                ]]
            ]
        ]);
        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body; // Explicitly cast the body to a string
        Log::info('subscribe --code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('stringbody for subscribe request' . $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);

        $subscriber = Subscriber::where(['msisdn' => $this->raw_subscribe_request['number'],
         'project_id' => $this->raw_subscribe_request['project_id'],
         'premium_id' => $this->raw_subscribe_request['premium_id']])->first();

         $subscriber->activate_body = $stringBody ;
         $subscriber->provider_status =  $data['responseParam']['status'];
         $subscriber->provider_statusCode = $data['responseParam']['statusCode'];
         $subscriber->provider_description =  $data['responseParam']['description'];
        $subscriber->save;

        //then what ?
    }
}
