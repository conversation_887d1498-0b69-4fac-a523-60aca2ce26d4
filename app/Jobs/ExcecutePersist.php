<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use PDO;

class ExcecutePersist implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $sql;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($sql)
    {
        $this->sql = $sql;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $time_start = microtime(true);
        $db_instance = DbConnector::getInstance();
        $conn = $db_instance->getConnection();
        $conn->query($this->sql);
        Log::info('persistence completed in::' . (string)(microtime(true) - $time_start));
        //$code = $response->getStatusCode(); // 200
        //$reason = $response->getReasonPhrase(); // OK
        //$body = $response->getBody();
        //$stringBody = (string) $body;
    }
}

class DbConnector
{
    protected static $instance = null;
    protected $servername = "localhost";
    protected $username = "root";
    protected $password = "Cl@$$1c105";
    protected $dbname = "talkzurilocal";
    // set the PDO error mode to exception
    function __construct()
    {
        $this->conn = new PDO("mysql:host=$this->servername;dbname=$this->dbname", $this->username, $this->password);
        $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new DbConnector();
        }
        return self::$instance;
    }
    public function getConnection()
    {
        return $this->conn;
    }
}
