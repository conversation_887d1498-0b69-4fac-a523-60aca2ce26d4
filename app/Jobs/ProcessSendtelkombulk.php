<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use GuzzleHttp\Client;
use App\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class ProcessSendtelkombulk implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_smses;
    protected  $package_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_smses,  $package_id)
    {
        $this->raw_smses = $raw_smses;
        $this->package_id = $package_id;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $raw_sms = $this->raw_smses;
        //$timestamp = time();
        $base_uri = config('app.telkom_sdp_base_uri'); //'http:// 62.24.104.73:9080/v1/;
        $telkom_sdp_shortcode = config('app.telkom_sdp_shortcode');
        $telkom_sdp_service_id = config('app.telkom_sdp_service_id');
        //$sdp_response_uri = config('app.sdp_response_uri');
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'bulkrequest', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                //'X-Authorization' => 'Bearer ' . $this->token,
                //'X-Requested-With' => 'XMLHttpRequest'
                //'SourceAddress' =>  $SourceAddress
            ],
            'json' => [
                    //"timeStamp" => $timestamp,
                    //"userName" => $sdp_portal_username,  //cpPassword = MD5(cpId + Password + timestamp
                    //"packageId" => $this->package_id, //multiple
                    "msisdn" =>  [$raw_sms['to']],
                    "shortCode" =>   $telkom_sdp_shortcode,
                    "message" =>   $raw_sms['message'],
                    "senderAddress" => $raw_sms['from'], //"SDPTest",//from
                   // "serviceId" =>  $raw_sms['message_id'], //$unique_id,
                    "serviceId" => $telkom_sdp_service_id, //$unique_id,
            ]
        ]);

        $code = $response->getStatusCode(); // 200 
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body; // Explicitly cast the body to a string
       // Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('Telkom stringbody'. $raw_sms['messageId'] . $stringBody);
        $data = json_decode($stringBody, true);

        if ( array_key_exists ( 'Error', $data ) ) { 
            $time = Carbon::parse(  $data["timeStamp"] );
            $code =  $data["code"] ;
            $status =  $data["Error"] ;
           // $detail = $data["SMSDeliveryStatusReference"]["referenceCode"] ;     
            $detail =$data["Error"] ;   
            $description = $data["Error"] ;  

        } else  {
            $time = Carbon::parse( $data["SMSDeliveryStatusReference"]["dateTime"] );
            $code = $data["SMSDeliveryStatusReference"]["referenceCode"] ;     
            $status = 'SUCCESS' ;
            $detail = $data["SMSDeliveryStatusReference"]["referenceCode"] ;  
            $description = 'Delivered to terminal' ;
        }

        $sms = new Sms();
        $sms->cost = number_format((float) $raw_sms['messagePart'], 2);
        $sms->price =  $raw_sms['credits'];
        $sms->message_id = $raw_sms['messageId'];
        $sms->message_part =  $raw_sms['messagePart'];
        $sms->to = $raw_sms['to'];
        $sms->detail = $detail;
        $sms->status = $status;
        $sms->description = $description;
        $sms->status_code = $code; //$data['statusCode'];
        $sms->channel = 'sms';
        $sms->package_id = $this->package_id;
        $sms->project_id =   $raw_sms['project_id'];
        $sms->message =    $raw_sms['message'];
        $sms->from =   $raw_sms['from'];
        $sms->direction = "outbound";
        $sms->created_at =    $raw_sms['created_at'];
        $sms->sent_at =  $time;
        $sms->campaign_id = array_key_exists('campaign_id',   $raw_sms) ?    $raw_sms['campaign_id'] : null;
        $sms->tz_id =   $raw_sms['messageId'];
        $sms->bulk_id =   $raw_sms['bulkId'];
        $sms->telco =   $raw_sms['telco'];
        if(!array_key_exists('retry', $raw_sms)/* && $raw_sms['retry'] !== 1 */) {
        $sms->save();
        }
    }
}
