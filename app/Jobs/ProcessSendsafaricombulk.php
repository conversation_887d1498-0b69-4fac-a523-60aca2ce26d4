<?php
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use GuzzleHttp\Client;
use App\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;

class ProcessSendsafaricombulk implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_smses;
    protected $token;
    protected  $package_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_smses, $token, $package_id)
    {
        $this->raw_smses = $raw_smses;
        $this->token = $token;
        $this->package_id = $package_id;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $raw_sms = $this->raw_smses;
        $timestamp = time();
        $base_uri = config('app.sdp_base_uri');
        $sdp_portal_username = config('app.sdp_portal_username');
        $SourceAddress = config('app.sdp_source_address');
        $sdp_response_uri = config('app.sdp_response_uri');
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'public/CMS/bulksms', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Authorization' => 'Bearer ' . $this->token,
                'SourceAddress' =>  $SourceAddress
            ],
            'json' => [
                "timeStamp" => $timestamp,
                "dataSet" => [[
                    "userName" => $sdp_portal_username,  
                    "channel" => "sms",
                    "packageId" => $this->package_id,
                    "oa" => $raw_sms['from'],
                    "msisdn" =>  $raw_sms['to'],
                    "message" =>   $raw_sms['message'],
                    "uniqueId" =>  $raw_sms['messageId'],
                    "actionResponseURL" =>  $sdp_response_uri
                ]]
            ]
        ]);

        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        $stringBody = (string) $body; // Explicitly cast the body to a string
        //Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('Safaricom stringbody' . $raw_sms['messageId'] . $stringBody);

        $data = json_decode($stringBody, true);
        //Log::info($data);
        $sms = new Sms();
        $sms->cost = number_format((float) $raw_sms['messagePart'], 2);
        $sms->price =  $raw_sms['credits'];
        // $sms->price =  $raw_sms['credits'];
        $sms->message_id = $raw_sms['messageId'];
        $sms->message_part =  $raw_sms['messagePart'];
        $sms->to = $raw_sms['to'];
        $sms->detail = $data['keyword'];
        $sms->status = $data['status'];
        $sms->status_code = $data['statusCode'];
        $sms->description = 'accepted';//
        $sms->channel = 'sms';
        $sms->package_id = $this->package_id;
        $sms->project_id =   $raw_sms['project_id'];
        $sms->message =    $raw_sms['message'];
        $sms->from =   $raw_sms['from'];
        $sms->direction = "outbound";
        $sms->created_at =    $raw_sms['created_at'];
        $sms->sent_at = Carbon::now();
        $sms->campaign_id = array_key_exists('campaign_id',   $raw_sms) ?    $raw_sms['campaign_id'] : null;
        $sms->tz_id =   $raw_sms['messageId'];
        $sms->bulk_id =   $raw_sms['bulkId'];
        $sms->telco =   $raw_sms['telco'];
        if(!array_key_exists('retry', $raw_sms)) {  ///* && $raw_sms['retry'] !== 1 */
        $sms->save();
        }
    }
}
