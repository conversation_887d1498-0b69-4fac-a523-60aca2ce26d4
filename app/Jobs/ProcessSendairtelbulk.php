<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use GuzzleHttp\Client;
use App\Message;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Exception;

class ProcessSendairtelbulk implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_smses;
    protected $package_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_smses, $package_id)
    {
        $this->raw_smses = $raw_smses;
        $this->package_id = $package_id;
    }
    public $timeout = 90;
    public $tries = 5;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $raw_sms = $this->raw_smses;
        //if message from is numeric, then it is a shortcode and use smpp route
        Log::info($raw_sms['from']);
        if(preg_match('/^[0-9]+$/', $raw_sms['from']))  {
            Log::info('smpp messsge');
            $this->sendToSmpp($raw_sms);
        } else {
            Log::info('http messsge');
            $this->sendToHttp($raw_sms);
        }

    }

    public function sendToHttp($raw_sms) {
        $startTime = microtime(true);
        $base_uri = config('app.airtel_sdp_base_uri');
        $airtel_sdp_username = ($raw_sms['type'] == 'transactional') ? config('app.airtel_sdp_usernameT') : config('app.airtel_sdp_username');
        log::info($raw_sms['type']);
        log::info('username: ' . $airtel_sdp_username);
        $airtel_sdp_password = config('app.airtel_sdp_password');
        $client = new Client(['base_uri' => $base_uri, 'verify' => false]);
        $from = $raw_sms['from'];
        $Error_number_Submission_ID = "1591";
        $tries = 0;
        $maxRetries = 5;

        while ($Error_number_Submission_ID == "1591" && $tries < $maxRetries) {
            try {
                $response = $client->request('GET', 'qs', [

                    'headers' => [],
                    'query' => [
                        'REQUESTTYPE' => 'SMSSubmitReq',
                        'USERNAME' => $airtel_sdp_username,
                        'PASSWORD' => $airtel_sdp_password,
                        'MOBILENO' => $raw_sms['to'],
                        'MESSAGE' => $raw_sms['message'],
                        'ORIGIN_ADDR' => $from,
                        'TYPE' => '0'
                    ],
                    [
                        'timeout' => 16,
                        'connect_timeout' => 16,
                    ]
                ]);

                $body = $response->getBody();
                $stringBody = (string) $body;
                Log::info('Airtel response after ' . $tries . ' attempts ' . $raw_sms['to'] . ' id: ' . $raw_sms['messageId'] . ': ' . $stringBody);
                //[2024-04-19 15:16:47] production.INFO: Airtel response after 0 attempts 254786732076 id: c01d1cc2c87d3e1fea8af80a42336656ea655a3f: +OK|8248757079407498456|Success|20240419-151647  
                $data = explode("|", $stringBody);
                $Text = $data[0];
                $Error_number_Submission_ID = $data[1];
                $description = strtolower($data[2]); // Success check the capitalization.
                $Timestamp = strtotime(preg_replace('/-/', '', $data[3]));
                try {
                    $messageId = $raw_sms['messageId'];
                    Updateairtelsms::dispatch($messageId, $description, "bulk")->onQueue('dlr')->delay(now()->addMinutes(3));
                } catch (Exception $e) {
                    Log::info(" AIRTEL MESSAGE QUEUE UPDATE ERROR " . $e->getMessage());
                }
                // If the submission was successful, exit the loop.
                if ($Error_number_Submission_ID != "1591") {
                    break;
                }
                Log::info($data);
            } catch (Exception $e) {
                Log::info($e->getMessage() . ". " . $tries . " attempt");
                $tries++;
                usleep(100000 * ($tries ** 2)); // Exponential backoff for retry delay
            }
        }
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        $executionTimeInSeconds = number_format($executionTime, 2);
        if ($tries >= $maxRetries) {
            // Max retries reached for this SMS message, log or handle accordingly
            Log::info("Max retries (" . $tries . ") reached after " . $executionTimeInSeconds . " seconds for SMS message: " . $raw_sms['message']);
           // $this->sendFallbackSms($raw_sms);

        }
    }

    public function sendToSmpp($raw_sms)
    {
        $startTime = microtime(true);
        $sozuri_smpp_url = config('app.sozuri_smpp_url');
        $sozuri_smpp_username = config('app.sozuri_smpp_username');
        $sozuri_smpp_password = config('app.sozuri_smpp_password');
        $sozuri_smpp_dlr_url = config('app.sozuri_smpp_dlr_url');
        Log::info($sozuri_smpp_url." " . $sozuri_smpp_username." " . $sozuri_smpp_password." " . $sozuri_smpp_dlr_url);
        $client = new Client(['base_uri' => $sozuri_smpp_url, 'verify' => false]);
        $tries = 0;
        $maxRetries = 3;

        while ($tries < $maxRetries) {
            try {
                $response = $client->request('GET', 'send', [
                    'headers' => [],
                    'query' => [
                        'username' => $sozuri_smpp_username,
                        'password' => $sozuri_smpp_password,
                        'content' => $raw_sms['message'],
                        'to' => $raw_sms['to'],
                        'from' => $raw_sms['from'],
                        'dlr' => 'yes',
                        'dlr-url' => $sozuri_smpp_dlr_url,
                        'dlr-method' => 'GET',
                        'dlr-level' => '2',
                        'priority' => '1'
                    ],
                    [
                        'timeout' => 40,
                        'connect_timeout' => 40,
                    ]
                ]);
                $body = (string) $response->getBody();
                Log::info($body);
                if (strpos($body, 'Success') !== false) {
                    preg_match('/Success "(.*?)"/', $body, $matches);
                    $description = "suceess"; // Extracted success message
                    //Success "5c6574a6-92f2-47ca-8e60-49557411e6d7"  
                    $providerId = null;
                    if (preg_match('/Success "(.*?)"/', $body, $matches)) {
                        $providerId = $matches[1];
                    }
                    Log::info("Success: $description");
                } elseif (strpos($body, 'Error') !== false) {
                    preg_match('/Error "(.*?)"/', $body, $matches);
                    $description = "error";
                    $providerId = null;
                    if (preg_match('/Error "(.*?)"/', $body, $matches)) {
                        $providerId = $matches[1];
                    }
                    Log::info("SMPP Error: $description");
                } else {
                    $description = "unknown-smp";
                }
                try {
                    $messageId = $raw_sms['messageId'];
                    Updateairtelsms::dispatch($messageId, $description, $providerId)->onQueue('dlr')->delay(now()->addMinutes(1));
                    Log::info("SMPP AIRTEL MESSAGE QUEUE UPDATED");
                } catch (Exception $e) {
                    Log::info("SMPP AIRTEL MESSAGE QUEUE UPDATE ERROR " . $e->getMessage());
                }
                // If the submission was successful, exit the loop.
                if (strpos($body, 'Success') !== false) {
                    break;
                }

            } catch (Exception $e) {
                Log::info('SMPP AIRTEL Error ' . $e->getMessage() . ". " . $tries . " attempt");
                $tries++;
                usleep(100000 * ($tries ** 2)); // Exponential backoff for retry delay
            }
        }
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        $executionTimeInSeconds = number_format($executionTime, 2);
        if ($tries >= $maxRetries) {
            Log::info("SMPP Max retries (" . $tries . ") reached after " . $executionTimeInSeconds . " seconds for SMS message: " . $raw_sms['message']);
        }
    }

    private function sendFallbackSms($raw_sms) {
        try {
            $fallbackClient = new Client();
    
            $response = $fallbackClient->post('https://automation.sozuri.net/api/v3/sms/send', [
                'headers' => [
                    'Authorization' => 'Bearer 1|kpvUAsijL1tT6oeynSK74b04riO3Sv3DLQBwpXKe6062540c',
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'json' => [
                    'recipient' => $raw_sms['to'],
                    'sender_id' => '254722117850',//$raw_sms['from'],
                    'type' => 'plain',
                    'message' => $raw_sms['message'],
                ],
                'timeout' => 16,
                'connect_timeout' => 16,
            ]);
    
            $body = (string) $response->getBody();
            $result = json_decode($body, true);
    
            Log::info('Fallback SMS response: ', $result);
    
        } catch (Exception $e) {
            Log::error('Fallback SMS failed: ' . $e->getMessage());
        }
    }

    public function failed(\Throwable $exception)
{
    Log::error('ProcessSendairtelbulk FINAL FAIL: ' . $exception->getMessage());
}

    
}
