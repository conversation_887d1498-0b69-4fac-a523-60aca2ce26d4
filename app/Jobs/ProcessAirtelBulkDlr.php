<?php
namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use GuzzleHttp\Client;
use App\Project;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Carbon;
use Throwable;

class ProcessAirtelBulkDlr implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $id;
    protected $status;
    protected $reply_from;
protected $description;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id, $status, $description, $err)
    {
        $this->id = $id;
        $this->err = $err;
        $this->status = $status;
        $this->description = $description;


    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        try {
            if (!Sms::where('request_id', $this->id)->exists()) {
                Log::info("airtel sms not found in db for dlr update. exiting...");
            }
            $sms = Sms::where('request_id', $this->id)->first();
            Sms::where('request_id', $this->id)
                ->update(['status' => $this->status, 'description' => $this->description, 'status_code' => $this->err, 'desc' => $this->description, 'updated_at' => Carbon::now()]);
            Log::info('AIRTEL dlr sms just updated id:' . $this->id . ' description:' . $this->description);
        } catch (Exception $e) {
            Log::info($e->getMessage());
        }

        $project = $sms !== null ? Project::find($sms->project_id) : null;
        $callback = $project !== null && $project->smscallbacks()->count() > 0 ? $project->smscallbacks()->first()->deliveryCallbackUrl : null;
        $callback_array = explode(',', $callback); //https://smpp.sozuri.net/smpp_dlr/post

        foreach ($callback_array as $webhook) {
            if ($webhook != null && $sms !== null) {
                try {
                    $base_uri = $webhook;
                    $client = new Client(['base_uri' => $base_uri, 'verify' => false]);
                    $response = $client->request('POST', '', [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Accept' => 'application/json',
                        ],
                        'json' => ['project' => $project->name, 'messageId' => $sms->message_id, 'channel' => 'sms', 'status' => $codes, 'network' => 'safaricom', 'type' => 'bulkDelivery', 'timestamp' => time()] //add auth key from smscallbacks etc
                    ]);
                    $code = $response->getStatusCode();
                    $reason = $response->getReasonPhrase();
                    $body = $response->getBody();
                    $stringBody = (string) $body;
                    $sms->link_notify_response_from_customer_body = $stringBody;
                    $sms->save();
                    Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
                    Log::info('stringbody from customer bulk dlr ' . $stringBody);
                } catch (Throwable $e) {
                    Log::error($e->getMessage());
                    Log::info("Could not send dlr callback for project: " . $project->name);
                }
            }
        }
        Log::info('safaricom dlr processed');
    }
}


