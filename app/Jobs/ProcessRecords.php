<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessRecords implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_db_smses;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_db_smses)
    {
        $this->raw_db_smses = $raw_db_smses;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $time_start = microtime(true);
        DB::table('sms')->insert($this->raw_db_smses);
        Log::info('1000 records inserted from bulk in:' . (string)(microtime(true) - $time_start));
    }
}
