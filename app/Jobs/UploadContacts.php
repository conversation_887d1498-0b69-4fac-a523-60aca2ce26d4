<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Contact;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UploadContacts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected  $contacts_results;
    protected  $contactlists;
    protected  $project_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($contacts_results, $contactlists, $project_id)
    {
        $this->contacts_results = $contacts_results;
        $this->contactlists = $contactlists; //integer
        $this->project_id = $project_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach ($this->contacts_results as $contacts_result) {
            if ($contacts_result['mobile'] == null) {
                continue;
            }
            $contact_id =  DB::table('contacts')->where(['project_id' => $this->project_id, 'mobile' => $contacts_result['mobile']])->exists() ?
                DB::table('contacts')->where(['project_id' => $this->project_id, 'mobile' => $contacts_result['mobile']])->first()->id :
                DB::table('contacts')->insertGetId($contacts_result);
            DB::table('contact_contact_list')->insertOrIgnore(
                ['contact_id' =>  $contact_id, 'contact_list_id' => $this->contactlists, 'project_id' => $this->project_id]
            );
            // $contact->contactLists()->syncWithoutDetaching($this->contactlists);
            Log::info('contact imported');
        }
    }
}
