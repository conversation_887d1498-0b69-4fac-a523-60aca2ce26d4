<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use App\Subscriber;
use App\Premiumsms;
use App\Message;
use Illuminate\Support\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ProcessSendpremium implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $raw_subscribe_request;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($raw_subscribe_request)
    {
        //
        $this->raw_subscribe_request = $raw_subscribe_request;
        $this->token = $this->gettoken();

    }
    private function gettoken()
    {
        $base_uri = 'https://dtsvc.safaricom.com:8480/api/'; //config('app.at_base_uri'); //'http://**************'
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'auth/login', [
            'headers' => [
                'Content-Type'     => 'application/json',
                'Accept'     => 'application/json',
                'X-Requested-With'     => 'XMLHttpRequest',
            ],
            'json' => ['username' => 'Sozuri_apiuser', 'password' => '#EDC4rfv']
        ]);
        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        //echo $body;  // Implicitly cast the body to a string and echo it
        $stringBody = (string) $body; // Explicitly cast the body to a string
        Log::info('code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('stringbody' . $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);
        $token =  $data['token'];
        $msg =  $data['msg'];
        $refreshToken =  $data['refreshToken'];
        return $token;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(Sms $sms)
    {
        Log::info($this->raw_subscribe_request);

        $base_uri = config('app.at_base_uri'); //'http://**************'
        Log::info($base_uri);
        $raw_subscribe_request = $this->raw_subscribe_request;

        $timestamp = time();
        $uniqueid = uniqid();
        $token = $this->token;

        $timestamp = time();
        $uniqueid = uniqid();
        $token = $this->gettoken();
        $base_uri = 'https://dtsvc.safaricom.com:8480/api/'; //config('app.at_base_uri'); //'http://**************'
        $client = new Client(['base_uri' =>  $base_uri, 'verify' => false]);
        $response = $client->request('POST', 'public/SDP/sendSMSRequest', [
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Authorization' => 'Bearer ' . $token,
                'SourceAddress' => '************'
            ],
            'json' => [
                "requestId" => uniqid('SOSENDPRE'),
                "channel" => 'APIGW',
                "operation" => 'SendSMS',
                "requestParam" => [
                    "data" => [
                        ["name" => "LinkId", "value" => $this->raw_sms['linkId']], //null fro subscription
                        ["name" => "Msisdn", "value" => $this->raw_sms['msisdn']],
                        ["name" => "Content", "value" =>  $this->raw_sms['message']],
                        ["name" => "OfferCode", "value" => $this->raw_sms['Offer_ode']], //001029900701  F/OR SUBSCRIPTION  001029900700 FOR ONDEMAND
                        ["name" => "CpId", "value" => $this->raw_sms['CpId']],
                    ]
                ]
            ]
        ]);
        $code = $response->getStatusCode(); // 200
        $reason = $response->getReasonPhrase(); // OK
        $body = $response->getBody();
        //echo $body;  // Implicitly cast the body to a string and echo it
        $stringBody = (string) $body; // Explicitly cast the body to a string
        Log::info('sendpremium --code:' . $code . ' reason:' . $reason . ' body:' . $body);
        Log::info('stringbody' . $stringBody);
        $data = json_decode($stringBody, true);
        Log::info($data);

        $premiumsms = new Premiumsms();
        $premiumsms->cost = $this->raw_sms['rate'];
        $premiumsms->message_id = $this->raw_sms['tzId'];
        $premiumsms->message_parts  = $this->raw_sms['message_parts'];
        $premiumsms->msisdn = $data['SMSMessageData']['Recipients'][0]['number'];
        $premiumsms->provider_status = $data['SMSMessageData']['Recipients'][0]['status'];
        $premiumsms->provider_statuscode = $data['SMSMessageData']['Recipients'][0]['statusCode'];
        $premiumsms->request_body = $data['SMSMessageData']['Recipients'][0]['request_body'];
        $premiumsms->response_body =  $stringBody;
        $premiumsms->project_id =   $this->raw_sms['project_id'];
        $premiumsms->premium_id =   $this->raw_sms['premium_id'];
        $premiumsms->message =    $this->raw_sms['message'];
        $premiumsms->offer_code =    $this->raw_sms['offer_code'];
        $premiumsms->link_id =    $this->raw_sms['linkId'];
        $premiumsms->language =    $this->raw_sms['language'];
        $premiumsms->cp_id =    $this->raw_sms['CpId'];
        $premiumsms->rate =    $this->raw_sms['rate'];
        $premiumsms->type =    $this->raw_sms['type'];
        $premiumsms->campaign_id =    $this->raw_sms['campaign_id'];
        $premiumsms->description =    $this->raw_sms['description'];
        $premiumsms->from =   $this->raw_sms['from'];
        $premiumsms->price =   $this->raw_sms['price'];
        $premiumsms->detail = $data['SMSMessageData']['message'];
        $premiumsms->created_at =    $this->raw_sms['created_at'];
        $premiumsms->send_at =$this->raw_sms['send_at'];
        $premiumsms->sent_at = Carbon::now();
        $premiumsms->tzId =   $this->raw_sms['tzId'];
        $premiumsms->save();

        //wait for delivery

        //then what ?
    }
}
