<?php

namespace App\Jobs;

use App\Models\SmsCopy;
use Carbon\Carbon;
use App\Models\LogDownload;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use App\Mail\AppNotification;
use Illuminate\Support\Facades\Log;
use Throwable;

class ExportSmsLogJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $projectId;
    protected $filters;
    protected $user;

    public $timeout = 7200; // 2 hour timeout 
    public $tries = 3;     // Retry up to 3 times
    public $maxExceptions = 3; // Maximum number of exceptions before failing the job
    public $backoff = [60, 300, 600]; // Progressive backoff (1 min, 5 mins, 10 mins)
    
    // Fixed the queue property assignment
   // protected $queue = 'high'; // Use a dedicated queue for exports

    /**
     * Create a new job instance.
     */
    public function __construct($projectId, $user, array $filters = [])
    {
        $this->projectId = $projectId;
        $this->filters = $filters;
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        try {
            Log::info("Starting SMS export job for project {$this->projectId}", [
                'user_id' => $this->user->id,
                'filters' => json_encode($this->filters)
            ]);
            
            // Set PHP settings for this process
            ini_set('memory_limit', '18G');
            ini_set('max_execution_time', 3600);
            ini_set('default_socket_timeout', '-1');

            $fileName = "sms_export_{$this->projectId}_" . now()->format('Ymd_His') . ".csv";
            $filePath = "exports/{$fileName}";

            // Open a file handle using Laravel's storage system
            Storage::makeDirectory('exports');
            $handle = fopen(storage_path("app/{$filePath}"), 'w');
            
            if (!$handle) {
                throw new \Exception("Failed to create file handle for {$filePath}");
            }

            // Write CSV headers
            fputcsv($handle, [
                'Date', 'From', 'To', 'Message', 'Cost',
                'Status', 'Reason', 'Campaign', 'Network',
                'Message ID', 'Delivered @'
            ]);

            // Track progress for logging
            $totalRecords = 0;
            $chunkSize = 5000; // Reduced chunk size for better memory management
            
            // Build and chunk query
            $this->buildOptimizedQuery()->orderBy('id')->chunk($chunkSize, function ($logs) use ($handle, &$totalRecords) {
                $chunkCount = count($logs);
                $totalRecords += $chunkCount;
                Log::info("Processing chunk of {$chunkCount} records, total so far: {$totalRecords}");
                
                foreach ($logs as $log) {
                    $cleanMessage = str_replace(["\r", "\n"], ' ', $log->message ?? '');
                    $cleanMessage = str_replace('"', '""', $cleanMessage);

                    fputcsv($handle, [
                        $log->created_at,
                        $log->from,
                        $log->to,
                        $cleanMessage,
                        $log->price,
                        $log->status,
                        $log->description,
                        $log->campaign_name,
                        $log->telco,
                        $log->message_id,
                        $log->updated_at
                    ]);
                }
                
                // Force garbage collection after each chunk to reduce memory usage
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
            });

            fclose($handle);
            Log::info("CSV file created successfully with {$totalRecords} records: " . storage_path("app/{$filePath}"));

            // Set file permissions to ensure accessibility
            chmod(storage_path("app/{$filePath}"), 0644);

            // Create log download record
            $this->createLogDownloadRecord($filePath, $fileName);
            
            // Send notification email
            $this->sendNotificationEmail($fileName);
            
            Log::info("SMS export job completed successfully for project {$this->projectId}");
            
        } catch (Throwable $e) {
            Log::error("Error in SMS export job: " . $e->getMessage(), [
                'project_id' => $this->projectId,
                'user_id' => $this->user->id,
                'exception' => $e->getTraceAsString()
            ]);
            
            // Try to clean up resources
            if (isset($handle) && is_resource($handle)) {
                fclose($handle);
            }
            
            // Notify user of failure
            $this->notifyFailure($e->getMessage());
            
            // Rethrow to let Laravel's job system handle it
            throw $e;
        }
    }

    /**
     * Apply all filters to the base query, optimized for indexes.
     */
    protected function buildOptimizedQuery()
    {
        // Start with the most selective filter (project_id) to utilize index
        $query = SmsCopy::where('project_id', $this->projectId);

        // Date range filtering - assuming there's an index on created_at
        if (!empty($this->filters['search_date_from']) && !empty($this->filters['search_date_to'])) {
            $query->whereBetween('created_at', [
                $this->filters['search_date_from'],
                Carbon::parse($this->filters['search_date_to'])->endOfDay()
            ]);
        } elseif (!empty($this->filters['search_date_from'])) {
            $query->where('created_at', '>=', $this->filters['search_date_from']);
        } elseif (!empty($this->filters['search_date_to'])) {
            $query->whereDate('created_at', '<=', Carbon::parse($this->filters['search_date_to'])->endOfDay());
        }

        // Apply any indexed column filters first
        // Assuming message_id is unique or highly selective and indexed
        if (!empty($this->filters['search_message_id'])) {
            // Use exact match if possible for better index utilization
            if (strpos($this->filters['search_message_id'], '%') === false) {
                $query->where('message_id', $this->filters['search_message_id']);
            } else {
                $query->where('message_id', 'like', $this->filters['search_message_id']);
            }
        }

        // Then apply phone number filters (often indexed in SMS systems)
        if (!empty($this->filters['search_from'])) {
            if (strpos($this->filters['search_from'], '%') === false) {
                $query->where('from', $this->filters['search_from']);
            } else {
                $query->where('from', 'like', $this->filters['search_from']);
            }
        }

        if (!empty($this->filters['search_to'])) {
            if (strpos($this->filters['search_to'], '%') === false) {
                $query->where('to', $this->filters['search_to']);
            } else {
                $query->where('to', 'like', $this->filters['search_to']);
            }
        }

        // Status filters (usually indexed)
        if (!empty($this->filters['search_status'])) {
            $query->where('status', 'like', '%' . $this->filters['search_status'] . '%');
        }

        // Apply remaining less selective filters
        if (!empty($this->filters['search_message'])) {
            $query->where('message', 'like', '%' . $this->filters['search_message'] . '%');
        }

        if (!empty($this->filters['search_cost'])) {
            $query->where('cost', $this->filters['search_cost']);
        }

        if (!empty($this->filters['search_reason'])) {
            $query->where('description', 'like', '%' . $this->filters['search_reason'] . '%');
        }

        if (!empty($this->filters['search_campaign_name'])) {
            $query->where('campaign_name', 'like', '%' . $this->filters['search_campaign_name'] . '%');
        }

        if (!empty($this->filters['search_network'])) {
            $query->where('network', 'like', '%' . $this->filters['search_network'] . '%');
        }

        if (!empty($this->filters['search_delivered'])) {
            $query->whereDate('delivered_at', $this->filters['search_delivered']);
        }

        // Log the executed query for debugging (in development only)
        if (config('app.env') !== 'production') {
            Log::debug("SMS Export Query: " . $this->getQueryLog($query));
        }

        return $query;
    }

    /**
     * Get a readable version of the query for logging
     */
    protected function getQueryLog($query)
    {
        $sql = $query->toSql();
        $bindings = $query->getBindings();
        
        return vsprintf(str_replace('?', "'%s'", $sql), $bindings);
    }

    /**
     * Create log download record
     */
    protected function createLogDownloadRecord($filePath, $fileName)
    {
        try {
            $fileSize = Storage::size($filePath);
            
            LogDownload::create([
                'project_id' => $this->projectId,
                'file_path' => $filePath,
                'file_name' => $fileName,
                'file_type' => 'csv',
                'file_size' => $fileSize,
                'download_status' => 'pending',
                'created_at' => now(),
            ]);
            
            Log::info("LogDownload entry created successfully", [
                'user_id' => $this->user->id,
                'file_name' => $fileName,
                'file_size' => $fileSize
            ]);
            
        } catch (Throwable $e) {
            // Don't fail the whole job if only the log entry fails
            Log::error('Failed to create LogDownload entry: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send notification email
     */
    protected function sendNotificationEmail($fileName)
    {
        try {
            if ($this->user->email) {
                $emailTo = $this->user->email;
                $bcc = "<EMAIL>";
                $message = "Hi, Your SMS log download is ready. Login > select project > dashboard > History Log to download the file.";
                $message .= "\n\nFile: {$fileName}";
                $subject = "Your Sozuri SMS log download is ready";
                
                Mail::to($emailTo)
                    ->bcc($bcc)
                    ->queue(new AppNotification($message, $subject));
                
                Log::info('Email notification sent successfully', [
                    'email' => $emailTo,
                    'file_name' => $fileName
                ]);
            } else {
                Log::warning('User has no email address for notification', [
                    'user_id' => $this->user->id
                ]);
            }
        } catch (Throwable $e) {
            // Don't fail the whole job if only the email fails
            Log::error('Failed to send email notification: ' . $e->getMessage(), [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Notify user of job failure
     */
    protected function notifyFailure($errorMessage)
    {
        try {
            if ($this->user->email) {
                $emailTo = $this->user->email;
                $bcc = "<EMAIL>";
                $message = "We apologize, but there was a problem generating your SMS log export.";
                $message .= "\n\nOur technical team has been notified and will look into this issue.";
                $message .= "\n\nPlease try again later or contact support if the problem persists.";
                $subject = "Sozuri SMS log export - Processing issue";
                
                Mail::to($emailTo)
                    ->bcc($bcc)
                    ->queue(new AppNotification($message, $subject));
                
                Log::info('Failure notification email sent', [
                    'email' => $emailTo,
                    'error' => $errorMessage
                ]);
            }
        } catch (Throwable $e) {
            Log::error('Failed to send failure notification: ' . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception)
    {
        Log::error('SMS Export Job failed', [
            'project_id' => $this->projectId,
            'user_id' => $this->user->id,
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        // Ensure user is notified of failure
        $this->notifyFailure($exception->getMessage());
    }
}