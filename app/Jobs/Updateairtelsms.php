<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Sms;
use Illuminate\Support\Facades\Log;
use Exception;

class Updateairtelsms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $messageId;
    protected $description;
    protected $providerId;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($messageId, $description, $providerId)
    {
        $this->messageId = $messageId;
        $this->description = $description;
        $this->providerId = $providerId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            Sms::where('message_id', $this->messageId)
            ->update(['status' => "success", 'status_code' => "0",'description' => $this->description, 'request_id' => $this->providerId]);

            Log::info('AIRTEL sms just updated id:'.$this->messageId. 'description:'.$this->description);
        } catch(Exception $e) {
            Log::info($e->getMessage());
        }
    }
}
