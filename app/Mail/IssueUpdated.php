<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Issue;
class IssueUpdated extends Mailable  implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $issue;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Issue $issue)
    {
        $this->issue = $issue;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //Once the data has been set to a public property, it will automatically be available in your view
        return $this->view('emails.lowcredit')
            ->subject('SOZURI: Your Help Ticket/Issue has been updated');
    }
}
