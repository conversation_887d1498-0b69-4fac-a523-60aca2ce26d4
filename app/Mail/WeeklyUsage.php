<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use App\Project;
use App\User;
use Illuminate\Queue\SerializesModels;

class WeeklyUsage extends Mailable  implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $user;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //Once the data has been set to a public property, it will automatically be available in your view
        return $this->view('emails.weeklyusage')
        ->subject('Your Sozuri weekly usage update summary '.date('Y-m-d'));
        //->with(['user'=> $this->user->projects()]);
    }
}
