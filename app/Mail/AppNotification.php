<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AppNotification extends Mailable  implements ShouldQueue
{
    use Queueable, SerializesModels;
    public $message;
    public $subject;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($message, $subject)
    {
        $this->message = $message;
        $this->subject = $subject;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //Once the data has been set to a public property, it will automatically be available in your view
        $subject = $this->subject ? $this->subject : 'SOZURI: Important Notice '.date('Y-m-d');
        return $this->view('emails.appnotification')
            ->subject($subject)
            ->with(['msg' => $this->message]);

    }
}
