<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Project;

class CustomerMonthlySmsReport extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;
    public $project;
    public $groupedData;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($groupedData, Project $project)
    {
        $this->project = $project;
        $this->groupedData = $groupedData;
    }
  

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //Once the data has been set to a public property, it will automatically be available in your view
        return $this->view('emails.customer-monthlysmsreport')
            ->subject('SOZURI: Your monthly SMS report for project '.$this->project->name.': '.now()->subMonth()->format('M Y'));
    }
}
