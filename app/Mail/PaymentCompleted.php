<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use App\Payment;
use Illuminate\Queue\SerializesModels;

class PaymentCompleted extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $payment;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Payment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //Once the data has been set to a public property, it will automatically be available in your view

            return $this->view('emails.paymentcompleted')
            ->subject('SOZURI: Your payment has been completed successfully '.date('Y-m-d'))
            ->with(['payment' => $this->payment]);
    }
}
