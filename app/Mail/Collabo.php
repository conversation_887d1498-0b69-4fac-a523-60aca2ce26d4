<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Collaboration;

class Collabo extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;
    public $collaboration;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Collaboration $collaboration)
    {
        //
        $this->collaboration = $collaboration;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.collaboration')
        ->subject('New Invitation to Collaborate '. time())
        ->with(['collaboration' => $this->collaboration]);
    }
}
