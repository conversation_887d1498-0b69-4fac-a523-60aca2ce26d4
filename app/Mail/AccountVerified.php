<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Project;

class AccountVerified extends Mailable  implements ShouldQueue
{
    use Queueable, SerializesModels;
    public $project;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Project $project)
    {
        $this->project = $project;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        //Once the data has been set to a public property, it will automatically be available in your view
        return $this->view('emails.lowcredit')
            ->subject('SOZURI: You are Running LOW on SMS credits '.date('Y-m-d'));
    }
}
