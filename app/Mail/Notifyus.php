<?php

namespace App\Mail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Notifyme;
class Notifyus extends Mailable
{
    use Queueable, SerializesModels;

    public $notifyme;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Notifyme $notifyme)
    {
        $this->notifyme =  $notifyme;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.notifyme')
        //->from()
        ->subject('new Newsletter Subscription')
        ->with(['notifyme' => $this->notifyme]);
    }
}
