<?php


namespace App\Services;
use App\Contact;
use App\ContactList;
use App\Models\Automation;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Exception;
class AutomationService {

    public function __construct() {
        // Constructor
    }

    public static function doesMessageHaveKeyword(String $keywordString, String $message) {
        $keywords = explode(',',$keywordString);

        // Initialize an array to store matched keywords
        $matchedKeywords = [];
        $words = explode(' ', $message);

        // Iterate over the keywords list
        foreach ($keywords as $keyword) {
            // Check if the keyword exists in the content
            if (stripos($message, $keyword) !== false) {
                // If found, add it to the matchedKeywords array
                $matchedKeywords[] = $keyword;
            }
        }
        return count($matchedKeywords) > 0;
    }
    public static function automate($project, $keyword, $to, $from_channel)
    {
        Log::info('automate' . $keyword . '\n' . $to);
        $campaign = null;
        //get automations
        $automations = Automation::where(['project_id'=> $project->id, 'from_channel' => $from_channel])->get();
        Log::info('Automations found are: ' . $automations->count());
        foreach ($automations as $automation) {
            $reply_from = $automation->reply_from;
            $message = $automation->reply;
            //reply?
            if ($automation->reply_from && $automation->reply && $automation->content) {
                if (Self::doesMessageHaveKeyword($automation->content, $keyword)) {
                    Log::info('has reply');
                    $reply_channel = $automation->reply_channel;
                    $reply_channel == "whatsapp" ? Self::sendWhatsappMessage($project->name, $project->api_token, $reply_from, $to, $message, $campaign, $reply_channel) :
                     Self::sendSmsMessage($project->name, $project->api_token, $reply_from, $to, $message, $campaign, $automation->reply_channel);
                    //check forwarding for the first matched rule
                    if ($automation->forward_to && $automation->forward_to != "") {
                        Log::info('and has forward');
                        $to = trim($automation->forward_to);
                    $reply_channel == "whatsapp" ? Self::sendWhatsappMessage($project->name, $project->api_token, $reply_from, $to, $keyword, $campaign, $reply_channel) :
                    Self::sendSmsMessage($project->name, $project->api_token, $reply_from, $to, $keyword, $campaign, $reply_channel);
                    }
                    return;//exit on first success
                }
            }
        }
        //no automation was matched, send default
        if ($defaultAutomation = Automation::where(['project_id' => $project->id, 'content' => '*', 'from_channel' => $from_channel])->exists()) {
            
            $defaultAutomation = Automation::where(['project_id' => $project->id, 'content' => '*', 'from_channel' => $from_channel])->first();
            Log::info('Default Automation found is: ' . $defaultAutomation->count());
            $reply_from = $defaultAutomation->reply_from;
            $reply_channel = $defaultAutomation->reply_channel;

            $reply_channel == "whatsapp" ? Self::sendWhatsappMessage($project->name, $project->api_token, $reply_from, $to, $defaultAutomation->reply, $campaign, $reply_channel) :
            Self::sendSmsMessage($project->name, $project->api_token, $reply_from, $to, $defaultAutomation->reply, $campaign, $reply_channel);
          
            if ($defaultAutomation->forward_to && $defaultAutomation->forward_to != "") {
                Log::info('and has forward');
                $to = trim($defaultAutomation->forward_to);
                $reply_channel == "whatsapp" ? Self::sendWhatsappMessage($project->name, $project->api_token, $reply_from, $to, $keyword, $campaign, $reply_channel) :
                Self::sendSmsMessage($project->name, $project->api_token, $reply_from, $to, $keyword, $campaign, $reply_channel);

            }

        }
    }
    public static function sendSmsMessage($projectName, $token, $from, $to, $message, $campaign, $channel)
    {
        $client = new Client();
        try {
            Log::warning("Sending message: " . $message);
            $response = $client->request('POST', "https://sozuri.net/api/v1/messaging", [
                 'headers' => [
                     'Content-Type' => 'application/json',
                     'Accept' => 'application/json',
                     'authorization' => 'Bearer ' . $token,
                 ],
                 'allow_redirects' => [
                    'max' => 10
                 ],
                 'json' => ['project' => $projectName, 'from' => $from, 'to' => $to, 'campaign' => $campaign, 'channel' => 'sms', 'message' => $message, 'type' => 'promotional']
             ]);
             if ($response->getStatusCode() >= 300 && $response->getStatusCode() < 400) {
                Log::info("Redirected to: " . $response->getHeader('Location')[0]);
            }
             $body = $response->getBody();
             $stringBody = (string) $body;
             Log::info($stringBody);
        } catch (Exception $e) {
            Log::warning("Error while sending message: " . $e->getMessage());
        }

        return true;
    }

    public static function sendWhatsappMessage($projectName, $token, $from, $to, $message, $campaign, $channel)
    {
        Log::info($projectName."\n". $token."\n". $from."\n".$to."\n". $message."\n". $campaign."\n". $channel);
        $client = new Client();
        try {
             
            Log::warning("Sending message: " . $message);
            $response = $client->request('POST', "https://sozuri.net/api/v1/messaging/whatsapp", [
                 'headers' => [
                     'Content-Type' => 'application/json',
                     'Accept' => 'application/json',
                     'authorization' => 'Bearer ' . $token,
                 ],
                 'allow_redirects' => [
                    'max' => 10
                 ],
                 'json' => ['project' => $projectName, 'from' => $from, 'to' => $to, 'campaign' => $campaign, 'channel' => $channel, 'message' => $message, 'type' => 'text',
                  'text' => ['body' => $message]
                 ]
             ]);
             if ($response->getStatusCode() >= 300 && $response->getStatusCode() < 400) {
                Log::info("Redirected to: " . $response->getHeader('Location')[0]);
            }
             $body = $response->getBody();
             $stringBody = (string) $body;
             Log::info($stringBody);
        } catch (Exception $e) {
            Log::warning("Error while sending message: " . $e->getMessage());
        }

        return true;
    }



    public static function createContact($projectId, $mobile)
    {
        $contact = null;
        $contactListId = ContactList::where(['project_id' => $projectId, 'name' => 'shortcode_contacts'])->exists() ?
            ContactList::where(['project_id' => $projectId, 'name' => 'shortcode_contacts'])->first()->id :
            ContactList::insertGetId(['project_id' => $projectId, 'name' => 'shortcode_contacts']);
        try {

            if (!Contact::where(['project_id' => $projectId, 'tag' => $contactListId, 'mobile' => $mobile])->exists()) {
                $contact = new Contact();
                $contact->project_id = $projectId;
                $contact->mobile = $mobile;
                $contact->tag = $contactListId;

                $contact->save();
                Log::info('contact saved');
            } else {
                Log::info('contact exists');
            }
        } catch (Exception $e) {
            Log::info($e->getMessage());
        }
        return $contact;
    }

}