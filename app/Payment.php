<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    //

    public function paymentmethod()
    {
        return $this->belongsTo('App\Paymentmethod');
    }

    public function paymentstatus()
    {
        return $this->belongsTo('App\Paymentstatus');
    }


    public function transaction()
    {
        return $this->belongsTo('App\Transaction');
    }

    public function project()
    {
        return $this->belongsTo('App\Project');
    }
    public function enrollments()
    {
        return $this->belongsToMany('App\Enrollment');
    }
}
