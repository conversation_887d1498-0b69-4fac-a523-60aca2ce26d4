<?php

namespace App\Imports;

use App\Contact;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;


class ContactsImport implements ToModel, WithHeadingRow
{
    use Importable;

    public function model(array $row)
    {
        return new Contact([


            'mobile' => $row['mobile'],
            'fname' => $row['first name'],
            'mname' => $row['middle name'],
            'lname' => $row['last name'],
            'email' => $row['email'],
            //'list' => $row['list'],
            'type' => $row['category'],
            'job' => $row['job'],
            'company' => $row['company'],
            'city' => $row['city'],
            'isStar' => $row['star'],
            'isSpam' => $row['spam'],
            'isHidden' => $row['hide'],
            'detail' => $row['detail'],
        ]);
    }
}