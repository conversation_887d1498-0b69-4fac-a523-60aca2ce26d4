# Sozuri SMS Platform - Comprehensive Codebase Analysis

## Executive Summary

After conducting a thorough analysis of the Sozuri SMS platform, I've identified critical performance bottlenecks and architectural issues that prevent the system from efficiently handling 10M+ messages. The platform has a solid foundation but requires strategic improvements to scale to 50M+ messages seamlessly.

## Current Architecture Overview

### Message Types & Processing
1. **Bulk SMS**: Sender ID-based, one-way communication (promotional/transactional)
2. **Interactive SMS**: Shortcode (254367) based, two-way communication enabling chat
3. **Premium SMS**: On-demand and subscription-based value services
4. **WhatsApp**: Infrastructure exists but currently inactive

### Multi-tenancy Architecture
- **Project-based isolation**: All data scoped by `project_id`
- **User-project relationships**: Users can own multiple projects + collaboration support
- **API authentication**: Token-based per project

### Message Processing Pipeline
- **Custom consumer service**: File-based log processing (not Kafka/RabbitMQ)
- **Batch processing**: 400 SMS per 3 seconds to Safaricom
- **Redis queue**: Used for Airtel messages
- **Systemd management**: 3 parallel consumer processes

### Database Architecture & Data Flow
```
Active Data: sms table (daily, cleared nightly)
Archive: sms → sms_monthly_copy (02:00 daily)
Permanent: sms → sms_copy + DELETE from sms (00:20 daily)
Cleanup: DELETE old sms_monthly_copy (monthly)
Interactive: sms → interactive_sms (for chat functionality)
```

## Critical Performance Issues Identified

### 1. UI/UX and Reporting Bottlenecks

**Problem**: Reports become unusable with 10M+ messages
- Memory limits artificially increased to 1024M
- Execution time limits set to 3600 seconds
- No server-side pagination
- Client-side DataTables processing large datasets

**Evidence from code**:
```php
// SmsController.php search method
ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 3600);
$smses = DB::table('sms_copy')->where('project_id', $project->id)->latest()->get();
```

### 2. Chat Performance Crisis

**Problem**: Interactive SMS chat extremely slow with 1M+ messages
- Polling-based message retrieval (no real-time updates)
- Inefficient contact list queries loading 6000+ records
- No pagination in chat interface
- Manual refresh required for new messages

**Evidence from code**:
```php
// whatsapp/new-chat.blade.php
$smsInboundNumbers1 = array_unique(
    DB::table('interactive_sms')
        ->where(['project_id' => $project->id])
        ->orderBy('created_at', 'desc')
        ->limit(6000) // Major performance bottleneck
        ->pluck('from')
        ->toArray()
);
```

### 3. Database Performance Issues

**Missing Critical Indexes**:
- Limited indexing on frequently queried columns
- Only basic composite index: `['to','project_id','message_id']` on sms table
- No indexes optimized for reporting queries
- Missing indexes on `interactive_sms` for chat queries

**Query Optimization Gaps**:
- Full table scans on large datasets
- No query result caching
- Inefficient JOIN operations
- No database connection pooling

### 4. Testing Infrastructure Gap

**Critical Missing Components**:
- Minimal test coverage (placeholder tests only)
- No automated testing pipeline
- No performance testing framework
- No integration tests for SMS processing
- No load testing for high-volume scenarios

## Current System Strengths

### 1. Proven Message Processing Reliability
- **Stable throughput**: 400 SMS/3 seconds to Safaricom
- **Robust retry logic**: Exponential backoff in consumer service
- **Effective archival**: Smart data lifecycle management
- **4-year uptime**: No major incidents

### 2. Solid Multi-tenancy Foundation
- **Clean data isolation**: Project-based scoping
- **Proper authorization**: Laravel policies implementation
- **Collaboration features**: Team access management
- **API security**: Token-based authentication

### 3. Infrastructure Stability
- **Adequate resources**: 64GB RAM, 16 CPUs, 500GB SSD
- **Self-hosted control**: Full infrastructure ownership
- **Systemd management**: Reliable service orchestration

## Scalability Bottlenecks Analysis

### 1. Database Layer Limitations
- **Single MySQL instance**: No read replicas for reporting
- **Connection bottlenecks**: No connection pooling
- **Index strategy**: Insufficient for complex reporting queries
- **Query caching**: No implementation of query result caching

### 2. Application Layer Issues
- **Synchronous processing**: Heavy operations in web requests
- **Memory consumption**: Inefficient data loading patterns
- **No caching layer**: Repeated database queries
- **Pagination strategy**: Client-side processing of large datasets

### 3. Frontend Performance Problems
- **jQuery limitations**: Outdated frontend technology
- **DOM manipulation**: Inefficient rendering of large datasets
- **No lazy loading**: All data loaded upfront
- **Client-side processing**: DataTables struggling with large datasets

## Technology Stack Assessment

### Current Stack
- **Backend**: Laravel 7.x/8.x (appears outdated)
- **Database**: MySQL/MariaDB (single instance)
- **Cache/Queue**: Redis (underutilized)
- **Frontend**: jQuery + DataTables + Bootstrap
- **Infrastructure**: AWS EC2 (single server)

### Stack Evaluation
- **Laravel version**: Security and feature concerns
- **Frontend technology**: Limits modern UX capabilities
- **Database architecture**: Single point of failure
- **Caching strategy**: Minimal implementation
- **Monitoring**: No application performance monitoring

## Security Assessment

### Current Security Measures
- CSRF protection implemented
- API token authentication
- Project-based authorization policies
- Input sanitization for SMS content
- SQL injection protection (mostly)

### Security Gaps Identified
- Potential raw query vulnerabilities
- No API rate limiting
- Limited audit logging
- No encryption for sensitive data at rest
- Missing security headers

## Performance Metrics & Bottleneck Analysis

### Current Performance Characteristics
- **Message throughput**: 400 SMS/3 seconds (Safaricom)
- **Daily processing**: 1M+ bulk SMS campaigns
- **UI breaking point**: 10M+ messages
- **Chat performance**: Severely degraded with 1M+ messages
- **Report generation**: Frequent timeouts

### Root Cause Analysis
1. **Database queries**: Full table scans on large datasets
2. **Memory usage**: Loading entire result sets into memory
3. **Frontend rendering**: Client-side processing of large datasets
4. **Real-time features**: Polling instead of push notifications
5. **Indexing strategy**: Insufficient for reporting workloads

## Immediate Critical Issues Requiring Attention

### 1. Chat Functionality Crisis
- **Impact**: Customer service severely impacted
- **Root cause**: Inefficient queries and no real-time updates
- **Priority**: CRITICAL - affects daily operations

### 2. Reporting System Breakdown
- **Impact**: Business intelligence and customer reports failing
- **Root cause**: Memory and timeout issues with large datasets
- **Priority**: HIGH - affects customer satisfaction

### 3. UI/UX Degradation
- **Impact**: User experience becomes unusable at scale
- **Root cause**: Client-side processing limitations
- **Priority**: HIGH - affects platform usability

### 4. Testing Infrastructure Gap
- **Impact**: Risk of regressions and performance degradation
- **Root cause**: No automated testing framework
- **Priority**: MEDIUM - affects development velocity

## Gradual Improvement Strategy (No Complete Overhaul)

### Phase 1: Immediate Fixes (0-1 month)
**Goal**: Stabilize current performance issues

1. **Database Optimization**
   - Add critical indexes for reporting queries
   - Implement query result caching
   - Optimize slow queries identified in logs

2. **Chat Performance Fix**
   - Implement server-side pagination for chat
   - Add WebSocket support for real-time updates
   - Optimize contact list queries

3. **Reporting Improvements**
   - Implement server-side DataTables processing
   - Add pagination to all large dataset views
   - Remove memory/time limit hacks

### Phase 2: Foundation Strengthening (1-3 months)
**Goal**: Build sustainable performance foundation

1. **Caching Layer Implementation**
   - Redis caching for frequently accessed data
   - Query result caching
   - Session-based pagination caching

2. **Database Enhancements**
   - Add read replica for reporting
   - Implement connection pooling
   - Database query optimization

3. **Testing Framework**
   - Unit tests for critical components
   - Integration tests for SMS processing
   - Performance testing suite

### Phase 3: Scalability Improvements (3-6 months)
**Goal**: Prepare for 50M+ message handling

1. **Frontend Modernization**
   - Implement Vue.js/React for dynamic components
   - Add lazy loading and virtual scrolling
   - Implement real-time updates

2. **API Optimization**
   - Add API rate limiting
   - Implement API versioning
   - Add comprehensive API documentation

3. **Monitoring & Observability**
   - Application performance monitoring
   - Database performance monitoring
   - Real-time alerting system

### Phase 4: Advanced Scaling (6-12 months)
**Goal**: Enterprise-grade scalability

1. **Microservices Preparation**
   - Extract reporting service
   - Separate chat service
   - API gateway implementation

2. **Advanced Database Strategy**
   - Database sharding strategy
   - Advanced indexing and partitioning
   - Data warehouse for analytics

3. **Infrastructure Scaling**
   - Load balancer implementation
   - Auto-scaling groups
   - CDN for static assets

## Specific Technical Recommendations

### 1. Database Optimization (Immediate Priority)

**Critical Indexes to Add**:
```sql
-- For reporting queries
CREATE INDEX idx_sms_copy_project_created_status ON sms_copy(project_id, created_at, status);
CREATE INDEX idx_sms_copy_project_campaign_created ON sms_copy(project_id, campaign_id, created_at);

-- For chat functionality
CREATE INDEX idx_interactive_sms_project_from_created ON interactive_sms(project_id, `from`, created_at);
CREATE INDEX idx_interactive_sms_project_to_created ON interactive_sms(project_id, `to`, created_at);

-- For dashboard queries
CREATE INDEX idx_sms_copy_created_status_telco ON sms_copy(created_at, status, telco);
```

**Query Optimization Examples**:
```php
// Replace this inefficient query
$smses = DB::table('sms_copy')->where('project_id', $project->id)->latest()->get();

// With paginated, indexed query
$smses = DB::table('sms_copy')
    ->where('project_id', $project->id)
    ->orderBy('created_at', 'desc')
    ->paginate(50);
```

### 2. Chat Performance Fix (Critical)

**WebSocket Implementation**:
```php
// Add Laravel WebSockets package
composer require beyondcode/laravel-websockets

// Create real-time chat events
php artisan make:event NewInteractiveSmsReceived
```

**Optimized Chat Queries**:
```php
// Replace inefficient contact loading
// From: limit(6000)->pluck('from')->toArray()
// To: Paginated contact loading with search
public function getContacts(Request $request) {
    return DB::table('interactive_sms')
        ->where('project_id', $request->project_id)
        ->when($request->search, function($query, $search) {
            return $query->where('from', 'like', "%{$search}%");
        })
        ->select('from', DB::raw('MAX(created_at) as last_message'))
        ->groupBy('from')
        ->orderBy('last_message', 'desc')
        ->paginate(20);
}
```

### 3. Server-Side DataTables Implementation

**Controller Enhancement**:
```php
public function getDataTableData(Request $request) {
    $query = DB::table('sms_copy')
        ->where('project_id', $request->project_id);

    // Apply search filters
    if ($request->search['value']) {
        $search = $request->search['value'];
        $query->where(function($q) use ($search) {
            $q->where('message', 'like', "%{$search}%")
              ->orWhere('from', 'like', "%{$search}%")
              ->orWhere('to', 'like', "%{$search}%");
        });
    }

    // Apply ordering
    $orderColumn = $request->order[0]['column'] ?? 0;
    $orderDir = $request->order[0]['dir'] ?? 'desc';
    $columns = ['created_at', 'from', 'to', 'message', 'status'];
    $query->orderBy($columns[$orderColumn], $orderDir);

    // Get total count before pagination
    $totalRecords = $query->count();

    // Apply pagination
    $query->skip($request->start)->take($request->length);

    return response()->json([
        'draw' => $request->draw,
        'recordsTotal' => $totalRecords,
        'recordsFiltered' => $totalRecords,
        'data' => $query->get()
    ]);
}
```

### 4. Caching Strategy Implementation

**Redis Caching for Common Queries**:
```php
// Cache project statistics
public function getProjectStats($projectId) {
    return Cache::remember("project_stats_{$projectId}", 300, function() use ($projectId) {
        return DB::table('sms_copy')
            ->where('project_id', $projectId)
            ->selectRaw('
                COUNT(*) as total_messages,
                SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as successful,
                SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed,
                SUM(price) as total_cost
            ')
            ->first();
    });
}

// Cache contact lists for chat
public function getCachedContacts($projectId) {
    return Cache::remember("chat_contacts_{$projectId}", 600, function() use ($projectId) {
        return DB::table('interactive_sms')
            ->where('project_id', $projectId)
            ->select('from', DB::raw('MAX(created_at) as last_message'))
            ->groupBy('from')
            ->orderBy('last_message', 'desc')
            ->limit(100)
            ->get();
    });
}
```

### 5. Testing Framework Setup

**Basic Test Structure**:
```php
// tests/Feature/SmsProcessingTest.php
class SmsProcessingTest extends TestCase {
    public function test_bulk_sms_processing() {
        // Test bulk SMS processing pipeline
        $project = Project::factory()->create();
        $campaign = Campaign::factory()->create(['project_id' => $project->id]);

        $response = $this->postJson("/api/v1/sms", [
            'project' => $project->name,
            'campaign' => $campaign->name,
            'message' => 'Test message',
            'numbers' => ['+254700000000']
        ]);

        $response->assertStatus(200);
        $this->assertDatabaseHas('sms', [
            'project_id' => $project->id,
            'message' => 'Test message'
        ]);
    }

    public function test_interactive_sms_chat_performance() {
        // Test chat functionality with large datasets
        $project = Project::factory()->create();
        InteractiveSms::factory()->count(1000)->create(['project_id' => $project->id]);

        $startTime = microtime(true);
        $response = $this->get("/projects/{$project->id}/chat/contacts");
        $endTime = microtime(true);

        $response->assertStatus(200);
        $this->assertLessThan(2.0, $endTime - $startTime); // Should complete in under 2 seconds
    }
}
```

## Questions for Clarification

1. **Laravel Version**: What exact Laravel version are you running? This affects upgrade strategy.

2. **Database Size**: What's the current size of your `sms_copy` table? This helps prioritize optimization efforts.

3. **Peak Usage**: What are your peak concurrent users and message volumes per hour?

4. **Budget Constraints**: Are there budget limitations for infrastructure improvements (read replicas, CDN, etc.)?

5. **Downtime Tolerance**: What's the acceptable downtime window for database optimizations?

6. **Monitoring Tools**: Do you have any existing monitoring tools (New Relic, DataDog, etc.)?

7. **Backup Strategy**: What's your current backup and disaster recovery strategy?

## Implementation Priority Matrix

### Critical (Fix Immediately)
1. Add database indexes for reporting queries
2. Implement server-side pagination for SMS views
3. Fix chat contact loading performance
4. Add basic caching for dashboard statistics

### High Priority (1-2 months)
1. Implement WebSocket for real-time chat
2. Add comprehensive testing framework
3. Upgrade Laravel to latest LTS
4. Implement Redis caching strategy

### Medium Priority (3-6 months)
1. Frontend modernization with Vue.js
2. Database read replica for reporting
3. API rate limiting and optimization
4. Advanced monitoring implementation

### Future Considerations (6+ months)
1. Microservices architecture
2. Database sharding strategy
3. Advanced scaling infrastructure
4. Machine learning for SMS optimization

## Conclusion

The Sozuri platform has a solid foundation but requires strategic improvements to handle 50M+ messages efficiently. The recommended gradual approach ensures minimal disruption to existing customers while systematically addressing performance bottlenecks.

The key is to start with database optimization and caching, then move to frontend improvements, and finally consider architectural changes. This approach will provide immediate relief while building toward long-term scalability.

**Next immediate actions**:
1. Implement critical database indexes
2. Add server-side pagination to SMS views
3. Fix chat performance with optimized queries
4. Set up basic performance monitoring

This roadmap will transform Sozuri from a system struggling with 10M messages to one that can seamlessly handle 50M+ messages while maintaining excellent user experience.

---

## SEEDING STRATEGY FOR PERFORMANCE TESTING

### Current Seeding Infrastructure Assessment

**Existing Strengths:**
- Basic factories for core models (SMS, Contact, Project, Campaign)
- DatabaseSeeder structure in place
- Laravel 9 factory system partially implemented

**Critical Gaps Identified:**
1. **InteractiveSmsFactory** - Empty implementation
2. **No SMS archive table seeders** (sms_copy, sms_monthly_copy)
3. **Small scale testing** - Current SMS seeder only creates 5,000 records
4. **Unrealistic data patterns** - Factories don't reflect production data characteristics
5. **No performance-optimized seeding** - Will be too slow for millions of records

### Seeding Strategy for 50M+ Records

#### Phase 1: Local Environment Setup
```bash
# Install MySQL locally (Ubuntu/Debian)
sudo apt update
sudo apt install mysql-server mysql-client

# Install Redis locally
sudo apt install redis-server

# Configure MySQL for heavy seeding
sudo mysql -e "SET GLOBAL innodb_buffer_pool_size = 2G;"
sudo mysql -e "SET GLOBAL innodb_log_file_size = 512M;"
sudo mysql -e "SET GLOBAL max_allowed_packet = 1G;"
```

#### Phase 2: Optimized Factory Implementations

**1. Enhanced SMS Factory (Realistic Production Data)**
```php
// database/factories/SmsFactory.php
$factory->define(App\Sms::class, function (Faker $faker) {
    $telcos = ['safaricom', 'airtel', 'telkom'];
    $statuses = ['success', 'failed', 'sent', 'unknown_error'];
    $statusWeights = [70, 15, 10, 5]; // 70% success rate

    return [
        'project_id' => $faker->numberBetween(1, 10), // 10 test projects
        'campaign_id' => $faker->numberBetween(1, 50), // 50 campaigns
        'message' => $faker->realText(160), // SMS length limit
        'from' => $faker->randomElement(['SOZURI', 'INFO', 'ALERT', '254367']),
        'to' => '+254' . $faker->numberBetween(700000000, 799999999), // Kenyan numbers
        'cost' => $faker->randomFloat(2, 0.5, 2.0), // Realistic SMS costs
        'price' => $faker->randomFloat(2, 0.5, 2.0),
        'status' => $faker->randomElement($statuses, $statusWeights),
        'telco' => $faker->randomElement($telcos),
        'message_id' => 'SMS_' . $faker->uuid,
        'message_part' => $faker->numberBetween(1, 3),
        'created_at' => $faker->dateTimeBetween('-2 years', 'now'),
        'updated_at' => function (array $attributes) {
            return $attributes['created_at'];
        },
    ];
});
```

**2. Interactive SMS Factory (Chat Data)**
```php
// database/factories/InteractiveSmsFactory.php
use App\Models\InteractiveSms;

class InteractiveSmsFactory extends Factory
{
    protected $model = InteractiveSms::class;

    public function definition()
    {
        $directions = ['inbound-api', 'outbound-api'];
        $customerNumbers = $this->generateCustomerNumbers(1000); // 1000 unique customers

        return [
            'project_id' => $this->faker->numberBetween(1, 10),
            'campaign_id' => $this->faker->numberBetween(1, 50),
            'from' => $this->faker->randomElement(['254367', ...$customerNumbers]),
            'to' => $this->faker->randomElement(['254367', ...$customerNumbers]),
            'message' => $this->faker->realText(160),
            'direction' => $this->faker->randomElement($directions),
            'status' => 'success',
            'telco' => 'safaricom',
            'created_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }

    private function generateCustomerNumbers($count)
    {
        $numbers = [];
        for ($i = 0; $i < $count; $i++) {
            $numbers[] = '+254' . $this->faker->numberBetween(700000000, 799999999);
        }
        return $numbers;
    }
}
```

**3. Contact Factory (Realistic Contact Data)**
```php
// Enhanced ContactFactory.php
$factory->define(App\Contact::class, function (Faker $faker) {
    return [
        'project_id' => $faker->numberBetween(1, 10),
        'fname' => $faker->firstName,
        'lname' => $faker->lastName,
        'mobile' => '+254' . $faker->numberBetween(700000000, 799999999),
        'email' => $faker->unique()->safeEmail,
        'tag' => $faker->numberBetween(1, 20), // 20 contact lists
        'created_at' => $faker->dateTimeBetween('-2 years', 'now'),
    ];
});
```

#### Phase 3: High-Performance Seeders

**1. Bulk SMS Seeder (Optimized for Millions)**
```php
// database/seeds/BulkSmsSeeder.php
class BulkSmsSeeder extends Seeder
{
    public function run()
    {
        $this->command->info('Starting bulk SMS seeding...');

        // Disable foreign key checks for speed
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Seed in chunks to avoid memory issues
        $totalRecords = 5000000; // 5 million records
        $chunkSize = 10000;
        $chunks = ceil($totalRecords / $chunkSize);

        $bar = $this->command->getOutput()->createProgressBar($chunks);

        for ($i = 0; $i < $chunks; $i++) {
            $this->seedChunk($chunkSize);
            $bar->advance();
        }

        $bar->finish();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $this->command->info("\nBulk SMS seeding completed!");
    }

    private function seedChunk($size)
    {
        $data = [];
        $faker = Faker\Factory::create();

        for ($i = 0; $i < $size; $i++) {
            $data[] = [
                'project_id' => $faker->numberBetween(1, 10),
                'campaign_id' => $faker->numberBetween(1, 50),
                'message' => $faker->realText(160),
                'from' => $faker->randomElement(['SOZURI', 'INFO', 'ALERT']),
                'to' => '+254' . $faker->numberBetween(700000000, 799999999),
                'cost' => $faker->randomFloat(2, 0.5, 2.0),
                'price' => $faker->randomFloat(2, 0.5, 2.0),
                'status' => $faker->randomElement(['success', 'failed', 'sent']),
                'telco' => $faker->randomElement(['safaricom', 'airtel', 'telkom']),
                'message_id' => 'SMS_' . uniqid(),
                'created_at' => $faker->dateTimeBetween('-2 years', 'now'),
                'updated_at' => now(),
            ];
        }

        DB::table('sms')->insert($data);
    }
}
```

**2. SMS Archive Seeders**
```php
// database/seeds/SmsCopySeeder.php
class SmsCopySeeder extends Seeder
{
    public function run()
    {
        $this->command->info('Seeding SMS Copy table (50M records)...');

        // Copy from SMS table and add more historical data
        DB::statement('INSERT INTO sms_copy SELECT * FROM sms');

        // Add additional historical data
        $this->seedHistoricalData(45000000); // 45M additional records
    }

    private function seedHistoricalData($count)
    {
        // Use raw SQL for maximum performance
        $sql = "
            INSERT INTO sms_copy (project_id, campaign_id, message, `from`, `to`,
                                cost, price, status, telco, message_id, created_at, updated_at)
            SELECT
                FLOOR(1 + RAND() * 10) as project_id,
                FLOOR(1 + RAND() * 50) as campaign_id,
                CONCAT('Historical message ', FLOOR(RAND() * 1000000)) as message,
                ELT(FLOOR(1 + RAND() * 3), 'SOZURI', 'INFO', 'ALERT') as `from`,
                CONCAT('+254', FLOOR(700000000 + RAND() * 99999999)) as `to`,
                ROUND(0.5 + RAND() * 1.5, 2) as cost,
                ROUND(0.5 + RAND() * 1.5, 2) as price,
                ELT(FLOOR(1 + RAND() * 3), 'success', 'failed', 'sent') as status,
                ELT(FLOOR(1 + RAND() * 3), 'safaricom', 'airtel', 'telkom') as telco,
                CONCAT('SMS_', UUID()) as message_id,
                DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 730) DAY) as created_at,
                NOW() as updated_at
            FROM
                (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4) t1,
                (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4) t2,
                (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4) t3,
                (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4) t4
            LIMIT {$count}
        ";

        DB::statement($sql);
    }
}
```

#### Phase 4: Seeding Commands & Scripts

**1. Master Seeding Command**
```php
// app/Console/Commands/SeedProductionData.php
class SeedProductionData extends Command
{
    protected $signature = 'seed:production-data {--table=all}';
    protected $description = 'Seed production-like data for performance testing';

    public function handle()
    {
        $table = $this->option('table');

        $this->info('Starting production data seeding...');
        $this->info('This will take several hours for full dataset.');

        if ($table === 'all' || $table === 'foundation') {
            $this->call('db:seed', ['--class' => 'UsersTableSeeder']);
            $this->call('db:seed', ['--class' => 'ProjectsTableSeeder']);
            $this->call('db:seed', ['--class' => 'CampaignsTableSeeder']);
        }

        if ($table === 'all' || $table === 'contacts') {
            $this->call('db:seed', ['--class' => 'BulkContactSeeder']);
        }

        if ($table === 'all' || $table === 'sms') {
            $this->call('db:seed', ['--class' => 'BulkSmsSeeder']);
        }

        if ($table === 'all' || $table === 'interactive') {
            $this->call('db:seed', ['--class' => 'InteractiveSmsSeeder']);
        }

        if ($table === 'all' || $table === 'archive') {
            $this->call('db:seed', ['--class' => 'SmsCopySeeder']);
            $this->call('db:seed', ['--class' => 'SmsMonthlyCopySeeder']);
        }

        $this->info('Production data seeding completed!');
    }
}
```

#### Phase 5: Database Optimization for Seeding

**MySQL Configuration for Heavy Seeding:**
```sql
-- Temporary settings for seeding
SET GLOBAL innodb_buffer_pool_size = 4294967296; -- 4GB
SET GLOBAL innodb_log_file_size = 1073741824;    -- 1GB
SET GLOBAL innodb_flush_log_at_trx_commit = 0;   -- Faster but less safe
SET GLOBAL sync_binlog = 0;                      -- Disable for seeding
SET GLOBAL foreign_key_checks = 0;               -- Disable FK checks
```

**Seeding Performance Script:**
```bash
#!/bin/bash
# scripts/seed-performance-data.sh

echo "Configuring MySQL for heavy seeding..."
mysql -u root -p -e "
SET GLOBAL innodb_buffer_pool_size = 4294967296;
SET GLOBAL innodb_flush_log_at_trx_commit = 0;
SET GLOBAL sync_binlog = 0;
"

echo "Starting foundation data seeding..."
php artisan seed:production-data --table=foundation

echo "Seeding contacts (2M records)..."
php artisan seed:production-data --table=contacts

echo "Seeding SMS data (5M records)..."
php artisan seed:production-data --table=sms

echo "Seeding interactive SMS (1M records)..."
php artisan seed:production-data --table=interactive

echo "Seeding archive data (50M records)..."
php artisan seed:production-data --table=archive

echo "Restoring MySQL settings..."
mysql -u root -p -e "
SET GLOBAL innodb_flush_log_at_trx_commit = 1;
SET GLOBAL sync_binlog = 1;
"

echo "Seeding completed! Database ready for performance testing."
```