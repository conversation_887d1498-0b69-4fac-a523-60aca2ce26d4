AI is here,planning is key!

scan the codebase. let me know what you think about how messages are managed. sozuri deals with sms messages. bulk messages have a sender id and cannot be replied to, interactive messages are sent using a shortcode(254367) and can be replied to by subscribers, that is why you can chat in sozuri with customers via a shortcode. premium sms are of two types, ondemand and subscription and are used to send value messages to customers and their airtime is deducted accordingly. deduction of airtime happens at the telco not sozuri. whatsapp messages are not active for now. bulk sms are of two types promotional and transactional but their is no difference between them other than regulation. multitenancy is achieved via projects which own everything. a user can have several projects. there is an admin console. there is a big problem with ui/ux and handling/reports for projects with over 10 million messages. timeouts and slow unoptimized reports. the chat for interactive sms is super slow. outbound interactive sms is sent the same way as normal sms with no difference at all. interactive sms is also saved in sms table for easy aggregation, and in their own table for easy querying. because of the huge sms load, sms is actively saved in sms table, sent to monthly sms and sms copy tables every night and deleted ready for the next day, this ensures fast querying. kafka and rabbit are not used, but rather a dedicated custom service is used, see the consumer folder. the script is run by systemd and kept active and picks sms from the log file at intervals. i run about 3 of these processes to churn out sms and send to telco, especially safaricom. airtel messages are sent via a redis queue. this method allows me to send like 400sms per 3 seconds which is a good speed for customers especially who send 1million bulk sms at once. i use self hosted mysql and laravel  both in an ec2 of 64gb ram and 16 cpus and 500gb ssd. i have not had incidences in the last 4 years. 

analyze this codebase like a pro. take your time. write a summarized document in the docs folder. we need to improve ui/ux and reporting of messages. provide solid methods to gradually make this system work with millions of messages without doing a complete overhaul since it has active customers. ask me questions about areas you dont understand. do a serious deepdive.

as an experienced laravel, web app, devops engineer


# interactive-sms-chat

-chat is slow with about 1mill messages
-seeder for users, admin, projects, campaigns, contacts, deposits, templates, sms, messages
-seed 1 mill interactive messages