<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/db.php';

use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Client;
use Dotenv\Dotenv;


/**
 * This script is responsible for:
 * 1. reading the database-ready log file, 
 * 2. persist message then
 * 3. send http request upstream
 */
class Consumer
{
    /**
     * Grab all info from sms log and create an array
     */
    private static function getLines($path): array
    {
        $file_stream = fopen($path, "r"); //or die("Unable to open file!");
        $lines = array();
        while (($line = fgets($file_stream)) !== false) {
            array_push($lines, json_decode($line, TRUE));
        }
        fclose($file_stream);

        if (count($lines) < 1) {
            echo "no data\n";
        }
        //$merged_lines = $lines ?  call_user_func_array('array_merge', $lines) : [];
        $merged_lines = $lines ? array_merge(...$lines) : [];
        shell_exec("echo -n > " . $path);
        return $merged_lines;
    }
    /**
     * insert records from array into database
     */
    private function persist($db_smses)
    {
        foreach (array_chunk($db_smses, 2000) as $raw_db_sms) {
            $sql = array();
            foreach ($raw_db_sms as $sms) {
                $sql[] = "('" . $sms['channel'] . "','" . $sms['package_id'] . "','" . $this->escapeApostrophe($sms['message']) . "','" . $sms['description'] . "', '" . $sms['price'] . "','" . $sms['project_id'] . "','" . $sms['from'] . "','" . $sms['campaign_id'] . "','" . $sms['telco']
                    . "','" . $sms['message_id'] . "','" . $sms['to'] . "','" . $sms['status'] . "','" . $sms['status_code'] . "','" . $sms['bulk_id'] . "','" . $sms['message_part'] . "','" . $sms['type'] . "','" . $sms['uri'] . "','" . $sms['cost'] . "','" . $sms['created_at'] . "','" . $sms['campaign_name'] . "')";
            }
            $db_instance = DatabaseConnection::getInstance();
            $conn = $db_instance->getConnection();
            $sql = 'INSERT INTO sms ( channel, package_id, `message`, `description`, price, project_id,`from`, `campaign_id`, telco, message_id,`to`, `status`, status_code, bulk_id, message_part, `type`, uri, cost, created_at, campaign_name) 
                VALUES ' . implode(',', $sql);
            echo $sql;
            try {
                $conn->query($sql);
            } catch (Exception $e) {
                var_dump($e);
            }
            echo date('Y-m-d H:i:s') . "saved to db \n";
        }
        return true;
    }
    /**
     * Escape apostrophes for database insertion
     */
    private function escapeApostrophe($message)
    {
        return preg_replace('/\'/', "\'", $message);

    }
    /**
     * persist to DB and then make HTTP request
     */
    public function consume()
    {
        //$dotenv = Dotenv::createImmutable(__DIR__);
        $dotenv = Dotenv::create(__DIR__);

        $dotenv->load();
        $base_uri = $_ENV['base_uri'];
        $SourceAddress = $_ENV['SourceAddress'];
        $token_location = $_ENV['token_location'];
        $sms_location = $_ENV['sms_location'];
        $sdp_location = $_ENV['sdp_location'];

        $time_start = microtime(true);
        $db_smses = $this->getLines($sms_location);
        $this->persist($db_smses);

        $sdp_smses = $this->getLines($sdp_location);

        $tokenfile = fopen($token_location, "r");
        $token = fgets($tokenfile);
        fclose($tokenfile);
        $timestamp = time();
        $client = new Client(['base_uri' => $base_uri]);  //, 'verify' => false]);
        foreach (array_chunk($sdp_smses, 400) as $sdp_sms) {
            //do ....... while $status != "SUCCESS"
            $tries = 0;
            $maxRetries = 20;
            while ($tries < $maxRetries) {
                try {
                    $response = $client->request('POST', 'public/CMS/bulksms', [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Accept' => 'application/json',
                            'X-Authorization' => 'Bearer ' . $token,
                            'SourceAddress' => $SourceAddress,
                            'Connection' => 'keep-alive',
                        ],
                        'json' => [
                            "timeStamp" => $timestamp,
                            "dataSet" => $sdp_sms
                        ],
                        [
                            'timeout' => 10, // Response timeout .....240
                            'connect_timeout' => 10, // Connection timeout ...240
                        ]
                    ]);
                    //$code = $response->getStatusCode(); // 200
                    //$reason = $response->getReasonPhrase(); // OK
                    $body = $response->getBody();
                    $stringBody = (string) $body;
                    echo date('Y-m-d H:i:s') . ' request attempt' . $tries . ' completed in: ' . (string) (microtime(true) - $time_start) . "\n" . $stringBody;
                    //reattempt on failure
                    //$status = (json_decode($stringBody, true))["status"];  //{"keyword":"BULK","status":"SUCCESS","statusCode":"SC0000"}
                    //sleep(1); //vary this according to outgoing volumes t
                    break;
                } catch (Exception $e) {
                    echo date('Y-m-d H:i:s') . ": consumer error on attempt " . $tries . " in: " . (string) (microtime(true) - $time_start) . "s " . $e->getMessage() . "\n";
                    $tries++;
                    usleep(100000 * ($tries ** 2)); // Exponential backoff for retry delay
                }
            }
            if ($tries >= $maxRetries) {
                // Max retries reached for this SMS message, log or handle accordingly
                echo "Max retries " . $tries . " reached for SMS message: " . json_encode($sdp_sms) . "\n";
            }
        }
    }
}


while (true) {
    ini_set('memory_limit', '10000M');
    // ini_set('max_input_time', '1800');
    ini_set('max_execution_time', 3600); //default=30.You can not change this setting with ini_set() when running in safe mode
    //set_time_limit(800);//same as above
    ini_set('default_socket_timeout', '-1');
    $consumer = new Consumer();
    $consumer->consume();
    sleep(5); //vary this according to outgoing volumes
}
