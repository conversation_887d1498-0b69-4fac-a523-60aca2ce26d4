<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/db.php';

use Dotenv\Dotenv;
/**
 * This script is responsible for:
 * 1. reading the database-ready payments log file, 
 * 2. persist payment information to db
 */
class Updatepay
{

    private static function getLines($path): array
    {
        $file_stream = fopen($path, "r"); //or die("Unable to open file!");
        $lines = array();
        while (($line = fgets($file_stream)) !== false) {
            array_push($lines, json_decode($line, TRUE));
        }
        fclose($file_stream);
        if (count($lines) < 1) {
            echo "no data\n";
        }

        shell_exec("echo -n > " . $path);
        return $lines;
    }
    private function persist(array $payments)
    {
        $combined_payments = [];
        foreach ($payments as $key => $value) {

            if (array_key_exists($value['payment_id'], $combined_payments)) {
                $combined_payments[$value['payment_id']] += $value['usage'];
            } else {
                $combined_payments[$value['payment_id']] = $value['usage'];
            }
        }

        print_r($combined_payments);

        try {

            $db_instance = DatabaseConnection::getInstance();
            $conn = $db_instance->getConnection();
            $prepStatement = $conn->prepare("UPDATE payments SET balance = (balance - :usage) WHERE (id= :payment_id)");

            foreach ($combined_payments as $payment_id => $usage) {
                if ($prepStatement->execute(array(':payment_id' => $payment_id, ':usage' => $usage))) {
                    echo "Saved payment";
                } else {
                    echo "Error when executing prepared statement";
                }
            }

            echo date('Y-m-d H:i:s') . "saved to db \n";
        } catch (Exception $e) {
            var_dump($e);
        }
        return;
    }

    public function update()
    {
        $time_start = microtime(true);
        try {
            $dotenv = Dotenv::create(__DIR__);
            $dotenv->load();
            $payment_path = $_ENV['payment_path'];
            $payments = $this->getLines($payment_path); //or die("Unable to open file!");
            if (count($payments) > 1) {
                $this->persist($payments);
                echo date('Y-m-d H:i:s') . 'payment update completed in:' . (string) (microtime(true) - $time_start) . "\n";
            }

        } catch (Exception $e) {
            echo $e->getMessage() . "\n";
        }
    }
}


while (true) {
    $updator = new Updatepay();
    $updator->update();
    sleep(5); //vary this according to outgoing volumes t
}
