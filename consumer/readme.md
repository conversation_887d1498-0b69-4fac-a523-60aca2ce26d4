
systemd logs and error log for this scrips is  saved in logs/ folder



the index and update-payment can be run by supervisor container or supervisord service on a serveror systemd service
the point is to ensure the two files are being called in a long running fashion since this is a message broker




sudo git clone https://<EMAIL>/lawrencekm04/message_log_consumer.git


sudo chown -R www-data: message_log_consumer/

sudo chmod -R 775  message_log_consumer/logs/
 systemd services

sudo nano /etc/systemd/system/consume-payments.service
[Unit]
Description=Run a PHP script to consume payments on log queue

[Service]
User=www-data
Group=www-data
Restart=always
WorkingDirectory=/var/www/message_log_consumer
ExecStart=/usr/bin/php update-payment.php
StandardOutput=append:/var/www/message_log_consumer/logs/payment-persist.log
StandardError=append:/var/www/message_log_consumer/logs/payment-persist-error.log

[Install]
WantedBy=default.target



sudo nano /etc/systemd/system/consume-messages.service
[Unit]
Description=Run a PHP script to consumed messages on log queue

[Service]
User=www-data
Group=www-data
Restart=always
WorkingDirectory=/var/www/message_log_consumer
ExecStart=/usr/bin/php index.php
StandardOutput=append:/var/www/message_log_consumer/logs/sms-persist-http.log
StandardError=append:/var/www/message_log_consumer/logs/sms-persist-http-error.log

[Install]
WantedBy=default.target




alternate method to read file
/*
$data_stream = file_get_contents("/home/<USER>/code/talkzuri/storage/logs/consumer-persist-http.log");
$data_array = explode("\n",$data_stream);
$sdp_smses = call_user_func_array('array_merge', $data_array);
shell_exec("echo '' > storage/logs/consumer-persist-http.log");
*/