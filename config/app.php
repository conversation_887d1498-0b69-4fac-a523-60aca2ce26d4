<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */

    'name' => env('APP_NAME', 'Laravel'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services your application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => env('APP_URL', 'https://sozuri.net'),
    //'url' => env('APP_URL', 'http://hoomestead.talkzuri'),


    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    //'timezone' => 'UTC',
    'timezone' => 'Africa/Nairobi',


    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log settings for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Settings: "single", "daily", "syslog", "errorlog"
    |
    */

    'log' => env('APP_LOG', 'single'),

    'log_level' => env('APP_LOG_LEVEL', 'debug'),

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => [

        /*
         * Laravel Framework Service Providers...
         */
        Illuminate\Auth\AuthServiceProvider::class,
        Illuminate\Broadcasting\BroadcastServiceProvider::class,
        Illuminate\Bus\BusServiceProvider::class,
        Illuminate\Cache\CacheServiceProvider::class,
        Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class,
        Illuminate\Cookie\CookieServiceProvider::class,
        Illuminate\Database\DatabaseServiceProvider::class,
        Illuminate\Encryption\EncryptionServiceProvider::class,
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        Illuminate\Foundation\Providers\FoundationServiceProvider::class,
        Illuminate\Hashing\HashServiceProvider::class,
        Illuminate\Mail\MailServiceProvider::class,
        Illuminate\Notifications\NotificationServiceProvider::class,
        Illuminate\Pagination\PaginationServiceProvider::class,
        Illuminate\Pipeline\PipelineServiceProvider::class,
        Illuminate\Queue\QueueServiceProvider::class,
        Illuminate\Redis\RedisServiceProvider::class,
        Illuminate\Auth\Passwords\PasswordResetServiceProvider::class,
        Illuminate\Session\SessionServiceProvider::class,
        Illuminate\Translation\TranslationServiceProvider::class,
        Illuminate\Validation\ValidationServiceProvider::class,
        Illuminate\View\ViewServiceProvider::class,

        /*
         * Package Service Providers...
         */

        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        Maatwebsite\Excel\ExcelServiceProvider::class,


    ],

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => [

        'App' => Illuminate\Support\Facades\App::class,
        'Artisan' => Illuminate\Support\Facades\Artisan::class,
        'Auth' => Illuminate\Support\Facades\Auth::class,
        'Blade' => Illuminate\Support\Facades\Blade::class,
        'Broadcast' => Illuminate\Support\Facades\Broadcast::class,
        'Bus' => Illuminate\Support\Facades\Bus::class,
        'Cache' => Illuminate\Support\Facades\Cache::class,
        'Config' => Illuminate\Support\Facades\Config::class,
        'Cookie' => Illuminate\Support\Facades\Cookie::class,
        'Crypt' => Illuminate\Support\Facades\Crypt::class,
        'DB' => Illuminate\Support\Facades\DB::class,
        'Eloquent' => Illuminate\Database\Eloquent\Model::class,
        'Event' => Illuminate\Support\Facades\Event::class,
        'File' => Illuminate\Support\Facades\File::class,
        'Gate' => Illuminate\Support\Facades\Gate::class,
        'Hash' => Illuminate\Support\Facades\Hash::class,
        'Lang' => Illuminate\Support\Facades\Lang::class,
        'Log' => Illuminate\Support\Facades\Log::class,
        'Mail' => Illuminate\Support\Facades\Mail::class,
        'Notification' => Illuminate\Support\Facades\Notification::class,
        'Password' => Illuminate\Support\Facades\Password::class,
        'Queue' => Illuminate\Support\Facades\Queue::class,
        'Redirect' => Illuminate\Support\Facades\Redirect::class,
        'Redis' => Illuminate\Support\Facades\Redis::class,
        'Request' => Illuminate\Support\Facades\Request::class,
        'Response' => Illuminate\Support\Facades\Response::class,
        'Route' => Illuminate\Support\Facades\Route::class,
        'Schema' => Illuminate\Support\Facades\Schema::class,
        'Session' => Illuminate\Support\Facades\Session::class,
        'Storage' => Illuminate\Support\Facades\Storage::class,
        'URL' => Illuminate\Support\Facades\URL::class,
        'Validator' => Illuminate\Support\Facades\Validator::class,
        'View' => Illuminate\Support\Facades\View::class,
        'Excel' => Maatwebsite\Excel\Facades\Excel::class,
        'RedisManager' => Illuminate\Support\Facades\Redis::class,

    ],
    'atUsername' => env('AT_USERNAME', 'lawrence_n' ),
    'atKey' => env('AT_API_KEY', '' ),
    'atFrom' => env('AT_FROM', '' ),
    'at_base_uri' => env('at_base_uri'),

    'PBFPubKey' => env('RAVE_PUBLIC_KEY', 'none' ),

    'PLATINUM' => env('PLATINUM', 400000 ),
    'DIAMOND' => env('DIAMOND', 300000 ),
    'GOLD' => env('GOLD', 200000 ),
    'RUBY' => env('RUBY', 100000 ),
    'SILVER' => env('SILVER', 50000 ),
    'BRONZE' => env('BRONZE', 20000 ),
    'GEM' => env('GEM',10000 ),

    'airtelPLATINUM' => env('airtelPLATINUM', 400000 ),
    'airtelDIAMOND' => env('airtelDIAMOND', 300000 ),
    'airtelGOLD' => env('airtelGOLD', 200000 ),
    'airtelRUBY' => env('airtelRUBY', 100000 ),
    'airtelSILVER' => env('airtelSILVER', 50000 ),
    'airtelBRONZE' => env('airtelBRONZE', 20000 ),
    'airtelGEM' => env('airtelGEM',10000 ),

    'telkomPLATINUM' => env('telkomPLATINUM', 400000 ),
    'telkomDIAMOND' => env('telkomDIAMOND', 300000 ),
    'telkomGOLD' => env('telkomGOLD', 200000 ),
    'telkomRUBY' => env('telkomRUBY', 100000 ),
    'telkomSILVER' => env('telkomSILVER', 50000 ),
    'telkomBRONZE' => env('telkomBRONZE', 20000 ),
    'telkomGEM' => env('telkomGEM',10000 ),

    'twSid'    =>  env('twSid',''),
    'twAuthToken'  => env('twAuthToken',''),
    'twWhNum'  => env('twWhNum',''),

    'dialogSid'    =>  env('dialogSid',''),
    'dialogAuthToken'  => env('dialogAuthToken',''),

    'SWEAR_WORDS' => env('SWEAR_WORDS',''),

    'TALKZURI_M_C_K' => env('TALKZURI_Mpesa_Consumer_Key',''),
    'TALKZURI_M_C_S' => env('TALKZURI_Mpesa_Consumer_SecreT',''),

    'RAVE_PUBLIC_KEY' => env('RAVE_PUBLIC_KEY',''),
    'RAVE_SECRET_KEY' => env('RAVE_SECRET_KEY',''),
    'RAVE_ENCRYPTION_KEY' => env('RAVE_ENCRYPTION_KEY',''),
    'RAVE_CALLBACK_URL' => env('RAVE_CALLBACK_URL',''),
    'RAVE_LOGO_URL' => env('RAVE_LOGO_URL',''),

    'FLUTTERWAVE_PUBLIC_KEY' => env('FLUTTERWAVE_PUBLIC_KEY',''),
    'FLUTTERWAVE_SECRET_KEY' => env('FLUTTERWAVE_SECRET_KEY',''),
    'FLUTTERWAVE_ENCRYPTION_KEY' => env('FLUTTERWAVE_ENCRYPTION_KEY',''),
    'FLUTTERWAVE_CALLBACK_URL' => env('FLUTTERWAVE_CALLBACK_URL',''),

    'TALKZURI_Mbesha_k' => env('TALKZURI_Mpesa_Consumer_Key',''),
    'TALKZURI_Mbesha_s' => env('TALKZURI_Mpesa_Consumer_Secret',''),
    'TALKZURI_Mpesa_callback_URL' => env('TALKZURI_Mpesa_callback_URL',''),
    'url_for_oauth_generation' => env('url_for_oauth_generation'),
    'process_stkpush_url' => env('process_stkpush_url'),
    'mpesa_pub_key' => env('mpesa_pub_key'),

    'db' => env('AURORA_DB_DATABASE',''),
    'db_u' => env('AURORA_DB_USERNAME',''),
    'db_p' => env('AURORA_DB_PASSWORD',''),
    'db_h' => env('AURORA_DB_HOST_READ_1',''),

    'db_host' => env('DB_HOST',''),
    'db_database' => env('DB_DATABASE','localhost'),
    'db_username' => env('DB_USERNAME',''),
    'db_password' => env('DB_PASSWORD',''),
    'db_port' => env('DB_PORT',''),


    'free_trial_amount' => env('free_trial_amount'),

    'sdp_portal_username' => env('sdp_portal_username'),
    'sdp_username' => env('sdp_username'),
    'sdp_password' => env('sdp_password'),
    'sdp_cp_id' => env('sdp_cp_id'),
    'sdp_source_address' => env('sdp_source_address'),
    'sdp_base_uri' => env('sdp_base_uri'),
    'package_id' => env('package_id'),
    'sdp_response_uri' => env('sdp_response_uri'),

    'airtel_sdp_base_uri' => env('airtel_sdp_base_uri'),
    'airtel_sdp_username' => env('airtel_sdp_username'),
    'airtel_sdp_usernameT' => env('airtel_sdp_usernameT'),

    'airtel_sdp_password' => env('airtel_sdp_password'),
    'airtel_sdp_ORIGIN_ADDR' => env('airtel_sdp_ORIGIN_ADDR'),
    'airtel_sdp_response_uri' => env('airtel_sdp_response_uri'),

    'telkom_sdp_base_uri' => env('telkom_sdp_base_uri'),
    'telkom_sdp_shortcode' => env('telkom_sdp_shortcode'),
    'telkom_sdp_service_id' => env('telkom_sdp_service_id'),
    
    'safaricom_prefixes' => env('safaricom_prefixes'),
    'airtel_prefixes' => env('airtel_prefixes'),
    'telkom_prefixes' => env('telkom_prefixes'),
    'equitel_prefixes' => env('equitel_prefixes'),
    'eferio_prefixes' => env('eferio_prefixes'),
    'faiba4G_prefixes' => env('faiba4G_prefixes'),
    'semamobile_prefixes' => env('semamobile_prefixes'),
    'mobilepay_prefixes' => env('mobilepay_prefixes'),
    'homelandsmedia_prefixes' => env('homelandsmedia_prefixes'),

    'whatsapp_base_uri' => env('whatsapp_base_uri'),
    'karix_webhook' => env('karix_webhook'),
    'karix_account_uid' => env('karix_account_uid'),
    'karix_account_token' => env('karix_account_token'),
    'karix_events_url' => env('karix_events_url'),

    'token_file' => env('token_file'),

//airtime 
'airtime_encryption_key' => env('airtime_encryption_key'),
'airtime_terminal_number' => env('airtime_terminal_number'),
'airtime_transaction_key' => env('airtime_transaction_key'),
'airtime_from_ani' => env('airtime_from_ani'),
'airtime_email' => env('airtime_email'),
'airtime_base_uri' => env('airtime_base_uri'),
'WHATSAPP_VERIFY_TOKEN' => env('WHATSAPP_VERIFY_TOKEN'),


'openai_base_uri' => env('openai_base_uri'),
'openai_key' => env('openai_key'),
'openai_org' => env('openai_org'),
'openai_model' => env('openai_model'),

'sozuri_smpp_url' => env('sozuri_smpp_url'),
'sozuri_smpp_username' => env('sozuri_smpp_username'),
'sozuri_smpp_password' => env('sozuri_smpp_password'),
'sozuri_smpp_dlr_url' => env('sozuri_smpp_dlr_url'),

'TURNSATILE_SECRET' => env('TURNSATILE_SECRET')

];
